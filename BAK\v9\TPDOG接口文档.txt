===========基础数据===========
股票列表
请求频率：30次/秒

更新频率：交易日收盘后

每次调用消耗积分：1积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/stocks/list?type=sz&token=


无TOKEN
-  https://www.tpdog.com/api/hs/stocks/list?type=sz&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
type	交易所	--	sh	是	1.sh：上证
2.sz：深证
3.bj：北证
响应说明

英文属性	中文名称	类型	示例	备注
code	股票代码	String	600206	--
name	股票名称	String	有研新材	--
type	交易所	String	sh	1.sh：上证
2.sz：深证
3.bj：北证
req_code	股票代码	String	sh.600206	其它API股票代码参数，结构：'交易所类型.股票代码（指数类型.指数代码）'
响应实例

{
    "code": 1000,
    "message": "成功",
    "content": [
        {
            "code": "000001",
            "name": "平安银行",
            "type": "sz",
            "req_code": "sz.000001"
        },
        {
            "code": "000002",
            "name": "万  科Ａ",
            "type": "sz",
            "req_code": "sz.000002"
        }
    ]
}


指数列表
请求频率：30次/秒

更新频率：交易日收盘后

每次调用消耗积分：1积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/zs/list?type=zs&token=


无TOKEN
-  https://www.tpdog.com/api/zs/list?type=zs&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
type	交易所	--	zs	是	1.zs：主指
2.zssh：上证
3.zssz：深证
响应说明

英文属性	中文名称	类型	示例	备注
code	指数代码	String	zssh	--
name	指数名称	String	中证100	--
type	交易所	String	sh	1.zs：主指
2.zssh：上证
3.zssz：深证
req_code	指数代码	String	zssh.000903	其它API参数，结构：'所属交易所.指数代码'
响应实例

{
    "code": 1000,
    "message": "成功",
    "content": [
        {
            "code": "000903",
            "name": "中证100",
            "type": "zssh",
            "req_code": "zssh.000903"
        },
        {
            "code": "000904",
            "name": "中证200",
            "type": "zssh",
            "req_code": "zssh.000904"
        }
    ]
}

版块列表
请求频率：30次/秒

更新频率：交易日收盘后

每次调用消耗积分：1积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/bk/list?type=bkr&token=


无TOKEN
-  https://www.tpdog.com/api/bk/list?type=bkr&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
type	所属版块	--	bkr	是	1.bkr：地域版块
2.bkc：概念版块
3.bki：行业版块
响应说明

英文属性	中文名称	类型	示例	备注
code	板块代码	String	bkr	--
name	版块名称	String	海南板块	--
type	所属版块	String	bkr	1.bkr：地域版块
2.bkc：概念版块
3.bki：行业版块
req_code	版块代码	String	bkr.881669	其它API参数，结构：'所属版块.版块代码'
响应实例

{
    "code": 1000,
    "message": "成功",
    "content": [
        {
            "code": "881669",
            "name": "海南板块",
            "type": "bkr",
            "req_code": "bkr.881669"
        },
        {
            "code": "881670",
            "name": "内蒙古",
            "type": "bkr",
            "req_code": "bkr.881670"
        }
    ]
}
交易日查询
请求频率：30次/秒

更新频率：交易日收盘后

每次调用消耗积分：1积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/trading_day/is?date=2024-08-02&token=


无TOKEN
-  https://www.tpdog.com/api/hs/trading_day/is?date=2024-08-02&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
date	日期	当前日期	2023-03-22	否	格式：yyyy-MM-dd
响应说明

英文属性	中文名称	类型	示例	备注
is_trainding	是否为交易日	Bool	false	true:是；false：否
响应实例

{
    "code": 1000,
    "message": "成功",
    "content": {
        "is_trainding": false
    }
}

个股信息
请求频率：30次/秒

更新频率：交易日收盘后

每次调用消耗积分：1积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/stock/info?code=sz.000001&token=


无TOKEN
-  https://www.tpdog.com/api/hs/stock/info?code=sz.000001&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
code	股票代码	--	sz.000001	是	1.sh：上证
2.sz：深证
3.bj：北证
4.zs：三大主指
5.zssh：上证指数
6.zssz：深证指数
响应说明

英文属性	中文名称	类型	示例	备注
code	股票代码	String		--
name	股票名称	String		--
date	时间	String		格式：yyyy-MM-dd
type	交易所/指数类型	String		1.sh：上海；2.sz：深圳，3.zs：大盘指数
pb	市净率	Float		--
valuation	总市值	Float		元
c_valuation	流通市值	Float		元
pe_ttm	市盈率（TTM）	Float		--
pe	静态市盈率	Float		--
pe_foward	动态市盈率	Float		--
响应实例

{
    "code": 1000,
    "message": "成功",
    "content": {
        "code": "000001",
        "name": "平安银行",
        "date": "2023-07-31",
        "type": "sz",
        "pb": 0.64,
        "valuation": 239080912199.36,
        "c_valuation": 239076338424,
        "pe_ttm": 5.06,
        "pe": 5.25,
        "pe_foward": 4.09
    }
}

版块个股
请求频率：30次/秒

更新频率：交易日收盘后

每次调用消耗积分：1积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/stocks/list_board?code=bki.880158&token=


无TOKEN
-  https://www.tpdog.com/api/hs/stocks/list_board?code=bki.880158&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
code	版块代码	--	bki.880158	是	版块代码，通过”基础数据“-“版块列表“接口查询
响应说明

英文属性	中文名称	类型	示例	备注
code	股票代码	String		--
name	股票名称	String		--
type	交易所	String	sh	1.sh：上证，2.sz：深证
req_code	股票代码	String	sh.600206	其它API股票代码参数，结构：'所属交易所.股票代码'
响应实例

{
  "code": 1000,
  "message": "成功",
  "content": [
    {
      "code": "000657",
      "name": "中钨高新",
      "type": "sz",
      "req_code": "sz.000657"
    },
    {
      "code": "000795",
      "name": "英洛华",
      "type": "sz",
      "req_code": "sz.000795"
    }
}

个股F10======================
F10板块
请求频率：30次/秒

更新频率：--

每次调用消耗积分：1积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/f10/board?code=sh.600206&token=


无TOKEN
-  https://www.tpdog.com/api/hs/f10/board?code=sh.600206&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
code	股票代码	--	sh.600206	是	股票代码，通过”基础数据“-“股票列表”接口查询
响应说明

英文属性	中文名称	类型	示例	备注
code	版块代码	String	880158	--
req_type	板块类型	String	bki	--
name	板块名称	String	小金属	--
type	板块类型名称	String	行业板块	行业板块/地域板块/概念版块
响应实例

{
  "code": 1000,
  "message": "成功",
  "content": [
    {
      "code": "880158",
      "req_type": "bki",
      "name": "小金属",
      "type": "行业板块"
    },
    {
      "code": "881750",
      "req_type": "bkr",
      "name": "北京板块",
      "type": "地域板块"
    }
  ]
}

F10股本
请求频率：30次/秒

更新频率：--

每次调用消耗积分：1积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/f10/equity?code=sz.000001&token=


无TOKEN
-  https://www.tpdog.com/api/hs/f10/equity?code=sz.000001&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
code	股票代码	--	sz.000001	是	股票代码，通过”基础数据“-“股票列表”接口查询
响应说明

英文属性	中文名称	类型	示例	备注
cir_stocks	流通A股	Int		--
total_stocks	总股本	Int		--
report_date	报告日期	String		格式：yyyy-MM-dd
响应实例

{
    "code": 1000,
    "message": "成功",
    "content": {
        "cir_stocks": "19405617528",
        "total_stocks": "19405918198",
        "report_date": "2024-06-30"
    }
}
F10股东数
请求频率：30次/秒

更新频率：--

每次调用消耗积分：1积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/f10/holder_num?code=sz.301377&year=2024&sort=2&token=


无TOKEN
-  https://www.tpdog.com/api/hs/f10/holder_num?code=sz.301377&year=2024&sort=2&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
code	股票代码	--	sz.000001	是	股票代码，通过”基础数据“-“股票列表”接口查询
year	年份	当前年份	2023	否	
sort	排序	2		否	1：按时间正序；2：按时间倒序
响应说明

英文属性	中文名称	类型	示例	备注
hoder_num	股东人数	Int		--
change_ratio	较上期变化	Float		百分比
avg_num	人均流通股	Int		--
avg_change_ratio	较上期变化	Float		百分比
avg_amt	人均持股金额	Float		元
top10_ratio	十大流通股东持股比例	Float		百分比
report_date	报告日期	String		格式：yyyy-MM-dd
响应实例

{
    "code": 1000,
    "message": "成功",
    "content": [
        {
            "hoder_num": 12999,
            "change_ratio": -3.14,
            "avg_num": 5463,
            "avg_change_ratio": 3.24,
            "avg_amt": 109877.12,
            "top10_ratio": 28.03,
            "report_date": "2024-09-30"
        }
    ]
}

基金ETF==========================
ETF列表
请求频率：30次/秒

更新频率：交易日收盘后

每次调用消耗积分：1积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/etfs/list?token=


无TOKEN
-  https://www.tpdog.com/api/hs/etfs/list?token=

响应说明

英文属性	中文名称	类型	示例	备注
code	ETF代码	String	159307	--
name	ETF名称	String	博时中证红利低波100ETF	--
type	ETF	String	etf	--
req_code	ETF代码	String	etf.159307	其它API的ETF代码参数，结构：'etf.ETF代码'
响应实例

{
    "code": 1000,
    "message": "成功",
    "content": [
        {
            "code": "159307",
            "name": "博时中证红利低波100ETF",
            "type": "etf",
            "req_code": "etf.159307"
        },
        {
            "code": "159309",
            "name": "汇添富中证油气资源ETF",
            "type": "etf",
            "req_code": "etf.159309"
        }
    ]
}

ETF日K
请求频率：30次/秒

更新频率：交易日收盘后

每次调用消耗积分：1积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/etf/daily?code=etf.159150&date=2024-10-11&token=


无TOKEN
-  https://www.tpdog.com/api/hs/etf/daily?code=etf.159150&date=2024-10-11&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
date	日期	当前日期	2024-10-14	否	格式：yyyy-MM-dd
code	ETF代码	--	etf.159150	是	ETF代码，通过”基金ETF“-“ETF列表”接口查询
响应说明

英文属性	中文名称	类型	示例	备注
code	ETF代码	String	159150	--
name	ETF名称	String	易方达深证50ETF	--
date	时间	String		格式：yyyy-MM-dd
open	开盘价	Float		元
close	收盘价	Float		元
high	最高价	Float		元
low	最低价	Float		元
type	交易所	String		--
volume	成交量	Float		--
total_amt	成交额	Float		元
range_rate	振幅	Float		百分比
rise_rate	涨/跌幅	Float		百分比
rise	涨/跌额	Float		元
t_rate	换手率	Float		百分比
响应实例

{
    "code": 1000,
    "message": "成功",
    "content": {
        "code": "159150",
        "name": "易方达深证50ETF",
        "date": "2024-10-11",
        "open": 1.224,
        "close": 1.174,
        "high": 1.224,
        "low": 1.161,
        "type": "etf",
        "volume": 325383,
        "total_amt": 38571978.956,
        "range_rate": 5.17,
        "rise_rate": -3.69,
        "rise": -0.045,
        "t_rate": 9.27
    }
}

ETF实时盘口
接口说明：Lv1五档盘口

套餐限制：白银、黄金可用

请求频率：100次/分钟

更新频率：实时

开放时间：交易日09:15~11:30/13:00~15:00

每次调用消耗积分：5积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/etf/current/inventory?code=etf.563520&token=


无TOKEN
-  https://www.tpdog.com/api/hs/etf/current/inventory?code=etf.563520&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
code	ETF代码	--	etf.563520	是	ETF代码，通过”基础数据“-“ETF列表”接口查询
t	测试参数		1	否	t=1时，非指定调用时间返回样例数据
响应说明

英文属性	中文名称	类型	示例	备注
code	ETF代码	String	600206	--
name	ETF名称	String	有研新材	--
price	最新价格	Float		--
high	最高价格	Float		--
low	最低价格	Float		--
hands	成交手数	Int		--
turnover	成交金额	Float		--
yt_close	昨收价格	Float		--
limit_up	涨停价格	Float		--
limit_dn	跌停价格	Float		--
ave_price	均价	Float		--
h_outer	外盘	Int		--
h_inner	内盘	Int		--
v_rate	量比	Float		百分数
raise_rate	涨跌幅	Float		百分数
raise	涨跌额	Float		--
e_rate	委比	Float		百分数
e_dif	委差	Int		手数
t_rate	换手	Float		百分数
sell_p_5	卖五	Float		价格
sell_h_5	卖五	Int		手数
sell_p_4	卖四	Float		价格
sell_h_4	卖四	Int		手数
sell_p_3	卖三	Float		价格
sell_h_3	卖三	Int		手数
sell_p_2	卖二	Float		价格
sell_h_2	卖二	Int		手数
sell_p_1	卖一	Float		价格
sell_h_1	卖一	Int		手数
buy_p_1	买一	Float		价格
buy_h_1	买一	Int		手数
buy_p_2	买二	Float		价格
buy_h_2	买二	Int		手数
buy_p_3	买三	Float		价格
buy_h_3	买三	Int		手数
buy_p_4	买四	Float		价格
buy_h_4	买四	Int		手数
buy_p_5	买五	Float		价格
buy_h_5	买五	Int		手数
响应实例

{
    "code": 1000,
    "message": "成功",
    "content": {
        "time": "2025-03-15 13:08:13",
        "code": "563520",
        "name": "沪深300ETF永赢",
        "price": 0.996,
        "high": 0.999,
        "low": 0.976,
        "hands": 450882,
        "turnover": 44704812,
        "yt_close": 0.974,
        "limit_up": 1.071,
        "limit_dn": 0.877,
        "ave_price": 0.996,
        "h_outer": 307650,
        "h_inner": 143232,
        "v_rate": 1.04,
        "raise_rate": 2.26,
        "raise": 0.022,
        "e_rate": -59.66,
        "e_dif": -39032,
        "t_rate": 3.38,
        "sell_p_5": 1.001,
        "sell_h_5": 7420,
        "sell_p_4": 1,
        "sell_h_4": 33832,
        "sell_p_3": 0.999,
        "sell_h_3": 3692,
        "sell_p_2": 0.998,
        "sell_h_2": 2083,
        "sell_p_1": 0.997,
        "sell_h_1": 5203,
        "buy_p_1": 0.996,
        "buy_h_1": 9995,
        "buy_p_2": 0.995,
        "buy_h_2": 201,
        "buy_p_3": 0.994,
        "buy_h_3": 2500,
        "buy_p_4": 0.993,
        "buy_h_4": 1,
        "buy_p_5": 0.981,
        "buy_h_5": 501
    }
}

ETF秒级实时
套餐限制：白银、黄金可用

请求频率：100次/分钟

更新频率：3秒

开放时间：交易日09:15~11:30/13:00~15:00

每次调用消耗积分：5积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/etf/current/second?code=etf.563520&sort=2&pagesize=10&token=


无TOKEN
-  https://www.tpdog.com/api/hs/etf/current/second?code=etf.563520&sort=2&pagesize=10&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
code	ETF代码	--	etf.563520	是	ETF代码，通过”基础数据“-“ETF列表”接口查询
sort	排序	2		否	1：按时间正序；2：按时间倒序
pagesize	查询数量	--	100	否	默认查询全部数据
t	测试参数		1	否	t=1时，非指定调用时间返回样例数据
响应说明

英文属性	中文名称	类型	示例	备注
code	ETF代码	String		--
name	ETF名称	String		--
time	时间	String		格式：yyyy-MM-dd HH:mm:ss
price	实时价格	Float		--
volume	成交量	Int		--
buy_sell	主买/卖	Int		0：集合竞价；1：主卖；2：主买
响应实例

{
    "code": 1000,
    "message": "成功",
    "content": [
        {
            "code": "563520",
            "name": "沪深300ETF永赢",
            "time": "2025-01-03 10:12:55",
            "price": 0.95,
            "volume": 176,
            "buy_sell": 2
        }
    ]
}

ETF分钟实时
套餐限制：白银、黄金可用

请求频率：100次/分钟

更新频率：≤1分钟

开放时间：交易日09:30~11:30/13:00~15:00

每次调用消耗积分：5积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/etf/current/minute?code=etf.563520&sort=2&pagesize=10&token=


无TOKEN
-  https://www.tpdog.com/api/hs/etf/current/minute?code=etf.563520&sort=2&pagesize=10&token=

响应说明

英文属性	中文名称	类型	示例	备注
code	ETF代码	String		--
name	ETF名称	String		--
date	时间	String		格式：yyyy-MM-dd HH:mm
open	开盘价	Float		元
close	收盘价	Float		--
high	最高价	Float		元
low	最低价	Float		元
type	基金ETF	String	etf	--
volume	成交量	Float		--
total_amt	成交额	Float		元
range_rate	振幅	Float		百分比
rise_rate	涨/跌幅	Float		百分比
rise	涨/跌额	Float		元
t_rate	换手率	Float		百分比
响应实例

{
    "code": 1000,
    "message": "成功",
    "content": [
        {
            "code": "563520",
            "name": "沪深300ETF永赢",
            "date": "2025-01-03 10:32",
            "open": 0.953,
            "close": 0.952,
            "high": 0.953,
            "low": 0.952,
            "type": "etf",
            "volume": 5737,
            "total_amt": 546175,
            "range_rate": 0.11,
            "rise_rate": 0,
            "rise": 0,
            "t_rate": 0.04
        }
    ]
}

ETF集合竞价
套餐限制：白银、黄金可用

请求频率：100次/分钟

更新频率：实时

开放时间：交易日09:15~09:30

每次调用消耗积分：5积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/etf/current/call_auction?code=etf.563520&sort=2&pagesize=10&token=


无TOKEN
-  https://www.tpdog.com/api/hs/etf/current/call_auction?code=etf.563520&sort=2&pagesize=10&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
code	ETF代码	--	etf.563520	是	ETF代码，通过”基础数据“-“ETF列表”接口查询
sort	排序	2		否	1：按时间正序；2：按时间倒序
t	测试参数		1	否	t=1时，非指定调用时间返回样例数据
响应说明

英文属性	中文名称	类型	示例	备注
code	ETF代码	String		--
name	ETF名称	String		--
time	时间	String		格式：yyyy-MM-dd HH:mm:ss
price	实时价格	Float		--
volume	成交量	Int		--
rise_rate	涨/跌幅	Float		百分比
buy_sell	主买/卖	Int		0：集合竞价
响应实例

{
    "code": 1000,
    "message": "成功",
    "content": [
        {
            "code": "563520",
            "name": "沪深300ETF永赢",
            "time": "2025-01-03 09:25:01",
            "price": 0.95,
            "volume": 25,
            "buy_sell": 2,
            "rise_rate": 0.11
        }
    ]
}

ETF筛选
套餐限制：白银、黄金可用

请求频率：100次/分钟

更新频率：≤1分钟

开放时间：交易日09:15~11:30/13:00~15:00

每次调用消耗积分：20积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/etf/current/scans?&sort=1&field=code&filter=&token=


无TOKEN
-  https://www.tpdog.com/api/hs/etf/current/scans?&sort=1&field=code&filter=&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
sort	排序	2		否	1：按指定属性正序；2：按按指定属性倒序
field	排序属性	code		否	可排序属性：'code'，'rise_rate'，'t_rate'，'v_rate'，'income'
filter	过滤条件		(t_rate<3$v_rate<1)*rise_rate>1	否	可指定除code和name以外的属性进行简单的逻辑运算来筛选数据，运算符包括：“=”，“>”，“<”，“>=”，“<=”，“()”，“*”为并且，“$”为或者
响应说明

英文属性	中文名称	类型	示例	备注
code	ETF代码	String		--
name	ETF名称	String		--
price	实时价格	Float		元
high	最高价	Float		元
low	最低价	Float		元
open	开盘价	Float		元
yt_close	昨收价格	Float		元
rise_rate	涨/跌幅	Float		百分数
rise	涨/跌额	Float		元
range_rate	振幅	Float		百分数
volume	成交量	Float		--
total_amt	成交额	Float		元
t_rate	换手率	Float		百分数
v_rate	量比	Float		百分数
响应实例

{
    "code": 1000,
    "message": "成功",
    "content": [
        {
            "code": "159300",
            "name": "300ETF",
            "price": 4.018,
            "high": 4.044,
            "low": 4,
            "open": 4.043,
            "type": "etf",
            "yt_close": 4.044,
            "rise_rate": -0.64,
            "rise": -0.026,
            "range_rate": 1.09,
            "volume": 637041,
            "total_amt": 255676916.78,
            "t_rate": 8.11,
            "v_rate": 1.48
        }
   ]
}

全量扫描===============
股票筛选
套餐限制：白银、黄金可用

请求频率：100次/分钟

更新频率：≤1分钟

开放时间：交易日09:15~11:30/13:00~15:00

每次调用消耗积分：20积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/current/scans?zs_type=zssh&sort=2&field=v_rate&filter=&token=


无TOKEN
-  https://www.tpdog.com/api/hs/current/scans?zs_type=zssh&sort=2&field=v_rate&filter=&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
zs_type	交易所	--	sh	是	1.zssh：上海；2.zssz：深圳，3.zsbj：北京
sort	排序	2		否	1：按指定属性正序；2：按按指定属性倒序
field	排序属性	code		否	可排序属性：'code'，'pe'，'rise_rate'，'t_rate'，'v_rate'，'income'
filter	过滤条件		(t_rate<3$v_rate<1)*rise_rate>1	否	可指定除code和name以外的属性进行简单的逻辑运算来筛选数据，运算符包括：“=”，“>”，“<”，“>=”，“<=”，“()”，“*”为并且，“$”为或者
t	测试参数		1	否	t=1时，非指定调用时间返回样例数据
响应说明

英文属性	中文名称	类型	示例	备注
code	股票代码	String	600206	--
name	股票名称	String	有研新材	--
price	实时价格	Float		元
high	最高价	Float		元
low	最低价	Float		元
open	开盘价	Float		元
yt_close	昨收价格	Float		元
rise_rate	涨/跌幅	Float		百分数
rise	涨/跌额	Float		元
range_rate	振幅	Float		百分数
volume	成交量	Float		--
total_amt	成交额	Float		元
t_rate	换手率	Float		百分数
v_rate	量比	Float		百分数
income	主力流入	Float		元
pe	市盈率	Float		--
pb	市净率	Float		--
响应实例

{
    "code": 1000,
    "message": "成功",
    "content": [
        {
            "code": "603868",
            "name": "飞科电器",
            "price": 50.25,
            "high": 51.46,
            "low": 49.24,
            "open": 49.58,
            "income": 47742901,
            "pe": 21.47,
            "pb": 6.12,
            "yt_close": 48.6,
            "rise_rate": 3.4,
            "rise": 1.65,
            "range_rate": 4.57,
            "volume": 53474,
            "total_amt": 268696011,
            "t_rate": 1.23,
            "v_rate": 4.79
        }]
}

版块筛选
套餐限制：白银、黄金可用

请求频率：100次/分钟

更新频率：≤1分钟

开放时间：交易日09:15~11:30/13:00~15:00

每次调用消耗积分：20积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/current/bk_scans?bk_type=bki&sort=2&field=v_rate&filter=&token=


无TOKEN
-  https://www.tpdog.com/api/current/bk_scans?bk_type=bki&sort=2&field=v_rate&filter=&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
bk_type	版块类型	--	bkr	是	1.bkr：地域版块
2.bkc：概念版块
3.bki：行业版块
sort	排序	2		否	1：按指定属性正序；2：按按指定属性倒序
field	排序属性	code		否	可排序属性：'code'，'pe'，'rise_rate'，'t_rate'，'v_rate'，'income'
filter	过滤条件		(t_rate<3$v_rate<1)*rise_rate>1	否	可指定除code和name以外的属性进行简单的逻辑运算来筛选数据，运算符包括：“=”，“>”，“<”，“>=”，“<=”，“()”，“*”为并且，“$”为或者
t	测试参数		1	否	t=1时，非指定调用时间返回样例数据
响应说明

英文属性	中文名称	类型	示例	备注
code	版块代码	String	880154	--
name	版块名称	String	燃气	--
d_rise_rate	版块类型	String		1.bkr：地域版块
2.bkc：概念版块
3.bki：行业版块
price	实时	Float		
high	最高	Float		
low	最低	Float		
open	开盘	Float		
yt_close	昨收	Float		
rise_rate	涨/跌幅	Float		百分数
rise	涨/跌额	Float		
range_rate	振幅	Float		百分数
volume	成交量	Float		--
total_amt	成交额	Float		元
t_rate	换手率	Float		百分数
v_rate	量比	Float		百分数
income	主力流入	Float		元
pe	市盈率	Float		--
ups	上涨家数	Int		--
dns	下跌家数	Int		--
u_name	领涨股票	String		--
u_rise_rate	涨/跌幅	Float		百分比
d_name	领跌股票	String		--
d_rise_rate	涨/跌幅	Float		百分比
响应实例

{
    "code": 1000,
    "message": "成功",
    "content": [
        {
            "code": "880154",
            "name": "燃气",
            "price": 1307.28,
            "high": 1311.2,
            "low": 1295.58,
            "open": 1297.49,
            "income": -326605504,
            "pe": 12.93,
            "type": "bki",
            "yt_close": 1291.71,
            "rise_rate": 1.21,
            "rise": 15.57,
            "range_rate": 1.21,
            "volume": 6900384,
            "total_amt": 3947233083,
            "t_rate": 2.39,
            "v_rate": 2.78,
            "ups": 26,
            "dns": 6,
            "u_name": "中泰股份",
            "u_rise_rate": 3.59,
            "d_name": "大众公用",
            "d_rise_rate": -9.11
        }]
}

竞价历史
套餐限制：白银、黄金可用

接口说明：提供最近一个月9点25分集合竞价数据

请求频率：100次/分钟

更新频率：交易日9:30后更新

每次调用消耗积分：20积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/scans/auction?zs_type=zssh&sort=1&field=code&filter=&date=&token=


无TOKEN
-  https://www.tpdog.com/api/hs/scans/auction?zs_type=zssh&sort=1&field=code&filter=&date=&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
zs_type	交易所	--	sh	是	1.zssh：上海；2.zssz：深圳，3.zsbj：北京
sort	排序	2		否	1：按指定属性正序；2：按按指定属性倒序
field	排序属性	code		否	可排序属性：'code'，'pe'，'rise_rate'，'t_rate'，'v_rate'，'income'
filter	过滤条件		(t_rate<3$v_rate<1)*rise_rate>1	否	可指定除code和name以外的属性进行简单的逻辑运算来筛选数据，运算符包括：“=”，“>”，“<”，“>=”，“<=”，“()”，“*”为并且，“$”为或者
t	测试参数		1	否	t=1时，非指定调用时间返回样例数据
响应说明

英文属性	中文名称	类型	示例	备注
code	股票代码	String	600206	--
name	股票名称	String	有研新材	--
price	实时价格	Float		元
high	最高价	Float		元
low	最低价	Float		元
open	开盘价	Float		元
yt_close	昨收价格	Float		元
rise_rate	涨/跌幅	Float		百分数
rise	涨/跌额	Float		元
range_rate	振幅	Float		百分数
volume	成交量	Float		--
total_amt	成交额	Float		元
t_rate	换手率	Float		百分数
v_rate	量比	Float		百分数
income	主力流入	Float		元
pe	市盈率	Float		--
pb	市净率	Float		--
date	时间	String		格式：yyyy-MM-dd


股票资金流
套餐限制：白银、黄金可用

请求频率：100次/分钟

更新频率：≤1分钟

开放时间：交易日09:30~11:30/13:00~15:00

每次调用消耗积分：20积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/current/funds?zs_type=zssh&filter=&field=&sort=1&token=


无TOKEN
-  https://www.tpdog.com/api/hs/current/funds?zs_type=zssh&filter=&field=&sort=1&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
zs_type	交易所	--	zssh	是	1.zssh：上海；2.zssz：深圳，3.zsbj：北京
sort	排序	2		否	1：按指定属性正序；2：按按指定属性倒序
field	排序属性	code		否	可排序属性：除'type'和'name'以外的属性
filter	过滤条件		m_in_ratio>m_out_ratio	否	可指定除'code'、'name'和'type'以外的属性进行简单的逻辑运算来筛选数据，运算符包括：“=”，“>”，“<”，“>=”，“<=”，“()”，“*”为并且，“$”为或者
响应说明

英文属性	中文名称	类型	示例	备注
code	股票代码	String		--
name	股票名称	String		--
type	交易所	String		1.sh：上证
2.sz：深证
3.bj：北证
m_in	主力流入	Float		元
m_out	主力流出	Float		元
m_net	主力净流入	Float		元
r_in	散户流入	Float		元
r_out	散户流出	Float		元
r_net	散户净流入	Float		元
m_in_ratio	主力流入比例	Float		百分比
m_out_ratio	主力流出比例	Float		百分比
r_in_ratio	散户流入比例	Float		百分比
r_out_ratio	散户流出比例	Float		百分比
响应实例

{
    "code": 1000,
    "message": "成功",
    "content": [
        {
            "code": "600000",
            "name": "浦发银行",
            "type": "sh",
            "m_in": 136035186,
            "m_out": 112213261,
            "m_net": 23821925,
            "r_in": 287739639,
            "r_out": 311561564,
            "r_net": -23821925,
            "m_in_ratio": 16.05,
            "m_out_ratio": 13.24,
            "r_in_ratio": 33.95,
            "r_out_ratio": 36.76
        }
   ]
}


版块资金流
套餐限制：白银、黄金可用

请求频率：100次/分钟

更新频率：≤1分钟

开放时间：交易日09:30~11:30/13:00~15:00

每次调用消耗积分：20积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/current/bk_funds?bk_type=bkr&filter=&field=&sort=1&token=


无TOKEN
-  https://www.tpdog.com/api/hs/current/bk_funds?bk_type=bkr&filter=&field=&sort=1&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
bk_type	版块类型	--	bkr	是	1.bkr：地域版块
2.bkc：概念版块
3.bki：行业版块
sort	排序	2		否	1：按指定属性正序；2：按按指定属性倒序
field	排序属性	code		否	可排序属性：除'type'和'name'以外的属性
filter	过滤条件		m_in_ratio>m_out_ratio	否	可指定除'code'、'name'和'type'以外的属性进行简单的逻辑运算来筛选数据，运算符包括：“=”，“>”，“<”，“>=”，“<=”，“()”，“*”为并且，“$”为或者
响应说明

英文属性	中文名称	类型	示例	备注
code	板块代码	String		--
name	板块名称	String		--
type	版块类型	String		1.bkr：地域版块
2.bkc：概念版块
3.bki：行业版块
m_in	主力流入	Float		元
m_out	主力流出	Float		元
m_net	主力净流入	Float		元
r_in	散户流入	Float		元
r_out	散户流出	Float		元
r_net	散户净流入	Float		元
m_in_ratio	主力流入比例	Float		百分比
m_out_ratio	主力流出比例	Float		百分比
r_in_ratio	散户流入比例	Float		百分比
r_out_ratio	散户流出比例	Float		百分比
响应实例

{
    "code": 1000,
    "message": "成功",
    "content": [
        {
            "code": "881669",
            "name": "海南板块",
            "type": "bkr",
            "m_in": 2221342864,
            "m_out": 2455050864,
            "m_net": -233708000,
            "r_in": 4695181184,
            "r_out": 4461473168,
            "r_net": 233708016,
            "m_in_ratio": 16.06,
            "m_out_ratio": 17.75,
            "r_in_ratio": 33.94,
            "r_out_ratio": 32.25
        }
   ]
}
涨跌分布
套餐限制：白银、黄金可用

请求频率：100次/分钟

更新频率：实时

开放时间：交易日09:15~11:30/13:00~15:00

每次调用消耗积分：5积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/current/distribution?token=


无TOKEN
-  https://www.tpdog.com/api/hs/current/distribution?token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
--	--				
t	测试参数		1	否	t=1时，非指定调用时间返回样例数据
响应说明

英文属性	中文名称	类型	示例	备注
up_limit	涨停数量	Int		--
down_limit	跌停数量	Int		--
line	0%数量	Int		--
up_7	7%以上数量	Int		--
up5_7	5%~7%数量	Int		--
up3_5	3%~5%数量	Int		--
up0_3	0%~3%数量	Int		--
down_7	-7%以下数量	Int		--
down5_7	-5%~-7%数量	Int		--
down3_5	-3%~-5%数量	Int		--
down0_3	0%~-3%数量	Int		--
响应实例

{
    "code": 1000,
    "message": "成功",
    "content": {
        "up_7": 14,
        "up5_7": 29,
        "up3_5": 90,
        "up0_3": 405,
        "line": 20,
        "down0_3": 711,
        "down3_5": 649,
        "down5_7": 753,
        "down_7": 1942,
        "up_limit": 18,
        "down_limit": 356
    }
}

实时数据========

集合竞价
套餐限制：白银、黄金可用

请求频率：100次/分钟

更新频率：实时

开放时间：交易日09:15~09:30

每次调用消耗积分：5积分

接口地址
有TOKEN
-  http://www.tpdog.com/api/hs/current/call_auction?code=sh.600206&sort=2&token=


无TOKEN
-  http://www.tpdog.com/api/hs/current/call_auction?code=sh.600206&sort=2&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
code	股票代码	--	sh.600206	是	--
sort	排序	2		否	1：按时间正序；2：按时间倒序
t	测试参数		1	否	t=1时，非指定调用时间返回样例数据
响应说明

英文属性	中文名称	类型	示例	备注
code	股票代码	String		--
name	股票名称	String		--
time	时间	String		格式：yyyy-MM-dd HH:mm:ss
price	实时价格	Float		--
volume	成交量	Int		--
rise_rate	涨/跌幅	Float		百分比
buy_sell	主买/卖	Int		0：集合竞价
响应实例

{
    "code": 1000,
    "message": "成功",
    "content": [
        {
            "code": "600206",
            "name": "有研新材",
            "time": "2024-01-15 09:24:59",
            "price": 11.4,
            "volume": 38,
            "buy_sell": 0,
            "rise_rate": -0.09
        }
    ]
}
实时盘口
接口说明：Lv1五档盘口

套餐限制：白银、黄金可用

请求频率：100次/分钟

更新频率：实时

开放时间：交易日09:15~11:30/13:00~15:00

每次调用消耗积分：5积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/current/inventory?code=sh.600206&token=


无TOKEN
-  https://www.tpdog.com/api/hs/current/inventory?code=sh.600206&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
code	股票/指数/版块代码	--	sh.600206	是	股票/指数/版块代码，通过”基础数据“-（“股票列表”、“指数列表”、“版块列表“）接口查询
t	测试参数		1	否	t=1时，非指定调用时间返回样例数据
响应说明

英文属性	中文名称	类型	示例	备注
code	股票代码	String	600206	--
name	股票名称	String	有研新材	--
price	最新价格	Float		--
high	最高价格	Float		--
low	最低价格	Float		--
hands	成交手数	Int		--
turnover	成交金额	Float		--
yt_close	昨收价格	Float		--
limit_up	涨停价格	Float		--
limit_dn	跌停价格	Float		--
ave_price	均价	Float		--
h_outer	外盘	Int		--
h_inner	内盘	Int		--
v_rate	量比	Float		百分数
raise_rate	涨跌幅	Float		百分数
raise	涨跌额	Float		--
e_rate	委比	Float		百分数
e_dif	委差	Int		手数
t_rate	换手	Float		百分数
sell_p_5	卖五	Float		价格
sell_h_5	卖五	Int		手数
sell_p_4	卖四	Float		价格
sell_h_4	卖四	Int		手数
sell_p_3	卖三	Float		价格
sell_h_3	卖三	Int		手数
sell_p_2	卖二	Float		价格
sell_h_2	卖二	Int		手数
sell_p_1	卖一	Float		价格
sell_h_1	卖一	Int		手数
buy_p_1	买一	Float		价格
buy_h_1	买一	Int		手数
buy_p_2	买二	Float		价格
buy_h_2	买二	Int		手数
buy_p_3	买三	Float		价格
buy_h_3	买三	Int		手数
buy_p_4	买四	Float		价格
buy_h_4	买四	Int		手数
buy_p_5	买五	Float		价格
buy_h_5	买五	Int		手数
响应实例

{
    "code": 1000,
    "message": "成功",
    "content": {
        "time": "2024-02-22 10:01:56",
        "code": "600206",
        "name": "有研新材",
        "price": 10.2,
        "high": 10.26,
        "low": 10.1,
        "hands": 23205,
        "turnover": 23666098,
        "yt_close": 10.1,
        "limit_up": 11.11,
        "limit_dn": 9.09,
        "ave_price": 10.2,
        "h_outer": 11748,
        "h_inner": 11457,
        "v_rate": 1.1,
        "raise_rate": 0.99,
        "raise": 0.1,
        "e_rate": -21.12,
        "e_dif": -468,
        "t_rate": 0.27,
        "sell_p_5": 10.25,
        "sell_h_5": 269,
        "sell_p_4": 10.24,
        "sell_h_4": 547,
        "sell_p_3": 10.23,
        "sell_h_3": 289,
        "sell_p_2": 10.22,
        "sell_h_2": 50,
        "sell_p_1": 10.21,
        "sell_h_1": 187,
        "buy_p_1": 10.2,
        "buy_h_1": 153,
        "buy_p_2": 10.19,
        "buy_h_2": 126,
        "buy_p_3": 10.18,
        "buy_h_3": 286,
        "buy_p_4": 10.17,
        "buy_h_4": 162,
        "buy_p_5": 10.16,
        "buy_h_5": 147
    }
}

秒级实时
套餐限制：白银、黄金可用

请求频率：100次/分钟

更新频率：3秒

开放时间：交易日09:15~11:30/13:00~15:00

每次调用消耗积分：5积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/current/second?code=sz.000001&sort=2&pagesize=100&token=


无TOKEN
-  https://www.tpdog.com/api/hs/current/second?code=sz.000001&sort=2&pagesize=100&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
code	股票/指数/版块代码	--	sh.600206	是	股票/指数/版块代码，通过”基础数据“-（“股票列表”、“指数列表”、“版块列表“）接口查询
sort	排序	2		否	1：按时间正序；2：按时间倒序
pagesize	查询数量	--	100	否	默认查询全部数据
t	测试参数		1	否	t=1时，非指定调用时间返回样例数据
响应说明

英文属性	中文名称	类型	示例	备注
code	股票/指数/版块代码	String		--
name	股票/指数/版块名称	String		--
time	时间	String		格式：yyyy-MM-dd HH:mm:ss
price	实时价格	Float		--
volume	成交量	Int		--
buy_sell	主买/卖	Int		0：集合竞价；1：主卖；2：主买
响应实例

{
    "code": 1000,

    "message": "成功",
    "content": [
        {
            "code": "000001",
            "name": "平安银行",
            "time": "2023-05-25 09:22:33",
            "price": 12.02,
            "volume": 1661,
            "buy_sell": 0
        }
    ]
}
秒级盘后
套餐限制：白银、黄金可用

请求频率：200次/分钟

更新频率：盘后

开放时间：交易日18:00~次日6:00

每次调用消耗积分：5积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/current/second_today?code=sz.000001&sort=1&pagesize=10&page=&token=


无TOKEN
-  https://www.tpdog.com/api/hs/current/second_today?code=sz.000001&sort=1&pagesize=10&page=&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
code	股票代码	--	sz.000001	是	股票代码，通过”基础数据“-“股票列表”接口查询
sort	排序	2		否	1：按时间正序；2：按时间倒序
pagesize	查询数量	--	100	否	默认查询全部数据
page	页码			否	
t	测试参数		1	否	t=1时，非指定调用时间返回样例数据
响应说明

英文属性	中文名称	类型	示例	备注
code	股票代码	String		--
name	股票名称	String		--
time	时间	String		格式：yyyy-MM-dd HH:mm:ss
price	实时价格	Float		--
volume	成交量	Int		--
buy_sell	主买/卖	Int		0：集合即将；1：主卖；2：主买
响应实例

{
    "code": 1000,
    "message": "成功"
    "content": [
        {
            "code": "000001",
            "name": "平安银行",
            "time": "2023-05-25 09:22:33",
            "price": 12.02,
            "volume": 1661,
            "buy_sell": 0
        }
    ]
}
龙虎榜单========
龙虎榜
请求频率：30次/秒

更新频率：交易日收盘后

每次调用消耗积分：1积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/board/bill?date=2024-08-02&token=


无TOKEN
-  https://www.tpdog.com/api/hs/board/bill?date=2024-08-02&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
date	日期	当前日期	2023-03-22	否	格式：yyyy-MM-dd
响应说明

英文属性	中文名称	类型	示例	备注
code	股票代码	String	000034	--
date	日期	String	2023-03-22	--
name	股票名称	String	神州数码	--
explain	上榜解读	String		--
close	收盘价格	Float		元
rise_rate	涨/跌幅	Float		百分比
net_amt	龙虎榜净买入额	Float		元
buy_amt	龙虎榜买入额	Float		元
sell_amt	龙虎榜卖出额	Float		元
board_amt	龙虎榜成交额	Float		元
total_amt	总成交额	Float		元
net_buy_ratio	净买额占比	Float		百分比
t_ratio	成交额占比	Float		百分比
t_rate	换手率	Float		百分比
cm_valuation	流通市值	Float		元
reason	上榜原因	String		--
buys	买五详情	Arrays		--
 -op_name	营业部名称	String	深股通专用	--
 -buy_amt	买入金额	Float		元
 -sell_amt	卖出金额	Float		元
 -net	净额	Float		元
 -rise_rate	上涨概率	Float		百分比
 -times	上榜次数	Int		--
 -buy_ratio	买入金额占比	Float		百分比
 -sell_ratio	卖出金额占比	Float		百分比
sells	卖五详情	Arrays		--
 -op_name	营业部名称	String	深股通专用	--
 -buy_amt	买入金额	Float		元
 -sell_amt	卖出金额	Float		元
 -net	净额	Float		元
 -rise_rate	上涨概率	Float		百分比
 -times	上榜次数	Int		--
 -buy_ratio	买入金额占比	Float		百分比
 -sell_ratio	卖出金额占比	Float		百分比
响应实例

{
    "code":1000,
    "message":"成功",
    "content":[
        {
            "code": "000506",
            "date": "2023-03-22",
            "name": "中润资源",
            "explain": "西藏自治区资金卖出，成功率36.30%",
            "close": 5.5,
            "rise_rate": -7.0946,
            "net_amt": 58687794.78,
            "buy_amt": 192182411.15,
            "sell_amt": 133494616.37,
            "board_amt": 325677027.52,
            "total_amt": 1536118943,
            "net_buy_ratio": 3.820524123307,
            "t_ratio": 21.201289718097,
            "t_rate": 29.4923,
            "cm_valuation": 5107690285.5,
            "reason": "日换手率达到20%的前5只证券",
            "buys": [
                {
                    "op_name": "机构专用",
                    "buy_amt": 47088520,
                    "sell_amt": 12069579,
                    "net": 35018941,
                    "rise_rate": 39.775280898876,
                    "times": 890,
                    "buy_ratio": 3.0654214776,
                    "sell_ratio": 0.7857190392
                },
                {
                    "op_name": "机构专用",
                    "buy_amt": 47088520,
                    "sell_amt": 12069579,
                    "net": 35018941,
                    "rise_rate": 39.775280898876,
                    "times": 890,
                    "buy_ratio": 3.0654214776,
                    "sell_ratio": 0.7857190392
                },
                {
                    "op_name": "中国银河证券股份有限公司北京中关村大街证券营业部",
                    "buy_amt": 34810494,
                    "sell_amt": 24428890.27,
                    "net": 10381603.73,
                    "rise_rate": 31.5,
                    "times": 200,
                    "buy_ratio": 2.2661327209,
                    "sell_ratio": 1.5902993958
                },
                {
                    "op_name": "中国银河证券股份有限公司北京中关村大街证券营业部",
                    "buy_amt": 34810494,
                    "sell_amt": 24428890.27,
                    "net": 10381603.73,
                    "rise_rate": 31.5,
                    "times": 200,
                    "buy_ratio": 2.2661327209,
                    "sell_ratio": 1.5902993958
                },
                {
                    "op_name": "华泰证券股份有限公司台州中心大道证券营业部",
                    "buy_amt": 28768349,
                    "sell_amt": 3850,
                    "net": 28764499,
                    "rise_rate": 46.666666666667,
                    "times": 15,
                    "buy_ratio": 1.8727943646999998,
                    "sell_ratio": 0.0002506316
                },
                {
                    "op_name": "华泰证券股份有限公司台州中心大道证券营业部",
                    "buy_amt": 28768349,
                    "sell_amt": 3850,
                    "net": 28764499,
                    "rise_rate": 46.666666666667,
                    "times": 15,
                    "buy_ratio": 1.8727943646999998,
                    "sell_ratio": 0.0002506316
                },
                {
                    "op_name": "机构专用",
                    "buy_amt": 26026203,
                    "sell_amt": 14479047.12,
                    "net": 11547155.88,
                    "rise_rate": 39.775280898876,
                    "times": 890,
                    "buy_ratio": 1.6942830579,
                    "sell_ratio": 0.942573307
                },
                {
                    "op_name": "机构专用",
                    "buy_amt": 26026203,
                    "sell_amt": 14479047.12,
                    "net": 11547155.88,
                    "rise_rate": 39.775280898876,
                    "times": 890,
                    "buy_ratio": 1.6942830579,
                    "sell_ratio": 0.942573307
                },
                {
                    "op_name": "东方财富证券股份有限公司拉萨团结路第一证券营业部",
                    "buy_amt": 18529912.15,
                    "sell_amt": 29978062.88,
                    "net": -11448150.73,
                    "rise_rate": 27.763496143959,
                    "times": 389,
                    "buy_ratio": 1.2062810783,
                    "sell_ratio": 1.9515456805000002
                },
                {
                    "op_name": "东方财富证券股份有限公司拉萨团结路第一证券营业部",
                    "buy_amt": 18529912.15,
                    "sell_amt": 29978062.88,
                    "net": -11448150.73,
                    "rise_rate": 27.763496143959,
                    "times": 389,
                    "buy_ratio": 1.2062810783,
                    "sell_ratio": 1.9515456805000002
                }
            ],
            "sells": [
                {
                    "op_name": "东方财富证券股份有限公司拉萨团结路第一证券营业部",
                    "buy_amt": 18529912.15,
                    "sell_amt": 29978062.88,
                    "net": -11448150.73,
                    "rise_rate": 27.763496143959,
                    "times": 389,
                    "buy_ratio": 1.2062810783,
                    "sell_ratio": 1.9515456805000002
                },
                {
                    "op_name": "东方财富证券股份有限公司拉萨团结路第一证券营业部",
                    "buy_amt": 18529912.15,
                    "sell_amt": 29978062.88,
                    "net": -11448150.73,
                    "rise_rate": 27.763496143959,
                    "times": 389,
                    "buy_ratio": 1.2062810783,
                    "sell_ratio": 1.9515456805000002
                },
                {
                    "op_name": "中国银河证券股份有限公司北京中关村大街证券营业部",
                    "buy_amt": 34810494,
                    "sell_amt": 24428890.27,
                    "net": 10381603.73,
                    "rise_rate": 31.5,
                    "times": 200,
                    "buy_ratio": 2.2661327209,
                    "sell_ratio": 1.5902993958
                },
                {
                    "op_name": "中国银河证券股份有限公司北京中关村大街证券营业部",
                    "buy_amt": 34810494,
                    "sell_amt": 24428890.27,
                    "net": 10381603.73,
                    "rise_rate": 31.5,
                    "times": 200,
                    "buy_ratio": 2.2661327209,
                    "sell_ratio": 1.5902993958
                },
                {
                    "op_name": "东方财富证券股份有限公司拉萨金融城南环路证券营业部",
                    "buy_amt": 9647233,
                    "sell_amt": 19445628,
                    "net": -9798395,
                    "rise_rate": 21.052631578947,
                    "times": 19,
                    "buy_ratio": 0.6280264327,
                    "sell_ratio": 1.2658933795
                },
                {
                    "op_name": "东方财富证券股份有限公司拉萨金融城南环路证券营业部",
                    "buy_amt": 9647233,
                    "sell_amt": 19445628,
                    "net": -9798395,
                    "rise_rate": 21.052631578947,
                    "times": 19,
                    "buy_ratio": 0.6280264327,
                    "sell_ratio": 1.2658933795
                },
                {
                    "op_name": "东方财富证券股份有限公司拉萨团结路第二证券营业部",
                    "buy_amt": 14740006,
                    "sell_amt": 18232519,
                    "net": -3492513,
                    "rise_rate": 30.360205831904,
                    "times": 583,
                    "buy_ratio": 0.9595615019,
                    "sell_ratio": 1.1869210443
                },
                {
                    "op_name": "东方财富证券股份有限公司拉萨团结路第二证券营业部",
                    "buy_amt": 14740006,
                    "sell_amt": 18232519,
                    "net": -3492513,
                    "rise_rate": 30.360205831904,
                    "times": 583,
                    "buy_ratio": 0.9595615019,
                    "sell_ratio": 1.1869210443
                },
                {
                    "op_name": "东方财富证券股份有限公司拉萨东环路第一证券营业部",
                    "buy_amt": 12571694,
                    "sell_amt": 14857040.1,
                    "net": -2285346.1,
                    "rise_rate": 29.639175257732,
                    "times": 388,
                    "buy_ratio": 0.8184062867,
                    "sell_ratio": 0.9671803193999999
                },
                {
                    "op_name": "东方财富证券股份有限公司拉萨东环路第一证券营业部",
                    "buy_amt": 12571694,
                    "sell_amt": 14857040.1,
                    "net": -2285346.1,
                    "rise_rate": 29.639175257732,
                    "times": 388,
                    "buy_ratio": 0.8184062867,
                    "sell_ratio": 0.9671803193999999
                }
            ]
        }
    ]
}

个股龙虎榜历史
请求频率：30次/秒

每次调用消耗积分：1积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/board/bill_info?start=2024-08-02&end=2024-08-02&code=sz.000506&token=


无TOKEN
-  https://www.tpdog.com/api/hs/board/bill_info?start=2024-08-02&end=2024-08-02&code=sz.000506&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
start	起始日期	当前日期	2023-03-22	否	格式：yyyy-MM-dd
end	结束日期	当前日期	2023-03-22	否	格式：yyyy-MM-dd
code	股票代码	--	sz.000034	是	--
响应说明

英文属性	中文名称	类型	示例	备注
code	股票代码	String	000506	--
date	日期	String	2023-03-22	--
name	股票名称	String	中润资源	--
close	收盘价	Float		元
rise_rate	涨/跌幅	Float		百分比
buyer_num	买方机构数	Int		--
seller_num	卖方机构数	Int		--
buy_amt	机构买入总额	Float		元
sell_amt	机构卖出总额	Float		元
net_amt	机构买入净额	Float		元
total_amt	总成交额	Float		元
t_ratio	净买占总比	Float		百分比
t_rate	换手率	Float		百分比
reason	上榜原因	String		--
响应实例

{
    "code":1000,
    "message":"成功",
    "content":[
        {
            "code": "000506",
            "date": "2023-03-22",
            "name": "中润资源",
            "explain": "西藏自治区资金卖出，成功率36.30%",
            "close": 5.5,
            "rise_rate": -7.0946,
            "net_amt": 58687794.78,
            "buy_amt": 192182411.15,
            "sell_amt": 133494616.37,
            "board_amt": 325677027.52,
            "total_amt": 1536118943,
            "net_buy_ratio": 3.820524123307,
            "t_ratio": 21.201289718097,
            "t_rate": 29.4923,
            "cm_valuation": 5107690285.5,
            "reason": "日换手率达到20%的前5只证券",
            "buys": [
                {
                    "op_name": "机构专用",
                    "buy_amt": 47088520,
                    "sell_amt": 12069579,
                    "net": 35018941,
                    "rise_rate": 39.775280898876,
                    "times": 890,
                    "buy_ratio": 3.0654214776,
                    "sell_ratio": 0.7857190392
                },
                {
                    "op_name": "机构专用",
                    "buy_amt": 47088520,
                    "sell_amt": 12069579,
                    "net": 35018941,
                    "rise_rate": 39.775280898876,
                    "times": 890,
                    "buy_ratio": 3.0654214776,
                    "sell_ratio": 0.7857190392
                },
                {
                    "op_name": "中国银河证券股份有限公司北京中关村大街证券营业部",
                    "buy_amt": 34810494,
                    "sell_amt": 24428890.27,
                    "net": 10381603.73,
                    "rise_rate": 31.5,
                    "times": 200,
                    "buy_ratio": 2.2661327209,
                    "sell_ratio": 1.5902993958
                },
                {
                    "op_name": "中国银河证券股份有限公司北京中关村大街证券营业部",
                    "buy_amt": 34810494,
                    "sell_amt": 24428890.27,
                    "net": 10381603.73,
                    "rise_rate": 31.5,
                    "times": 200,
                    "buy_ratio": 2.2661327209,
                    "sell_ratio": 1.5902993958
                },
                {
                    "op_name": "华泰证券股份有限公司台州中心大道证券营业部",
                    "buy_amt": 28768349,
                    "sell_amt": 3850,
                    "net": 28764499,
                    "rise_rate": 46.666666666667,
                    "times": 15,
                    "buy_ratio": 1.8727943646999998,
                    "sell_ratio": 0.0002506316
                },
                {
                    "op_name": "华泰证券股份有限公司台州中心大道证券营业部",
                    "buy_amt": 28768349,
                    "sell_amt": 3850,
                    "net": 28764499,
                    "rise_rate": 46.666666666667,
                    "times": 15,
                    "buy_ratio": 1.8727943646999998,
                    "sell_ratio": 0.0002506316
                },
                {
                    "op_name": "机构专用",
                    "buy_amt": 26026203,
                    "sell_amt": 14479047.12,
                    "net": 11547155.88,
                    "rise_rate": 39.775280898876,
                    "times": 890,
                    "buy_ratio": 1.6942830579,
                    "sell_ratio": 0.942573307
                },
                {
                    "op_name": "机构专用",
                    "buy_amt": 26026203,
                    "sell_amt": 14479047.12,
                    "net": 11547155.88,
                    "rise_rate": 39.775280898876,
                    "times": 890,
                    "buy_ratio": 1.6942830579,
                    "sell_ratio": 0.942573307
                },
                {
                    "op_name": "东方财富证券股份有限公司拉萨团结路第一证券营业部",
                    "buy_amt": 18529912.15,
                    "sell_amt": 29978062.88,
                    "net": -11448150.73,
                    "rise_rate": 27.763496143959,
                    "times": 389,
                    "buy_ratio": 1.2062810783,
                    "sell_ratio": 1.9515456805000002
                },
                {
                    "op_name": "东方财富证券股份有限公司拉萨团结路第一证券营业部",
                    "buy_amt": 18529912.15,
                    "sell_amt": 29978062.88,
                    "net": -11448150.73,
                    "rise_rate": 27.763496143959,
                    "times": 389,
                    "buy_ratio": 1.2062810783,
                    "sell_ratio": 1.9515456805000002
                }
            ],
            "sells": [
                {
                    "op_name": "东方财富证券股份有限公司拉萨团结路第一证券营业部",
                    "buy_amt": 18529912.15,
                    "sell_amt": 29978062.88,
                    "net": -11448150.73,
                    "rise_rate": 27.763496143959,
                    "times": 389,
                    "buy_ratio": 1.2062810783,
                    "sell_ratio": 1.9515456805000002
                },
                {
                    "op_name": "东方财富证券股份有限公司拉萨团结路第一证券营业部",
                    "buy_amt": 18529912.15,
                    "sell_amt": 29978062.88,
                    "net": -11448150.73,
                    "rise_rate": 27.763496143959,
                    "times": 389,
                    "buy_ratio": 1.2062810783,
                    "sell_ratio": 1.9515456805000002
                },
                {
                    "op_name": "中国银河证券股份有限公司北京中关村大街证券营业部",
                    "buy_amt": 34810494,
                    "sell_amt": 24428890.27,
                    "net": 10381603.73,
                    "rise_rate": 31.5,
                    "times": 200,
                    "buy_ratio": 2.2661327209,
                    "sell_ratio": 1.5902993958
                },
                {
                    "op_name": "中国银河证券股份有限公司北京中关村大街证券营业部",
                    "buy_amt": 34810494,
                    "sell_amt": 24428890.27,
                    "net": 10381603.73,
                    "rise_rate": 31.5,
                    "times": 200,
                    "buy_ratio": 2.2661327209,
                    "sell_ratio": 1.5902993958
                },
                {
                    "op_name": "东方财富证券股份有限公司拉萨金融城南环路证券营业部",
                    "buy_amt": 9647233,
                    "sell_amt": 19445628,
                    "net": -9798395,
                    "rise_rate": 21.052631578947,
                    "times": 19,
                    "buy_ratio": 0.6280264327,
                    "sell_ratio": 1.2658933795
                },
                {
                    "op_name": "东方财富证券股份有限公司拉萨金融城南环路证券营业部",
                    "buy_amt": 9647233,
                    "sell_amt": 19445628,
                    "net": -9798395,
                    "rise_rate": 21.052631578947,
                    "times": 19,
                    "buy_ratio": 0.6280264327,
                    "sell_ratio": 1.2658933795
                },
                {
                    "op_name": "东方财富证券股份有限公司拉萨团结路第二证券营业部",
                    "buy_amt": 14740006,
                    "sell_amt": 18232519,
                    "net": -3492513,
                    "rise_rate": 30.360205831904,
                    "times": 583,
                    "buy_ratio": 0.9595615019,
                    "sell_ratio": 1.1869210443
                },
                {
                    "op_name": "东方财富证券股份有限公司拉萨团结路第二证券营业部",
                    "buy_amt": 14740006,
                    "sell_amt": 18232519,
                    "net": -3492513,
                    "rise_rate": 30.360205831904,
                    "times": 583,
                    "buy_ratio": 0.9595615019,
                    "sell_ratio": 1.1869210443
                },
                {
                    "op_name": "东方财富证券股份有限公司拉萨东环路第一证券营业部",
                    "buy_amt": 12571694,
                    "sell_amt": 14857040.1,
                    "net": -2285346.1,
                    "rise_rate": 29.639175257732,
                    "times": 388,
                    "buy_ratio": 0.8184062867,
                    "sell_ratio": 0.9671803193999999
                },
                {
                    "op_name": "东方财富证券股份有限公司拉萨东环路第一证券营业部",
                    "buy_amt": 12571694,
                    "sell_amt": 14857040.1,
                    "net": -2285346.1,
                    "rise_rate": 29.639175257732,
                    "times": 388,
                    "buy_ratio": 0.8184062867,
                    "sell_ratio": 0.9671803193999999
                }
            ]
        }
    ]
}
股池数据=====
涨停股池
请求频率：30次/秒

更新频率：交易日收盘后

每次调用消耗积分：1积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/pool/v1/limitup/list?date=2024-08-02&token=


无TOKEN
-  https://www.tpdog.com/api/hs/pool/v1/limitup/list?date=2024-08-02&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
date	日期	当前日期	2023-05-26	否	格式：yyyy-MM-dd
响应说明

英文属性	中文名称	类型	示例	备注
code	股票代码	String	001337	--
type	交易所	String		--
name	股票名称	String	四川黄金	--
date	日期	String	2023-03-21	--
udtype	类型	String	涨停	--
close	收盘价	Float		元
industry	行业板块	String	贵金属	--
rise_rate	涨幅	Float		百分比
total_amt	成交额	Float		元
cm_valuation	流通市值	Float		元
valuation	总市值	Float		元
t_rate	换手率	Float		百分比
c_times	连板数	Int		--
time	封板时间	String	09:25:00	格式：HH:mm:ss
l_time	最后封板时间	String	09:25:00	格式：HH:mm:ss
l_amount	封板资金	Float		元
f_times	炸板次数	Int	0	--
l_info	连板信息	String	13/13	涨停次数/天数
响应实例

{
    "code": 1000,
    "message": "成功",
    "content": [
        {
            "type": "sh",
            "code": "603616",
            "name": "韩建河山",
            "date": "2023-05-26",
            "udtype": "涨停",
            "close": 5.75,
            "industry": "水泥建材",
            "rise_rate": 9.942638397216797,
            "total_amt": 63408556,
            "cm_valuation": 2192866000,
            "valuation": 2192866000,
            "t_rate": 2.8915836811065674,
            "c_times": 1,
            "time": "09:25:01",
            "l_time": "09:25:01",
            "l_amount": 41845193,
            "f_times": 0,
            "l_info": "1/1",
            "speed": 0
        }
    ]
}

跌停股池
请求频率：30次/秒

更新频率：交易日收盘后

每次调用消耗积分：1积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/pool/v1/limitdown/list?date=2024-08-02&token=


无TOKEN
-  https://www.tpdog.com/api/hs/pool/v1/limitdown/list?date=2024-08-02&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
date	日期	当前日期	2023-05-26	否	格式：yyyy-MM-dd
响应说明

英文属性	中文名称	类型	示例	备注
code	股票代码	String	003010	--
type	交易所	String		--
name	股票名称	String	四川黄金	--
date	日期	String	2023-05-26	--
udtype	类型	String	跌停	--
close	收盘价	Float		元
industry	行业板块	String	贵金属	--
pe	市盈率	Double		--
rise_rate	跌幅	Float		百分比
total_amt	成交额	Float		元
cm_valuation	流通市值	Float		元
valuation	总市值	Float		元
t_rate	换手率	Float		百分比
l_time	最后封板时间	String	09:25:00	格式：HH:mm:ss
l_amount	封板资金	Float		元
l_amt	板上成交额	Float		--
o_times	开板次数	Int		--
响应实例

{
    "code": 1000,
    "message": "成功",
    "content": [
        {
            "type": "sz",
            "code": "003010",
            "name": "若羽臣",
            "date": "2023-05-26",
            "udtype": "跌停",
            "close": 22.93,
            "industry": "互联网服",
            "pe": 101.42570495605469,
            "rise_rate": -10.00784969329834,
            "total_amt": 128458444,
            "cm_valuation": 1643686420.56,
            "valuation": 2790577331.2,
            "t_rate": 7.787642955780029,
            "l_time": "09:31:39",
            "l_amount": 90012471,
            "l_amt": 400996079,
            "o_times": 0,
            "speed": 0
        }
    ]
}

强势股池
请求频率：30次/秒

更新频率：交易日收盘后

每次调用消耗积分：1积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/pool/v1/strong/list?date=2024-08-02&token=


无TOKEN
-  https://www.tpdog.com/api/hs/pool/v1/strong/list?date=2024-08-02&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
date	日期	当前日期	2023-03-21	否	格式：yyyy-MM-dd
响应说明

英文属性	中文名称	类型	示例	备注
code	股票代码	String	001337	--
type	交易所	String		--
name	股票名称	String	四川黄金	--
date	日期	String	2023-03-21	--
udtype	类型	String	强势	--
close	收盘价	Float		元
industry	行业板块	String	贵金属	--
reason	入选原因	String		--
rise_rate	涨幅	Float		百分比
total_amt	成交额	Float		元
cm_valuation	流通市值	Float		元
valuation	总市值	Float		元
t_rate	换手率	Float		百分比
l_info	连板信息	String	13/13	涨停次数/天数
speed	涨速	Float		百分比
new_high	是否新高	Int	0	1：是；0：否
v_ratio	量比	Float		--
l_price	涨停价格	Float		元
响应实例

{
    "code": 1000,
    "message": "成功",
    "content": [
         {
            "type": "sz",
             "code": "301041",
             "name": "金百泽",
             "date": "2023-05-26",
             "udtype": "强势",
             "close": 31.66,
             "industry": "电子元件",
             "reason": "60日新高且近期多次涨停",
             "rise_rate": 20.01516342163086,
             "total_amt": 60794418,
             "cm_valuation": 1945214714.88,
             "valuation": 3377488800,
             "t_rate": 3.1253321170806885,
             "l_info": "2/2",
             "speed": 0,
             "new_high": 1,
             "v_ratio": 0.4895048141479492,
             "l_price": 31.66
         }
    ]
}

炸板股池
请求频率：30次/秒

更新频率：交易日收盘后

每次调用消耗积分：1积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/pool/v1/fire/list?date=2024-08-02&token=


无TOKEN
-  https://www.tpdog.com/api/hs/pool/v1/fire/list?date=2024-08-02&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
date	日期	当前日期	2023-05-26	否	格式：yyyy-MM-dd
响应说明

英文属性	中文名称	类型	示例	备注
code	股票代码	String	001337	--
type	交易所	String		--
name	股票名称	String	四川黄金	--
date	日期	String	2023-03-21	--
udtype	类型	String	炸板	--
close	收盘价	Float		元
industry	行业板块	String	贵金属	--
rise_rate	涨/跌幅	Float		百分比
total_amt	成交额	Float		元
cm_valuation	流通市值	Float		元
valuation	总市值	Float		元
t_rate	换手率	Float		百分比
time	封板时间	String	09:25:00	格式：HH:mm:ss
f_times	炸板次数	Int		--
l_info	连板信息	String	13/13	涨停次数/天数
speed	涨速	Float		百分比
range_rate	振幅	Float		百分比
l_price	涨停价格	Float		元
响应实例

{
    "code": 1000,
    "message": "成功",
    "content": [
        {
            "type": "sh",
            "code": "600982",
            "name": "宁波能源",
            "date": "2023-05-26",
            "udtype": "炸板",
            "close": 4.86,
            "industry": "电力行业",
            "rise_rate": 3.4042551517486572,
            "total_amt": 803985264,
            "cm_valuation": 5381008975.9800005,
            "valuation": 5432353490.88,
            "t_rate": 14.423503875732422,
            "time": "09:25:02",
            "f_times": 1,
            "l_info": "1/2",
            "speed": 0,
            "range_rate": 7.021276473999023,
            "l_price": 5.17
        }
    ]
}

次新股池
请求频率：30次/秒

更新频率：交易日收盘后

每次调用消耗积分：1积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/pool/v1/secnew/list?date=2024-08-02&token=


无TOKEN
-  https://www.tpdog.com/api/hs/pool/v1/secnew/list?date=2024-08-02&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
date	日期	当前日期	2023-10-21	否	格式：yyyy-MM-dd
响应说明

英文属性	中文名称	类型	示例	备注
code	股票代码	String	001223	--
type	交易所	String		--
name	股票名称	String	欧克科技	--
date	日期	String	2023-11-20	--
udtype	类型	String	次新	--
close	收盘价	Float		元
industry	行业板块	String	专用设备	--
rise_rate	涨幅	Float	-0.391743	百分比
total_amt	成交额	Float	21794299	元
cm_valuation	流通市值	Float	4408214800	元
valuation	总市值	Float	1102714800	元
t_rate	换手率	Float	1.96283	百分比
speed	涨速	Float	0	百分比
new_high	是否新高	Int	0	1：是；0：否
o_date_num	开板天数	Int	207	
o_date	开板日期	String	2022-12-12	
ipo_date	上市日期	String	2022-12-12	
响应实例

{
    "code":1000,
    "message":"成功",
    "content":[
        {
            "type": "sz",
            "code":"001223",
            "name":"欧克科技",
            "date":"2023-10-20",
            "udtype":"次新",
            "close":66.11,
            "industry":"专用设备",
            "rise_rate":-0.391743,
            "total_amt":21794299,
            "cm_valuation":4408214800,
            "valuation":1102714800,
            "t_rate":1.96283,
            "speed":0,
            "new_high":0,
            "o_date_num":207,
            "o_date":"2022-12-12",
            "ipo_date":"2022-12-12"
        }
    ]
}

个股异动=======
异动类型
请求频率：30次/秒

每次调用消耗积分：1积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/unusual/types?token=


无TOKEN
-  https://www.tpdog.com/api/hs/unusual/types?token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
响应说明

英文属性	中文名称	类型	示例	备注
type	异动类型	Int		1：封涨停板
2：封跌停板
3：打开涨停板
4：打开跌停板
5：60日新高
6：60日新低
7：向上缺口
8：向下缺口
9：有大买盘
10：有大卖盘
11：大笔买入
12：大笔卖出
13：火箭发射
14：高台跳水
15：快速反弹
16：加速下跌
17：60日大幅上涨
18：60日大幅下跌
19：高开5日线
20：低开5日线
21：竞价上涨
22：竞价下跌
type_name	异动详情	String		--
响应实例

{
    "code": 1000,
    "message": "成功",
    "content": [
        {
            "type_name": "封涨停板",
            "type": 1
        }
    ]
}

实时异动
请求频率：30次/秒

更新频率：30秒

每次调用消耗积分：5积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/unusual/get?un_type=1&sort=2&pagesize=10&token=


无TOKEN
-  https://www.tpdog.com/api/hs/unusual/get?un_type=1&sort=2&pagesize=10&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
un_type	异动类型		1	否	默认查询全部类型，通过”个股异动“-“异动类型”接口查询
sort	排序	2		否	1：按时间正序；2：按时间倒序
pagesize	查询数量	--	100	否	默认查询全部数据
响应说明

英文属性	中文名称	类型	示例	备注
code	股票代码	String		--
name	股票名称	String		--
time	时间	String		格式：yyyy-MM-dd HH:mm:ss
volume	成交量	String		--
price	价格	Float		元
type	异动类型	Int		--
type_name	异动详情	String		--
rise_rate	涨/跌幅	Float		百分比
响应实例

{
    "code": 1000,
    "message": "成功",
    "content": [
        {
            "code": "300901",
            "name": "中胤时尚",
            "time": "2023-05-25 11:29:46",
            "price": 12.76,
            "type": 5,
            "type_name": "60日新高",
            "rise_rate": 0.142346
        },
        {
            "code": "603258",
            "name": "电魂网络",
            "time": "2023-05-25 11:29:45",
            "price": 45.26,
            "type": 4,
            "type_name": "打开跌停板",
            "rise_rate": -0.10002
        }
    ]
}

历史异动
请求频率：30次/秒

每次调用消耗积分：1积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/unusual/his?date=2024-08-02&un_type=18&token=


无TOKEN
-  https://www.tpdog.com/api/hs/unusual/his?date=2024-08-02&un_type=18&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
date	日期	当前日期	2023-03-22	否	格式：yyyy-MM-dd
un_type	异动类型		1	否	默认查询全部类型，通过”个股异动“-“异动类型”接口查询
响应说明

英文属性	中文名称	类型	示例	备注
code	股票代码	String		--
name	股票名称	String		--
time	时间	String		格式：yyyy-MM-dd HH:mm:ss
volume	成交量	String		--
price	价格	Float		元
type	异动类型	Int		--
type_name	异动详情	String		--
rise_rate	涨/跌幅	Float		百分比
响应实例

{
    "code": 1000,
    "message": "成功",
    "content": [
        {
            "code": "300901",
            "name": "中胤时尚",
            "time": "2023-05-25 11:29:46",
            "price": 12.76,
            "type": 5,
            "type_name": "60日新高",
            "rise_rate": 0.142346
        },
        {
            "code": "603258",
            "name": "电魂网络",
            "time": "2023-05-25 11:29:45",
            "price": 45.26,
            "type": 4,
            "type_name": "打开跌停板",
            "rise_rate": -0.10002
        }
    ]
}

个股异动
请求频率：30次/秒

每次调用消耗积分：1积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/unusual/stock?code=sz.000001&start=2024-02-21&end=2024-02-21&un_type=1&sort=2&pagesize=10&token=


无TOKEN
-  https://www.tpdog.com/api/hs/unusual/stock?code=sz.000001&start=2024-02-21&end=2024-02-21&un_type=1&sort=2&pagesize=10&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
code	股票代码	--	sz.000001	是	股票代码，通过”基础数据“-“股票列表”接口查询
start	日期	当前日期	2024-02-21	否	格式：yyyy-MM-dd
end	日期	当前日期	2024-02-21	否	格式：yyyy-MM-dd
un_type	异动类型	--	1	否	通过”个股异动“-“异动类型”接口查询
sort	排序	2		否	1：按时间正序；2：按时间倒序
pagesize	查询数量	--	100	否	默认查询全部数据
响应说明

英文属性	中文名称	类型	示例	备注
code	股票代码	String		--
name	股票名称	String		--
time	时间	String		格式：yyyy-MM-dd HH:mm:ss
volume	成交量	String		--
price	价格	Floag		元
type	异动类型	Int		--
type_name	异动详情	String		--
rise_rate	涨/跌幅	Float		百分比
响应实例

{
    "code": 1000,
    "message": "成功",
    "content": [
        {
            "code": "000001",
            "name": "平安银行",
            "time": "2024-02-21 10:57:15",
            "price": 10.8,
            "type": 1,
            "type_name": "封涨停板",
            "rise_rate": 9.98
        }
    ]
}

资金流向=======
个股资金流
请求频率：30次/秒

更新频率：30分钟

每次调用消耗积分：1积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/fund/stock?date=2025-03-24&period=1&code=sh.600206&token=


无TOKEN
-  https://www.tpdog.com/api/hs/fund/stock?date=2025-03-24&period=1&code=sh.600206&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
code	股票代码	--	sh.600206	是	股票代码，通过”基础数据“-“股票列表”接口查询
date	日期	当前日期	2024-05-10	否	格式：yyyy-MM-dd
period	周期数	1	1	否	统计近几个交易日，取值范围[1-10]
响应说明

英文属性	中文名称	类型	示例	备注
code	股票代码	String	600206	--
name	股票名称	String		--
type	交易所	String		1.sh：上海；2.sz：深圳
start	开始日期	String		格式：yyyy-MM-dd
end	结束日期	String		格式：yyyy-MM-dd
m_in	主力流入	Float		--
m_out	主力流出	Float		--
m_net	主力净流入	Float		--
r_in	散户流入	Float		--
r_out	散户流出	Float		--
r_net	散户净流入	Float		--
m_in_ratio	主力流入比例	Float		--
m_out_ratio	主力流出比例	Float		--
r_in_ratio	散户流入比例	Float		--
r_out_ratio	散户流出比例	Float		--
响应实例

{
    "code": 1000,
    "message": "成功",
    "content": {
        "code": "000001",
        "name": "平安银行",
        "type": "sz",
        "start": "2024-05-24",
        "end": "2024-05-24",
        "m_in": 867396192,
        "m_out": 913865472,
        "m_net": -46469280,
        "r_in": 694275776,
        "r_out": 647806493,
        "r_net": 46469283,
        "m_in_ratio": 27.77,
        "m_out_ratio": 29.26,
        "r_in_ratio": 22.23,
        "r_out_ratio": 20.74
    }
}

行业资金流
请求频率：30次/秒

更新频率：30分钟

每次调用消耗积分：1积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/fund/industry?code=bki.880115&date=2025-03-24&period=1&token=


无TOKEN
-  https://www.tpdog.com/api/hs/fund/industry?code=bki.880115&date=2025-03-24&period=1&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
code	版块代码	--	bki.880115	是	通过”基础数据“-“版块列表“接口查询
date	日期	当前日期	2024-05-10	否	格式：yyyy-MM-dd
period	周期数	1	1	否	统计近几个交易日，取值范围[1-10]
响应说明

英文属性	中文名称	类型	示例	备注
code	板块代码	String	880115	--
name	版块名称	String		--
start	开始日期	String		格式：yyyy-MM-dd
end	结束日期	String		格式：yyyy-MM-dd
m_in	主力流入	Float		--
m_out	主力流出	Float		--
m_net	主力净流入	Float		--
r_in	散户流入	Float		--
r_out	散户流出	Float		--
r_net	散户净流入	Float		--
m_in_ratio	主力流入比例	Float		--
m_out_ratio	主力流出比例	Float		--
r_in_ratio	散户流入比例	Float		--
r_out_ratio	散户流出比例	Float		--
响应实例

{
    "code": 1000,
    "message": "成功",
    "content": {
        "code": "880115",
        "name": "专业服务",
        "start": "2024-07-31",
        "end": "2024-07-31",
        "m_in": 631803240,
        "m_out": 623879267,
        "m_net": 7923973,
        "r_in": 2272040720,
        "r_out": 2280089280,
        "r_net": -8048560,
        "m_in_ratio": 10.88,
        "m_out_ratio": 10.74,
        "r_in_ratio": 39.12,
        "r_out_ratio": 39.26
    }
}

概念资金流
请求频率：30次/秒

更新频率：30分钟

每次调用消耗积分：1积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/fund/concept?code=bkc.880664&date=2025-03-24&period=1&token=


无TOKEN
-  https://www.tpdog.com/api/hs/fund/concept?code=bkc.880664&date=2025-03-24&period=1&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
code	版块代码	--	bkc.880664	是	通过”基础数据“-“版块列表“接口查询
date	日期	当前日期	2024-05-10	否	格式：yyyy-MM-dd
period	周期数	1	1	否	统计近几个交易日，取值范围[1-10]
响应说明

英文属性	中文名称	类型	示例	备注
code	板块代码	String	880664	--
name	板块名称	String		--
start	开始日期	String		格式：yyyy-MM-dd
end	结束日期	String		格式：yyyy-MM-dd
m_in	主力流入	Float		--
m_out	主力流出	Float		--
m_net	主力净流入	Float		--
r_in	散户流入	Float		--
r_out	散户流出	Float		--
r_net	散户净流入	Float		--
m_in_ratio	主力流入比例	Float		--
m_out_ratio	主力流出比例	Float		--
r_in_ratio	散户流入比例	Float		--
r_out_ratio	散户流出比例	Float		--
响应实例

{
    "code": 1000,
    "message": "成功",
    "content": {
        "code": "880664",
        "name": "净水概念",
        "start": "2024-07-31",
        "end": "2024-07-31",
        "m_in": 2900446096,
        "m_out": 2719837776,
        "m_net": 180608320,
        "r_in": 4759733152,
        "r_out": 4940341488,
        "r_net": -180608336,
        "m_in_ratio": 18.93,
        "m_out_ratio": 17.75,
        "r_in_ratio": 31.07,
        "r_out_ratio": 32.25
    }
}

地域资金流
请求频率：30次/秒

更新频率：30分钟

每次调用消耗积分：1积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/fund/area?code=bkr.881766&date=2025-03-24&period=1&token=


无TOKEN
-  https://www.tpdog.com/api/hs/fund/area?code=bkr.881766&date=2025-03-24&period=1&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
code	版块代码	--	bkr.881766	是	通过”基础数据“-“版块列表“接口查询
date	日期	当前日期	2024-05-10	否	格式：yyyy-MM-dd
period	周期数	1	1	否	统计近几个交易日，取值范围[1-10]
响应说明

英文属性	中文名称	类型	示例	备注
code	板块代码	String	881766	--
name	版块名称	String		--
start	开始日期	String		格式：yyyy-MM-dd
end	结束日期	String		格式：yyyy-MM-dd
m_in	主力流入	Float		--
m_out	主力流出	Float		--
m_net	主力净流入	Float		--
r_in	散户流入	Float		--
r_out	散户流出	Float		--
r_net	散户净流入	Float		--
m_in_ratio	主力流入比例	Float		--
m_out_ratio	主力流出比例	Float		--
r_in_ratio	散户流入比例	Float		--
r_out_ratio	散户流出比例	Float		--
响应实例

{
    "code": 1000,
    "message": "成功",
    "content": {
        "code": "881766",
        "name": "上海板块",
        "start": "2024-07-31",
        "end": "2024-07-31",
        "m_in": 28001612544,
        "m_out": 27856824832,
        "m_net": 144787712,
        "r_in": 59134183680,
        "r_out": 59261248512,
        "r_net": -127064832,
        "m_in_ratio": 16.07,
        "m_out_ratio": 15.99,
        "r_in_ratio": 33.94,
        "r_out_ratio": 34.01
    }
}

市场情绪监控
请求频率：30次/秒

更新频率：交易日收盘后

每次调用消耗积分：1积分

接口地址
有TOKEN
-  https://www.tpdog.com/api/hs/emotion/get?date=2024-08-02&token=


无TOKEN
-  https://www.tpdog.com/api/hs/emotion/get?date=2024-08-02&token=

参数说明

英文属性	中文名称	默认值	示例	是否必须	描述
date	日期	当前日期	2023-03-22	否	格式：yyyy-MM-dd
响应说明

英文属性	中文名称	类型	示例	备注
date	日期	String		格式：yyyy-MM-dd
up_num	沪深上涨数	Int		--
down_num	沪深下跌数	Int		--
line_num	沪深持平数	Int		--
l_up_num	涨停数量	Int		--
f_num	炸板数量	Int		--
c_num	连板数量	Int		--
c_snum	2板数量	Int		--
c_tnum	3板数量	Int		--
c_more_num	3板以上数量	Int		--
c_ts	3板股票	Array		--
 -code	股票代码	String		--
 -name	股票名称	String		--
 -open	股票代码	开盘价		--
 -close	收盘价	Float		--
 -high	最高价	Float		--
 -low	最低价	Float		--
 -type	交易所	String	sh	1.sh：上证，2.sz：深证
c_ms	3板以上股票	Int		--
 -code	股票代码	String		--
 -name	股票名称	String		--
 -open	股票代码	开盘价		--
 -close	收盘价	Float		--
 -high	最高价	Float		--
 -low	最低价	Float		--
 -type	交易所	String	sh	1.sh：上证，2.sz：深证
c_trate	3板比率	Float		百分数
c_mrate	3板以上比率	Float		百分数
响应实例

{
    "code": 1000,
    "message": "成功",
    "content": {
        "date": "2023-06-01",
        "up_num": 2602,
        "down_num": 2250,
        "line_num": 206,
        "l_up_num": 43,
        "l_down_num": 6,
        "f_num": 17,
        "c_num": 11,
        "c_snum": 6,
        "c_tnum": 3,
        "c_more_num": 2,
        "c_ts": [
            {
                "code": "600560",
                "name": "金自天正",
                "open": 6.2,
                "close": 6.17,
                "high": 6.47,
                "low": 6.06,
                "type": "sh"
            },
            {
                "code": "601900",
                "name": "南方传媒",
                "open": 5.76,
                "close": 7.23,
                "high": 7.23,
                "low": 5.76,
                "type": "sh"
            },
            {
                "code": "605118",
                "name": "力鼎光电",
                "open": 10.87,
                "close": 13.09,
                "high": 13.09,
                "low": 10.87,
                "type": "sh"
            }
        ],
        "c_ms": [
            {
                "code": "002173",
                "name": "创新医疗",
                "open": 10.85,
                "close": 12.38,
                "high": 12.38,
                "low": 9.85,
                "type": "sz"
            },
            {
                "code": "603869",
                "name": "新智认知",
                "open": 4.34,
                "close": 4.34,
                "high": 4.34,
                "low": 4.34,
                "type": "sh"
            }
        ],
        "c_trate": 0,
        "c_mrate": 0
    }
}


基础说明=====
基础响应
响应说明

英文属性	中文名称	类型	示例	备注
code	响应编码	number	1000	参考：响应编码
message	响应信息	string	成功	参考：响应编码
content	响应内容	object	--	--
响应实例

示例1：

{

    "code": 1000,
    "message": "成功",
    "content": {...}
}

示例2：

{
    "code": 1000,
    "message": "成功",
    "content": [{...},{...}]
}
响应编码

CODE	MESSAGE
1000	成功
1001	未查到相关数据
2001	无法查询此接口数据，请注册获取积分
2002	用户等级只能查询5个周期前的数据
2003	接口限流，稍后再试
2004	无法查询此接口数据，请升级套餐
3001	TOKEN错误
3002	用户已锁定，请联系我
3003	IP不在白名单内
3004	积分不足
3005	无TOKEN，每个接口每天限请求3次，部分接口无法获取最新数据
3006	今日1000次试用次数已用完，请注册获取TOKEN
5001	起始日期大于终止日期
5002	日期区间不能超过1个月
5003	日期区间不能超过12个月
5004	日期区间不能超过3年
5005	日期区间不能超过5年
5006	日期不能超过当前日期
5007	日期不能超过当前日期
5008	龙虎榜未更新
5011	股池数据未更新
5012	情绪监控数据未更新
5013	当日数据未更新
5014	日期区间不能超过10天
5015	日期不能小于2000-01-01
5016	起始日期不能小于2000-01-01
5017	交易日9:15至9:30间调用，样例数据请加参数t=1
5018	调用时间：交易日9:15至11:30、13:00至15:00间，样例数据请加参数t=1
5019	调用时间：交易日9:30至11:30、13:00至15:00间，样例数据请加参数t=1
5021	条件表达式错误，属性错误或符号错误等原因
5022	游资榜未更新
5023	资金流数据未更新
5024	数据整理中，请尝试其它交易日
5025	只提供一年内数据
6001	系统错误，请联系管理员
6003	暂无数据
9001	未知问题，请联系客服
9002	网站首页：https://www.tpdog.com
9003	不支持请求
9004	缺少参数对象
9005	参数错误
9006	非交易日，交易时间
9007	type取值范围[1-22]，多个用逗号分割







