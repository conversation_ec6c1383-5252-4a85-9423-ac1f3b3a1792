一、快速开始
1. 安装sdk
# 首次安装
pip install adata
# 指定镜像源
pip install adata -i http://mirrors.aliyun.com/pypi/simple/

# 升级版本
pip install -U adata
# 指定镜像源
pip install -U adata -i http://mirrors.aliyun.com/pypi/simple/
Copied!
注：国内镜像可能存在同步延迟，可使用官方镜像源，以下是镜像源

阿里云【推荐】：http://mirrors.aliyun.com/pypi/simple/

清华大学：https://pypi.tuna.tsinghua.edu.cn/simple

官方镜像源：https://pypi.org/simple

2. 使用示例
2.1 获取股票代码
获取所有的股票代码

import adata

res_df = adata.stock.info.all_code()
print(res_df)
Copied!
示例结果：

  stock_code short_name exchange
0        001324       N长青科       SZ
1        301361       众智科技       SZ
2        300514        友讯达       SZ
3        300880       迦南智能       SZ
4        301368       丰立智能       SZ
...         ...        ...      ...
5488     300325        德威退       SZ
5489     300362        天翔退       SZ
5490     300367        网力退       SZ
5491     300372        欣泰退       SZ
5492     300431        暴风退       SZ

[5493 rows x 3 columns]
Copied!
2.2 获取股票的行情
获取到股票代码后，传入对应的stock_code参数，查询对应股票的行情信息。

import adata

# k_type: k线类型：1.日；2.周；3.月 默认：1 日k
res_df = adata.stock.market.get_market(stock_code='000001', k_type=1, start_date='2021-01-01')
print(res_df)
Copied!
示例结果：

            trade_time   open  close  ... pre_close stock_code  trade_date
0    2021-01-04 00:00:00  18.69  18.19  ...     18.93     000001  2021-01-04
1    2021-01-05 00:00:00  17.99  17.76  ...     18.19     000001  2021-01-05
2    2021-01-06 00:00:00  17.67  19.15  ...     17.76     000001  2021-01-06
3    2021-01-07 00:00:00  19.11  19.49  ...     19.15     000001  2021-01-07
4    2021-01-08 00:00:00  19.49  19.44  ...     19.49     000001  2021-01-08
..                   ...    ...    ...  ...       ...        ...         ...
571  2023-05-16 00:00:00  12.80  12.62  ...     12.83     000001  2023-05-16
572  2023-05-17 00:00:00  12.58  12.49  ...     12.62     000001  2023-05-17
573  2023-05-18 00:00:00  12.57  12.49  ...     12.49     000001  2023-05-18
574  2023-05-19 00:00:00  12.43  12.34  ...     12.49     000001  2023-05-19
575  2023-05-22 00:00:00  12.31  12.38  ...     12.34     000001  2023-05-22

[576 rows :x 13 columns]
Copied!
3. 其它数据使用
请参考下面数据列表和相关字典文档，找到对应的函数并查看对应的函数注释，进行正确使用。

数据列表
数据字典
4. 代理设置
项目是基于公开接口，可能存在限制等，因此增加代理设置功能

import adata

# 设置代理,代理是全局设置,代理失效后可从新设置。
adata.proxy(is_proxy=True, ip='************:1133')
res_df = adata.stock.info.all_code()
print(res_df)
Copied!
注：

proxy_url: 获取代理Ip的链接；ip和proxy_url方式选择其一；
每次请求获取一次，为节省ip资源建议使用自建的代理池。

数据列表-LIST
整理了项目所有的数据列表和相关使用API，方面快速浏览所有的数据。

详细内容和相关使用参数，请参考数据字典文档。

（1）股票-Stock
1. 基本信息
数据	API	说明	备注
A股代码	stock.info.all_code()	所有A股代码信息	
股本信息	stock.info.get_stock_shares()	获取单只股票的股本信息	来源：东方财富
申万一二级行业	stock.info.get_industry_sw()	获取单只股票的申万一二级行业	来源：百度
概念			
来源：同花顺			
概念代码	stock.info.all_concept_code_ths()	所有A股概念代码信息（同花顺）	来源：同花顺公开数据
概念成分列表	stock.info.concept_constituent_ths()	获取同花顺概念指数的成分股（同花顺）	注意：返回结果只有股票代码和股票简称，可根据概念名称查询
股票所属概念	stock.info.get_concept_ths()	获取单只股票所属的概念板块	F10
来源：东方财富			
概念代码	stock.info.all_concept_code_east()	所有A股概念代码信息（东方财富）	来源：东方财富
概念成分列表	stock.info.concept_constituent_east()	获取同花顺概念指数的成分股（东方财富）	注意：返回结果只有股票代码和股票简称，可根据概念名称查询
股票所属概念	stock.info.get_concept_east()	获取单只股票所属的概念板块	核心题材
股票所属板块	stock.info.get_plate_east()	获取单只股票所属的板块	1. 行业 2. 地域板块 3.概念，综合的概念接口
指数			
指数代码	stock.info.all_index_code()	获取所有A股市场的指数代码	来源同花顺，可能存在同花顺对代码重新编码的情况
指数对应的成分股	stock.info.index_constituent()	获取对应指数的成分股列表	
其它			
股票交易日历	stock.info.trade_calendar()	获取股票交易日信息	来源：深交所
2. 行情信息
数据	API	说明	备注
分红信息	stock.market.get_dividend()	获取单只股票的分红信息	
股票行情	stock.market.get_market()	获取单只股票的行情信息-日、周、月 k线	
stock.market.get_market_min()	获取单个股票的今日分时行情	只能获取当天
实时行情	stock.market.list_market_current()	获取多个股票最新行情信息	实时行情
数据源：2个，新浪和腾讯
stock.market.get_market_five()	获取单个股票的5档行情信息	实时行情
数据源：2个，腾讯和百度
stock.market.get_market_bar()	获取单个股票的分笔成交行情	实时行情
股市通
概念行情-同花顺	stock.market.get_market_concept_ths()	获取单个概念的行情信息-日、周、月 k线	获取同花顺概念行情时，
请注意传入参数是指数代码还是概念代码，
指数代码8开头，index_code
stock.market.get_market_concept_min_ths()	获取同花顺概念行情-当日分时	只能获取当天
stock.market.get_market_concept_current_ths()	获取同花顺当前的概念行情	实时行情
概念行情-东方财富	stock.market.get_market_concept_east()	获取单个概念的行情信息-日、周、月 k线	获取东方财富概念行情时，
指数代码BK开头，index_code
stock.market.get_market_concept_min_east()	获取同花顺概念行情-当日分时	只能获取当天
stock.market.get_market_concept_current_east()	获取同花顺当前的概念行情	实时行情
指数行情	stock.market.get_market_index()	获取指数的行情信息-日、周、月 k线	
stock.market.get_market_index_min()	获取指数的行情-当日分时	
stock.market.get_market_index_current()	获取当前的指数行情	实时行情
个股资金流	stock.market.get_capital_flow_min()	获取单个股票的今日分时资金流向	最新实时数据
stock.market.get_capital_flow()	获取单个股票的资金流向	历史日度数据
概念资金流	stock.market.all_capital_flow_east()	获取所有东财概念近N日资金流向	获取近1,5,10日资金流向
数据源：东方财富
注：概念和指数从本质来看是一样的，所以相关的接口和返回结果是一致的，概念是各个厂商自定义的指数，指数是官方或者权威机构定义的，都是一揽子股票的组合。

3. 财务数据
数据	API	说明	备注
核心财务数据	stock.finance.get_core_index()	获取单只股票的核心财务数据	来源：东方财富
三大报表详细数据，暂时不提供
（2）基金-ETF
1. 基本信息
数据	API	说明	备注
ETF（场内）	fund.info.all_etf_exchange_traded_info()	获取所有A股市场的ETF信息	来源：1. 东方财富
2. 行情信息
数据	API	说明	备注
ETF行情	fund.market.get_market_etf()	获取ETF的行情信息-日、周、月 k线	来源：同花顺
fund.market.get_market_etf_min()	获取ETF的行情-当日分时	
fund.market.get_market_etf_current()	获取当前的ETF行情	实时行情
（3）债券-Bond
数据	API	说明	备注
可转债代码	bond.info.all_convert_code()	获取所有A股市场的可转换债券代码信息	来源：1. 同花顺
可转债行情	bond.market.list_market_current()	获取A股市场的可转换债券最新行情	来源：新浪
其它数据排期中	TODO	若您有相关资源可以一起参与贡献	
（4）舆情
数据	API	说明	备注
最近一个月的股票解禁列表	sentiment.stock_lifting_last_month()	查询最近一个月的股票解禁列表	来源：1. 同花顺
全市场融资融券余额列表	sentiment.securities_margin()	查询全市场融资融券余额列表	来源：1. 东方财富
北向资金-行情			
sentiment.north.north_flow_current()	获取北向资金（沪深港通）当前流入资金的行情	来源：1.东方财富
sentiment.north.north_flow_min()	获取北向资金分时行情	
sentiment.north.north_flow()	获取北向资金历史流入行情	
热度榜单	sentiment.hot.pop_rank_100_east	东方财富人气100榜单	来源：东方财富
sentiment.hot.hot_rank_100_ths()	同花顺热度100排行榜	来源：同花顺
sentiment.hot.hot_concept_20_ths()	同花顺热门概念板块20排行榜	来源：同花顺
sentiment.hot.list_a_list_daily()	龙虎榜单列表	来源：东方财富
sentiment.get_a_list_info()	单只股票龙虎榜信息详情	来源：东方财富
扫雷	sentiment.mine.mine_clearance_tdx()	单只股票的扫雷避险信息	来源：通达信
其它数据排期中	TODO	若您有相关资源可以一起参与贡献	
数据列表-LIST
整理了项目所有的数据列表和相关使用API，方面快速浏览所有的数据。

详细内容和相关使用参数，请参考数据字典文档。

（1）股票-Stock
1. 基本信息
数据	API	说明	备注
A股代码	stock.info.all_code()	所有A股代码信息	
股本信息	stock.info.get_stock_shares()	获取单只股票的股本信息	来源：东方财富
申万一二级行业	stock.info.get_industry_sw()	获取单只股票的申万一二级行业	来源：百度
概念			
来源：同花顺			
概念代码	stock.info.all_concept_code_ths()	所有A股概念代码信息（同花顺）	来源：同花顺公开数据
概念成分列表	stock.info.concept_constituent_ths()	获取同花顺概念指数的成分股（同花顺）	注意：返回结果只有股票代码和股票简称，可根据概念名称查询
股票所属概念	stock.info.get_concept_ths()	获取单只股票所属的概念板块	F10
来源：东方财富			
概念代码	stock.info.all_concept_code_east()	所有A股概念代码信息（东方财富）	来源：东方财富
概念成分列表	stock.info.concept_constituent_east()	获取同花顺概念指数的成分股（东方财富）	注意：返回结果只有股票代码和股票简称，可根据概念名称查询
股票所属概念	stock.info.get_concept_east()	获取单只股票所属的概念板块	核心题材
股票所属板块	stock.info.get_plate_east()	获取单只股票所属的板块	1. 行业 2. 地域板块 3.概念，综合的概念接口
指数			
指数代码	stock.info.all_index_code()	获取所有A股市场的指数代码	来源同花顺，可能存在同花顺对代码重新编码的情况
指数对应的成分股	stock.info.index_constituent()	获取对应指数的成分股列表	
其它			
股票交易日历	stock.info.trade_calendar()	获取股票交易日信息	来源：深交所
2. 行情信息
数据	API	说明	备注
分红信息	stock.market.get_dividend()	获取单只股票的分红信息	
股票行情	stock.market.get_market()	获取单只股票的行情信息-日、周、月 k线	
stock.market.get_market_min()	获取单个股票的今日分时行情	只能获取当天
实时行情	stock.market.list_market_current()	获取多个股票最新行情信息	实时行情
数据源：2个，新浪和腾讯
stock.market.get_market_five()	获取单个股票的5档行情信息	实时行情
数据源：2个，腾讯和百度
stock.market.get_market_bar()	获取单个股票的分笔成交行情	实时行情
股市通
概念行情-同花顺	stock.market.get_market_concept_ths()	获取单个概念的行情信息-日、周、月 k线	获取同花顺概念行情时，
请注意传入参数是指数代码还是概念代码，
指数代码8开头，index_code
stock.market.get_market_concept_min_ths()	获取同花顺概念行情-当日分时	只能获取当天
stock.market.get_market_concept_current_ths()	获取同花顺当前的概念行情	实时行情
概念行情-东方财富	stock.market.get_market_concept_east()	获取单个概念的行情信息-日、周、月 k线	获取东方财富概念行情时，
指数代码BK开头，index_code
stock.market.get_market_concept_min_east()	获取同花顺概念行情-当日分时	只能获取当天
stock.market.get_market_concept_current_east()	获取同花顺当前的概念行情	实时行情
指数行情	stock.market.get_market_index()	获取指数的行情信息-日、周、月 k线	
stock.market.get_market_index_min()	获取指数的行情-当日分时	
stock.market.get_market_index_current()	获取当前的指数行情	实时行情
个股资金流	stock.market.get_capital_flow_min()	获取单个股票的今日分时资金流向	最新实时数据
stock.market.get_capital_flow()	获取单个股票的资金流向	历史日度数据
概念资金流	stock.market.all_capital_flow_east()	获取所有东财概念近N日资金流向	获取近1,5,10日资金流向
数据源：东方财富
注：概念和指数从本质来看是一样的，所以相关的接口和返回结果是一致的，概念是各个厂商自定义的指数，指数是官方或者权威机构定义的，都是一揽子股票的组合。

3. 财务数据
数据	API	说明	备注
核心财务数据	stock.finance.get_core_index()	获取单只股票的核心财务数据	来源：东方财富
三大报表详细数据，暂时不提供
（2）基金-ETF
1. 基本信息
数据	API	说明	备注
ETF（场内）	fund.info.all_etf_exchange_traded_info()	获取所有A股市场的ETF信息	来源：1. 东方财富
2. 行情信息
数据	API	说明	备注
ETF行情	fund.market.get_market_etf()	获取ETF的行情信息-日、周、月 k线	来源：同花顺
fund.market.get_market_etf_min()	获取ETF的行情-当日分时	
fund.market.get_market_etf_current()	获取当前的ETF行情	实时行情
（3）债券-Bond
数据	API	说明	备注
可转债代码	bond.info.all_convert_code()	获取所有A股市场的可转换债券代码信息	来源：1. 同花顺
可转债行情	bond.market.list_market_current()	获取A股市场的可转换债券最新行情	来源：新浪
其它数据排期中	TODO	若您有相关资源可以一起参与贡献	
（4）舆情
数据	API	说明	备注
最近一个月的股票解禁列表	sentiment.stock_lifting_last_month()	查询最近一个月的股票解禁列表	来源：1. 同花顺
全市场融资融券余额列表	sentiment.securities_margin()	查询全市场融资融券余额列表	来源：1. 东方财富
北向资金-行情			
sentiment.north.north_flow_current()	获取北向资金（沪深港通）当前流入资金的行情	来源：1.东方财富
sentiment.north.north_flow_min()	获取北向资金分时行情	
sentiment.north.north_flow()	获取北向资金历史流入行情	
热度榜单	sentiment.hot.pop_rank_100_east	东方财富人气100榜单	来源：东方财富
sentiment.hot.hot_rank_100_ths()	同花顺热度100排行榜	来源：同花顺
sentiment.hot.hot_concept_20_ths()	同花顺热门概念板块20排行榜	来源：同花顺
sentiment.hot.list_a_list_daily()	龙虎榜单列表	来源：东方财富
sentiment.get_a_list_info()	单只股票龙虎榜信息详情	来源：东方财富
扫雷	sentiment.mine.mine_clearance_tdx()	单只股票的扫雷避险信息	来源：通达信
其它数据排期中	TODO	若您有相关资源可以一起参与贡献	

STOCK
整个项目API的数据字典，按照模块层级分类命名，描述尽量精简，以便进行查阅。

股票-STOCK-INFO
------------
股票相关
1. 股票代码信息
说明介绍

获取A股所有股票代码信息列表

调用方法

stock.info.all_code()
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
输入参数

无

返回结果

字段	类型	注释	说明
stock_code	string	代码	600001
short_name	string	简称	中国平安
exchange	string	交易所	SH:上交；BJ：北交；SZ：深交
list_date	date	上市日期	1998-06-24；部分退市的股票存在无法获取，属于正常情况
参考示例

import adata
df = adata.stock.info.all_code()
print(df)
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
# 结果示例
     stock_code short_name exchange   list_date
0        000001       平安银行       SZ  1991-04-03
1        000002      万  科Ａ       SZ  1991-01-29
2        000003      PT金田A       SZ         NaN
...         ...        ...      ...         ...
5637     900955       退市海B       SH         NaN
5638     900956       东贝B股       SH         NaN
5639     900957       凌云Ｂ股       SH  2000-07-28

[5640 rows x 4 columns]
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
2. 股票股本信息
说明介绍

获取单个股票的股本信息

用作市值计算，最新市值=最新股本*最新股价

调用方法

stock.info.get_stock_shares()
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
输入参数

参数	类型	是否必填	说明
stock_code	string	是	股票代码；例：600001
is_history	bool	否	是否获取所有历史；默认是
返回结果

字段	类型	注释	说明
stock_code	string	代码	600001
change_date	date	变动时间	2023-07-17
total_shares	int	总股本：股	119534983
limit_shares	int	限售股本：股	119534983
list_a_shares	int	流通A股股本：股	119534983
change_reason	string	变动原因	首发限售股份上市
参考示例

import adata
df = adata.stock.info.get_stock_shares(stock_code='600001', is_history=True)
print(df)
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
# 结果示例
   stock_code change_date  ...  list_a_shares  change_reason
0      600001  2009-04-30  ...     2816456569      股改限售流通股上市
1      600001  2008-04-30  ...     2608760287      股改限售流通股上市
		...         ...       ...          ...    ...
18     600001  2006-03-08  ...      922283347          债转股上市
19     600001  2006-03-07  ...      904626208          债转股上市

[20 rows x 6 columns]

Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
3. 股票申万一二级行业信息
说明介绍

获取单个股票的申万一二级行业信息

调用方法

stock.info.get_industry_sw()
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
输入参数

参数	类型	是否必填	说明
stock_code	string	是	股票代码；例：300033
返回结果

字段	类型	注释	说明
stock_code	string	代码	300033
sw_code	string	申万行业代码	710000
industry_name	string	行业名称	计算机
industry_type	string	行业类别	申万一级
source	string	来源	百度股市通
参考示例

import adata
df = adata.stock.info.get_industry_sw(stock_code='300033')
print(df)
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
# 结果示例
  stock_code sw_code industry_name industry_type source
0     300033  710000           计算机          申万一级  百度股市通
1     300033  710400          软件开发          申万二级  百度股市通
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
------THS------
概念相关-同花顺
同花顺概念有相关限制，建议使用东方财富或者百度的；

后续停止维护同花顺的相关数据。

21. 概念指数信息-ths
说明介绍

获取同花顺的概念代码信息列表

调用方法

stock.info.all_concept_code_ths()
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
输入参数

无

返回结果

字段	类型	注释	说明
name	string	名称	物联网
index_code	string	指数代码	同花顺的概念指数代码是：8开头；例：885312
concept_code	string	概念代码	同花顺的概念代码是：3开头；例：309061；注意不要混淆
source	string	来源	同花顺
参考示例

import adata
df = adata.stock.info.all_concept_code_ths()
print(df)
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
# 结果示例
    index_code      name concept_code source
0       886050      算力租赁       309068    同花顺
1       886049      空间计算       309066    同花顺
2       886048     英伟达概念       309065    同花顺
3       886047      脑机接口       308535    同花顺
4       886046  MR（混合现实）       309063    同花顺
..         ...       ...          ...    ...
390     885311      智能电网       300037    同花顺
391     885284      稀缺资源       300777    同花顺
392        NaN  国家大基金持股        307816    同花顺
393        NaN      首发新股       301531    同花顺
394        NaN     半年报预增       308458    同花顺

[395 rows x 4 columns]
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
22. 概念指数成分信息-ths
说明介绍

获取同花顺的概念代码对应的最新成分列表信息，即概念代码和股票代码的关系。

成分信息会动态变动，需定期更新一次，建议每周进行更新

调用方法

stock.info.concept_constituent_ths()
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
输入参数

参数	类型	是否必填	说明
index_code	string	否	指数代码；8开头；例：885556
concept_code	string	否	概念代码；3开头；例：300843
name	string	否	概念名称；例：5G；来源于：同花顺问财
wait_time	int	否	等待时间：毫秒；表示每个请求的间隔时间，主要用于防止请求太频繁的限制。
**注：**三个参数选择其一，优先推荐使用指数代码

返回结果

字段	类型	注释	说明
short_name	string	股票简称	联特科技
stock_code	string	股票代码	301205
参考示例

import adata
df = adata.stock.info.concept_constituent_ths(index_code="885556")
print(df)
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
# 结果示例
stock_code short_name
0       688536        思瑞浦
1       301205       联特科技
2       300308       中际旭创
3       003031       中瓷电子
4       301191       菲菱科思
..         ...        ...
316     600601       方正科技
317     601929       吉视传媒
318     000889       ST中嘉
319     002089      *ST新海
320     000810       创维数字

[321 rows x 2 columns]
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
23. 单只股票所属概念-ths
说明介绍

获取单只股票所属的概念信息

调用方法

stock.info.get_concept_ths()
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
输入参数

参数	类型	是否必填	说明
stock_code	string	是	股票代码；例：300033
返回结果

字段	类型	注释	说明
stock_code	string	股票代码	300033
concept_code	string	股票代码	886031
name	string	概念名称	ChatGPT概念
source	string	来源	同花顺
reason	string	概念原因	打造的i问财目前是财经.....语音对话交互问答系统。
参考示例

import adata
df = adata.stock.info.get_concept_ths(stock_code="300033")
print(df)
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
# 结果示例
   stock_code concept_code       name source       reason
0      300033       886019     AIGC概念    同花顺    据公司2020年、2021年年报......自动生成技术。
1      300033       886031  ChatGPT概念    同花顺    打造的i问财目前是财经.....语音对话交互问答系统。
2      300033       885456      互联网金融    同花顺
..         ...          ...    ...    ...                    ...
9      300033       885402       智能医疗    同花顺
10     300033       886041       数据要素    同花顺
11     300033       885663       证金持股    同花顺

[12 rows x 5 columns]

Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
------EAST------
概念相关-东方财富
31. 概念指数信息-east
说明介绍

获取东方财富的的概念代码信息列表

调用方法

stock.info.all_concept_code_east()
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
输入参数

无

返回结果

字段	类型	注释	说明
name	string	名称	大飞机
index_code	string	指数代码	东方财富的概念指数代码是：BK开头
concept_code	string	概念代码	东方财富的概念指数代码是：BK开头，为了兼容指数代码和概念代码一致
source	string	来源	东方财富
参考示例

import adata
df = adata.stock.info.all_concept_code_east()
print(df)
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
# 结果示例
    concept_code index_code    name source
0         BK0814     BK0814     大飞机   东方财富
1         BK0519     BK0519    稀缺资源   东方财富
2         BK1047     BK1047    数据安全   东方财富
..           ...        ...     ...    ...
433       BK0804     BK0804     深股通   东方财富
434       BK0867     BK0867    富时罗素   东方财富
435       BK0596     BK0596    融资融券   东方财富

[436 rows x 4 columns]
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
32. 概念指数成分信息-east
说明介绍

获取东方财富的概念代码对应的最新成分列表信息，即概念代码和股票代码的关系。

成分信息会动态变动，需定期更新一次，建议每周进行更新

调用方法

stock.info.concept_constituent_east()
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
输入参数

参数	类型	是否必填	说明
concept_code	string	否	概念代码；BK开头
返回结果

字段	类型	注释	说明
short_name	string	股票简称	银之杰
stock_code	string	股票代码	300085
参考示例

import adata
df = adata.stock.info.concept_constituent_east(index_code="BK0637")
print(df)
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
# 结果示例
    stock_code short_name
0       300085        银之杰
1       002280       联络互动
2       301236       软通动力
..         ...        ...
208     600919       江苏银行
209     002385        大北农
210     601318       中国平安

[211 rows x 2 columns]
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
33. 单只股票所属概念-east
说明介绍

获取单只股票所属的概念信息

调用方法

stock.info.get_concept_east()
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
输入参数

参数	类型	是否必填	说明
stock_code	string	是	股票代码；例：300033
返回结果

字段	类型	注释	说明
stock_code	string	股票代码	600020
concept_code	string	股票代码	BK0685
name	string	概念名称	举牌
source	string	来源	东方财富
reason	string	概念原因	2023年06月05日公告显示获长城人寿保险股份有限公司举牌。
参考示例

import adata
df = adata.stock.info.get_concept_east(stock_code="600020")
print(df)
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
# 结果示例
  stock_code concept_code  name source                                                    reason
0     600020       BK0685    举牌   东方财富                           2023年06月05日公告显示获长城人寿保险股份有限公司举牌。
1     600020       BK0700   充电桩   东方财富  公司所属10对高速公路服务区95座充电桩已建设完成,计划2022年6月底前完成剩余7对高速公路服务区充电桩建设。
2     600020       BK0683  国企改革   东方财富                                         公司的实际控制人为河南省人民政府。
3     600020       BK0604  参股保险   东方财富                                  初始投资2亿元持有中原农业保险18.18%股权。
4     600020       BK0506    创投   东方财富                              2020年半年报显示公司旗下有西藏秉原创业投资有限公司。

Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
34. 单只股票所属板块-east
说明介绍

获取单只股票所属的板块信息：行业，板块（地区），概念

这是一个综合的接口，也可以用来获取个股的概念

调用方法

stock.info.get_plate_east()
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
输入参数

参数	类型	是否必填	说明
stock_code	string	是	股票代码；例：300033
plate_type	int	否	1. 行业 2. 地域板块 3.概念 ，默认：None 全部
返回结果

字段	类型	注释	说明
stock_code	string	股票代码	600020
plate_code	string	板块代码	BK0685
plate_name	string	板块名称	举牌
plate_type	string	板块类型	行业，板块，概念；
其中板块特指地区板块
source	string	来源	东方财富
参考示例

import adata
df = adata.stock.info.get_plate_east(stock_code="600020", plate_type=1)
print(df)
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
# 结果示例
  stock_code plate_code plate_name plate_type source
0     600020     BK0421       铁路公路         行业   东方财富

Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
------BAIDU------
41. 单只股票所属概念-baidu
说明介绍

获取单只股票所属的概念信息

调用方法

stock.info.get_concept_baidu()
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
输入参数

参数	类型	是否必填	说明
stock_code	string	是	股票代码；例：300033；也可以一次传入多个[]
返回结果

字段	类型	注释	说明
stock_code	string	股票代码	600020
concept_code	string	股票代码	BK0685
name	string	概念名称	举牌
source	string	来源	百度股市通
reason	string	概念原因	空
参考示例

import adata
df = adata.stock.info.get_concept_baidu(stock_code="600020")
print(df)
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
# 结果示例
   stock_code concept_code      name source reason
0      600020       002486    文化传媒概念  百度股市通       
1      600020       003506        创投  百度股市通       
2      600020       003596      融资融券  百度股市通       
......  
12     600020       GN2102   标普道琼斯中国  百度股市通       
13     600020       GN2109    高速公路概念  百度股市通       
14     600020       GN2142       破净股  百度股市通    

Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
------INDEX------
指数相关
51. 指数代码信息
说明介绍

获取A股所有指数信息列表

调用方法

stock.info.all_index_code()
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
输入参数

无

返回结果

字段	类型	注释	说明
name	string	指数简称	能源金属
index_code	string	指数代码	399366
concept_code	string	概念代码	同花顺的编码；例：000819 对应1B0819
source	string	来源	同花顺
参考示例

import adata
df = adata.stock.info.all_index_code()
print(df)
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
# 结果示例
    index_code concept_code   name source
0       399366       399366   能源金属    同花顺
1       000823       1B0823  800有色    同花顺
2       399395       399395   国证有色    同花顺
3       000819       1B0819   有色金属    同花顺
4       399232       399232   采矿指数    同花顺
..         ...          ...    ...    ...
546     399617       399617   深证消费    同花顺
547     399389       399389   国证通信    同花顺
548     000869       1B0869   HK银行    同花顺
549     399621       399621   深证电信    同花顺
550     399688       399688   深成电信    同花顺

[551 rows x 4 columns]
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
52. 指数成分信息
说明介绍

获取指数对应的最新成分列表信息，即指数代码和股票代码的关系。

成分信息会动态变动，需定期更新一次，建议每周进行更新

调用方法

stock.info.index_constituent()
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
输入参数

参数	类型	是否必填	说明
index_code	string	是	指数代码；例：000823
返回结果

字段	类型	注释	说明
short_name	string	股票简称	云南锗业
stock_code	string	股票代码	002428
index_code	string	指数代码	000823
wait_time	int	否	等待时间：毫秒；
表示每个请求的间隔时间，主要用于防止请求太频繁的限制。
参考示例

import adata
df = adata.stock.info.index_constituent(index_code='000823')
print(df)
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
# 结果示例
   index_code stock_code short_name
0      000823     002428       云南锗业
1      000823     601020       华钰矿业
2      000823     000960       锡业股份
3      000823     600497       驰宏锌锗
4      000823     002392       北京利尔
..        ...        ...        ...
68     000823     002056       横店东磁
69     000823     000629       钒钛股份
70     000823     600111       北方稀土
71     000823     600366       宁波韵升
72     000823     600259       广晟有色

[73 rows x 3 columns]
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
------OTHER------
其它信息
91. 交易日历
说明介绍

获取对应年份的交易日历信息

调用方法

stock.info.trade_calendar()
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
输入参数

参数	类型	是否必填	说明
year	int	是	年份；例：2023
返回结果

字段	类型	注释	说明
trade_date	date	交易日	2023-05-20
trade_status	int	交易状态：0.非交易日；1.交易日	1
day_week	int	一周第几天	从星期天开始的
参考示例

import adata
df = adata.stock.info.trade_calendar(year=2023)
print(df)
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
# 结果示例
     trade_date trade_status  day_week
0    2023-01-01            0         1
1    2023-01-02            0         2
2    2023-01-03            1         3
3    2023-01-04            1         4
4    2023-01-05            1         5
..          ...          ...       ...
360  2023-12-27            1         4
361  2023-12-28            1         5
362  2023-12-29            1         6
363  2023-12-30            0         7
364  2023-12-31            0         1

[365 rows x 3 columns]
STOCK
股票-STOCK-MARKET
1. 分红-DIVIDEND
说明介绍

获取对应股票的所有历史分红信息

调用方法

stock.market.get_dividend()
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
输入参数

参数	类型	是否必填	说明
stock_code	string	是	股票代码；例：000001
返回结果

字段	类型	注释	说明
stock_code	string	代码	600001
report_date	date	公告日	1990-01-01
dividend_plan	string	分红方案	10股派3.00元，10股转赠5.00股
ex_dividend_date	date	除权除息日	1990-01-01
参考示例

import adata
df = adata.stock.market.get_dividend(stock_code='000001')
print(df)
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
# 结果示例
   report_date         dividend_plan ex_dividend_date stock_code
0   2023-06-07             10股派2.85元       2023-06-14     000001
2   2022-07-15             10股派2.28元       2022-07-22     000001
4   2021-05-07             10股派1.80元       2021-05-14     000001
6   2020-05-22             10股派2.18元       2020-05-28     000001
8   2019-06-20             10股派1.45元       2019-06-26     000001
10  2018-07-06             10股派1.36元       2018-07-12     000001
12  2017-07-17             10股派1.58元       2017-07-21     000001
14  2016-06-08  10股派1.53元，10股转赠2.00股       2016-06-16     000001
16  2015-04-07  10股派1.74元，10股转赠2.00股       2015-04-13     000001
18  2014-06-06  10股派1.60元，10股转赠2.00股       2014-06-12     000001
20  2013-06-14             10股派1.70元       2013-06-20     000001
21  2012-10-12             10股派1.00元       2012-10-19     000001
29  2008-10-24             10股派0.34元       2008-10-31     000001
39  2003-09-23             10股派1.50元       2003-09-29     000001
42  2002-07-17             10股派1.50元       2002-07-23     000001
47  1999-10-11             10股派6.00元       1999-10-18     000001
52  1997-08-19             10股派2.00元       1997-08-25     000001
54  1996-05-23            10股转赠5.00股       1996-05-27     000001
55  1995-09-15             10股派3.00元       1995-09-25     000001
57  1994-07-02  10股派5.00元，10股转赠2.00股       1994-07-11     000001
60  1993-05-07  10股派3.00元，10股转赠5.00股       1993-05-24     000001
61  1992-03-14             10股派2.00元       1992-03-23     000001
62  1991-03-03             10股派3.00元       1991-05-02     000001
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
2. 股票行情-MARKET
2.1 k线行情
说明介绍
获取当个股票的K线行情信息

日，周，月K

调用方法
stock.market.get_market()
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
输入参数
参数	类型	是否必填	默认值	说明
stock_code	string	是	000001	股票代码
start_date	string	否	1990-01-01	开始时间；例：'2021-01-01'
end_date	string	否	当天	结束时间；例：'2021-01-01'
k_type	int	否	1	k线类型：1.日；2.周；3.月 ；4.季度；5.5分钟；15.15分钟；30.30分钟；60.60分钟；默认：1 日k
adjust_type	int	否	1	k线复权类型：0.不复权；1.前复权；2.后复权 默认：1 前复权
返回结果
字段	类型	注释	说明
stock_code	string	代码	600001
trade_time	time	交易时间	1990-01-01 00:00:00；分时图使用具体的时间
trade_date	date	交易日期	1990-01-01
open	decimal	开盘价(元)	9.98
close	decimal	收盘价(元)	9.98
high	decimal	最高价(元)	9.98
low	decimal	最低价(元)	9.98
volume	decimal	成交量(股)	64745722
amount	decimal	成交额(元)	934285179.00
change	decimal	涨跌额(元)	-0.02
change_pct	decimal	涨跌幅(%)	-0.16
turnover_ratio	decimal	换手率(%)	0.38
pre_close	decimal	昨收(元)	10.00
参考示例
import adata
df = adata.stock.market.get_market(stock_code='000001', start_date='2021-01-01', k_type=1, adjust_type=1)
print(df)
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
# 结果示例
              trade_time   open  close  ... pre_close stock_code  trade_date
0    2021-01-04 00:00:00  18.41  17.91  ...     18.65     000001  2021-01-04
1    2021-01-05 00:00:00  17.71  17.48  ...     17.91     000001  2021-01-05
2    2021-01-06 00:00:00  17.39  18.87  ...     17.48     000001  2021-01-06
3    2021-01-07 00:00:00  18.83  19.21  ...     18.87     000001  2021-01-07
4    2021-01-08 00:00:00  19.21  19.16  ...     19.21     000001  2021-01-08
..                   ...    ...    ...  ...       ...        ...         ...
601  2023-06-29 00:00:00  11.29  11.18  ...     11.30     000001  2023-06-29
602  2023-06-30 00:00:00  11.18  11.23  ...     11.18     000001  2023-06-30
603  2023-07-03 00:00:00  11.24  11.49  ...     11.23     000001  2023-07-03
604  2023-07-04 00:00:00  11.46  11.40  ...     11.49     000001  2023-07-04
605  2023-07-05 00:00:00  11.36  11.34  ...     11.40     000001  2023-07-05

[606 rows x 13 columns]
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
2.2 分时行情
说明介绍
获取单个股票最新交易日的分时行情

盘中请求就是当天的实时分时行情

分钟级别：每分钟一条数据

调用方法
stock.market.get_market_min()
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
输入参数
参数	类型	是否必填	说明
stock_code	string	是	股票代码；例：000001,默认获取最新交易日的分时行情
返回结果
字段	类型	注释	说明
stock_code	string	代码	600001
trade_time	time	交易时间	1990-01-01 00:00:00；分时图使用具体的时间
trade_date	date	交易日期	1990-01-01
price	decimal	价格(元)	9.98
avg_price	decimal	平均价(元)	9.98
change	decimal	涨跌额(元)	-0.02
change_pct	decimal	涨跌幅(%)	-0.16
volume	decimal	成交量(股)	64745722
amount	decimal	成交额(元)	934285179.00
参考示例
import adata
df = adata.stock.market.get_market_min(stock_code='000001')
print(df)
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
# 结果示例
    stock_code           trade_time  price  ...  volume  avg_price   amount
0       000001  2023-07-05 09:30:00  11.36  ...  488600      11.36  5550496
1       000001  2023-07-05 09:31:00  11.37  ...  461600      11.36  5247873
2       000001  2023-07-05 09:32:00  11.37  ...  220500      11.37  2507387
3       000001  2023-07-05 09:33:00  11.38  ...  148500      11.37  1688939
4       000001  2023-07-05 09:34:00  11.38  ...  111100      11.37  1263744
..         ...                  ...    ...  ...     ...        ...      ...
236     000001  2023-07-05 14:56:00  11.34  ...  159600      11.33  1808983
237     000001  2023-07-05 14:57:00  11.34  ...  292200      11.33  3313452
238     000001  2023-07-05 14:58:00  11.35  ...   11300      11.33   128253
239     000001  2023-07-05 14:59:00  11.35  ...       0      11.33        0
240     000001  2023-07-05 15:00:00  11.34  ...  657700      11.33  7458318

[241 rows x 8 columns]
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
2.3 实时行情
说明介绍
获取多只股票的实时最新行情数据

可以传多只股票的代码，建议最多不超过500个，多了接口无法返回

调用方法
stock.market.list_market_current()
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
输入参数
参数	类型	是否必填	说明
code_list	list	是	数量限制最多500个，股票代码列表；例：['000001', '600001', '000795', '872925']
返回结果
字段	类型	注释	说明
stock_code	string	代码	600001
short_name	string	简称	平安银行
price	decimal	当前价格(元)	12.36
change	decimal	涨跌额(元)	0.02
change_pct	decimal	涨跌幅(%)	0.16
volume	decimal	成交量(股)	34452500
amount	decimal	成交额(元)	
参考示例
import adata
df = adata.stock.market.list_market_current(code_list=['000001', '600001', '000795', '872925'])
print(df)
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
# 结果示例
  stock_code short_name  price change change_pct     volume        amount
0     000001       平安银行  11.34  -0.06      -0.53   42348200   479720000.0
1     000795        英洛华   8.10   0.53       7.00  140166000  1098610000.0
2     872925       锦好医疗  9.530  0.000      0.000     318846   3008578.290
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
2.4 五档行情
说明介绍
获取单个股票的5档行情

调用方法
stock.market.get_market_five()
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
输入参数
参数	类型	是否必填	说明
stock_code	str	是	股票代码；例：'000001'
返回结果
字段	类型	注释	说明
stock_code	string	代码	600001
short_name	string	简称	平安银行
s5	decimal	卖5价(元)	11.29
sv5	decimal	卖5量(股)	2263
s4	decimal	卖4价(元)	11.28
sv4	decimal	卖4量(股)	2263
s3	decimal	卖3价(元)	11.27
sv3	decimal	卖3量(股)	2263
s2	decimal	卖2价(元)	11.26
sv2	decimal	卖2量(股)	2263
s1	decimal	卖1价(元)	11.25
sv1	decimal	卖1量(股)	2263
b1	decimal	买1价(元)	11.24
bv1	decimal	买1量(股)	2263
b2	decimal	买2价(元)	11.23
bv2	decimal	买2量(股)	2263
b3	decimal	买3价(元)	11.22
bv3	decimal	买3量(股)	2263
b4	decimal	买4价(元)	11.21
bv4	decimal	买4量(股)	2263
b5	decimal	买5价(元)	11.20
bv5	decimal	买5量(股)	2263
参考示例
import adata
df = adata.stock.market.get_market_five(stock_code='000001')
print(df)
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
# 结果示例
  stock_code short_name     s5   sv5     s4  ...   bv3     b4    bv4     b5    bv5
0     000001     平安银行  11.29  2263  11.28  ...  8234  11.21  11227  11.20  14241

[1 rows x 22 columns]
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
2.5 分时成交
说明介绍
获取单个股票的成交分时，最新200条记录

调用方法
stock.market.get_market_bar()
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
输入参数
参数	类型	是否必填	说明
stock_code	str	是	股票代码；例：'000001'
返回结果
字段	类型	注释	说明
stock_code	string	代码	600001
trade_time	datetime	成交时间	2023-09-13 09:31:45
price	decimal	当前价格(元)	12.36
volume	decimal	成交量(股)	34452500
bs_type	string	买卖类型	B：买入，S：卖出
参考示例
import adata
df = adata.stock.market.get_market_bar(stock_code='000001')
print(df)
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
# 结果示例
    stock_code           trade_time  price  volume bs_type
0       000001  2023-09-13 14:46:15  11.25    5800       B
1       000001  2023-09-13 14:46:18  11.25     500       B
..         ...                  ...    ...     ...     ...
198     000001  2023-09-13 14:57:00  11.24    5100       S
199     000001  2023-09-13 15:00:00  11.25  351500       B

[200 rows x 5 columns]
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
3. 资金流向-CAPITAL_FLOW
3.1 分时资金流向
说明介绍
获取单个股票的资金流向分时

调用方法
stock.market.get_capital_flow_min()
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
输入参数
参数	类型	是否必填	说明
stock_code	str	是	股票代码；例：'000001'
返回结果
字段	类型	注释	说明
stock_code	string	代码	600001
trade_time	datetime	交易时间	2023-09-13 09:31:00
main_net_inflow	decimal	主力资金净流入(元)	2526394.0
max_net_inflow	decimal	特大单净流入(元)	2526394.0
lg_net_inflow	decimal	大单净流入(元)	2526394.0
mid_net_inflow	decimal	中单净流入(元)	2526394.0
sm_net_inflow	decimal	小单净流入(元)	2526394.0
参考示例
import adata
df = adata.stock.market.get_capital_flow_min(stock_code='000001')
print(df)
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
# 结果示例
    stock_code          trade_time  ...  lg_net_inflow  max_net_inflow
0       000001 2024-07-01 09:31:00  ...      2526394.0      -7783258.0
1       000001 2024-07-01 09:32:00  ...      6225393.0     -10094040.0
..         ...                 ...  ...            ...             ...
238     000001 2024-07-01 14:59:00  ...     28848843.0     196753761.0
239     000001 2024-07-01 15:00:00  ...     28848843.0     196753761.0

[240 rows x 7 columns]
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
3.2 资金流向-日度
说明介绍
获取单个股票的资金流向历史数据-日度

注：只能获取最近2-3年的数据

调用方法
stock.market.get_capital_flow()
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
输入参数
参数	类型	是否必填	说明
stock_code	str	是	股票代码；例：'000001'
start_date	date	否	开始时间
end_date	date	否	结束时间
返回结果
字段	类型	注释	说明
stock_code	string	代码	600001
trade_date	date	交易日期	2023-09-13
main_net_inflow	decimal	主力资金净流入(元)	2526394.0
max_net_inflow	decimal	特大单净流入(元)	2526394.0
lg_net_inflow	decimal	大单净流入(元)	2526394.0
mid_net_inflow	decimal	中单净流入(元)	2526394.0
sm_net_inflow	decimal	小单净流入(元)	2526394.0
参考示例
import adata
df = adata.stock.market.get_capital_flow(stock_code='000001')
print(df)
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
# 结果示例
    stock_code  trade_date  ...  lg_net_inflow  max_net_inflow
0       000001  2024-06-28  ...    -12017600.0      28667900.0
1       000001  2024-06-27  ...     -7480400.0      62277700.0
2       000001  2024-06-26  ...    -12715700.0      19353300.0
..         ...         ...  ...            ...             ...
667     000001  2021-09-23  ...   -272000000.0       8215700.0
668     000001  2021-09-22  ...      2247000.0     121000000.0
669     000001  2021-09-17  ...   -129000000.0     -66753500.0

[670 rows x 7 columns]
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
概念行情-CONCEPT MARKET
同花顺和东方财富的返回字段结果有所不同，请根据需求进行选择
股票概念是各大厂商自定义的，在设置成分上有所不同，所以分开提供
指数是官方提供，各大厂商一致可进行融合处理。
20. 概念行情-同花顺-THS
概念和指数在字典表以及运作的逻辑是一致的，所以参数的命名都为：index_code

20.1 k线
说明介绍
获取单个同花顺概念指数的K线行情

日，周，月K

调用方法
stock.market.get_market_concept_ths()
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
输入参数
参数	类型	是否必填	默认值	说明
index_code	string	是	886013	同花顺概念指数代码；8开头；例：886041
k_type	int	否	1	k线类型：1.日；2.周；3.月 默认：1 日k
返回结果
字段	类型	注释	说明
index_code	string	代码	886041
trade_time	time	交易时间	1990-01-01 00:00:00；分时图使用具体的时间
trade_date	date	交易日期	1990-01-01
open	decimal	开盘价	9.98
close	decimal	收盘价	9.98
high	decimal	最高价	9.98
low	decimal	最低价	9.98
volume	decimal	成交量(股)	64745722
amount	decimal	成交额(元)	934285179.00
change	decimal	涨跌额	-0.02
change_pct	decimal	涨跌幅(%)	-0.16
参考示例
import adata
df = adata.stock.market.get_market_concept_ths(index_code='886041')
print(df)
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
# 结果示例
    trade_date      open      high  ...           trade_time change change_pct
0   2023-04-11  1000.238  1045.339  ...  2023-04-11 00:00:00   None       None
1   2023-04-12  1042.454  1079.156  ...  2023-04-12 00:00:00  33.73       3.24
2   2023-04-13  1067.842  1097.239  ...  2023-04-13 00:00:00 -10.72       -1.0
3   2023-04-14  1066.132  1082.673  ...  2023-04-14 00:00:00  -3.71      -0.35
4   2023-04-17  1052.973  1053.219  ...  2023-04-17 00:00:00 -32.52      -3.06
..                   ...      ...        ...  ...         ...    ...        ...
53  2023-06-30   983.923   996.860  ...  2023-06-30 00:00:00   7.06       0.72
54  2023-07-03   995.890   996.022  ...  2023-07-03 00:00:00  -3.61      -0.36
55  2023-07-04   986.366   998.607  ...  2023-07-04 00:00:00    8.4       0.85
56  2023-07-05  1003.030  1017.040  ...  2023-07-05 00:00:00  -8.08      -0.81
57  2023-07-06      None      None  ...  2023-07-06 00:00:00  -0.92      -0.09

[58 rows x 11 columns]
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
20.2 分时
说明介绍
获取单个同花顺概念指数的分时行情

调用方法
stock.market.get_market_concept_min_ths()
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
输入参数
参数	类型	是否必填	默认值	说明
index_code	string	是	886041	同花顺概念指数代码；8开头；例：886041
返回结果
字段	类型	注释	说明
index_code	string	代码	886041
trade_time	time	交易时间	1990-01-01 00:00:00；分时图使用具体的时间
trade_date	date	交易日期	1990-01-01
price	decimal	现价	9.98
avg_price	decimal	均价	默认为：None
volume	decimal	成交量(股)	64745722
amount	decimal	成交额(元)	934285179.00
change	decimal	涨跌额	-0.02
change_pct	decimal	涨跌幅(%)	-0.16
注意：同花顺分时没有平均价；

参考示例
import adata
df = adata.stock.market.get_market_concept_min_ths(index_code='886041')
print(df)
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
# 结果示例
              trade_time    price     amount  ...  trade_date change change_pct
0    2023-07-06 09:30:00  988.049  220005020  ...  2023-07-06 -2.441  -0.246444
1    2023-07-06 09:31:00  984.919  668256970  ...  2023-07-06 -5.571  -0.562449
2    2023-07-06 09:32:00  988.754  544952910  ...  2023-07-06 -1.736  -0.175267
3    2023-07-06 09:33:00  990.360  412018700  ...  2023-07-06 -0.130  -0.013125
4    2023-07-06 09:34:00  991.679  349169300  ...  2023-07-06  1.189   0.120042
..                   ...      ...        ...  ...         ...    ...        ...
236  2023-07-06 14:56:00  986.953  201322000  ...  2023-07-06 -3.537  -0.357096
237  2023-07-06 14:57:00  987.137  220634000  ...  2023-07-06 -3.353  -0.338519
238  2023-07-06 14:58:00  987.043   37234000  ...  2023-07-06 -3.447  -0.348010
239  2023-07-06 14:59:00  987.043          0  ...  2023-07-06 -3.447  -0.348010
240  2023-07-06 15:00:00  987.264  313642000  ...  2023-07-06 -3.226  -0.325697

[241 rows x 9 columns]
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
20.3 实时
说明介绍
获取当个同花顺概念指数的实时行情，即最新的行情数据。

调用方法
stock.market.get_market_concept_current_ths()
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
输入参数
参数	类型	是否必填	默认值	说明
index_code	string	是	886041	同花顺概念指数代码；8开头；例：886041
返回结果
字段	类型	注释	说明
index_code	string	代码	886041
trade_time	time	交易时间	1990-01-01 00:00:00；返回当前的时间
trade_date	date	交易日期	1990-01-01
open	decimal	开盘价	9.98
price	decimal	现价	9.98
high	decimal	最高价	9.98
low	decimal	最低价	9.98
volume	decimal	成交量(股)	64745722
amount	decimal	成交额(元)	934285179.00
change	decimal	涨跌额	默认为：None
change_pct	decimal	涨跌幅(%)	默认为：None
注意：同花顺实时行情没有昨日收盘，涨跌额和涨跌幅无法计算；
参考示例
import adata
df = adata.stock.market.get_market_concept_current_ths(index_code='886041')
print(df)
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
# 结果示例
            trade_time  trade_date  ...           amount index_code
0  2023-07-06 17:57:00  2023-07-06  ...  27101941000.000     886041

[1 rows x 9 columns]
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
21. 概念行情-东方财富-EAST
概念和指数在字典表以及运作的逻辑是一致的，所以参数的命名都为：index_code

21.1 k线
说明介绍
获取单个东方财富概念指数的K线行情

日，周，月K

调用方法
stock.market.get_market_concept_east()
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
输入参数
参数	类型	是否必填	默认值	说明
index_code	string	是	BK0612	东方财富概念指数代码；BK开头
k_type	int	否	1	k线类型：1.日；2.周；3.月 默认：1 日k
返回结果
字段	类型	注释	说明
index_code	string	代码	BK0612
trade_time	time	交易时间	2012-06-14 00:00:00；分时图使用具体的时间
trade_date	date	交易日期	2012-06-14 00:00:00
open	decimal	开盘价	9.98
close	decimal	收盘价	9.98
high	decimal	最高价	9.98
low	decimal	最低价	9.98
volume	decimal	成交量(股)	64745722
amount	decimal	成交额(元)	934285179.00
change	decimal	涨跌额	-0.02
change_pct	decimal	涨跌幅(%)	-0.16
参考示例
import adata
df = adata.stock.market.get_market_concept_east(index_code='BK0612')
print(df)
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
# 结果示例
     index_code           trade_time  ...  change  change_pct
0        BK0612  2012-06-14 00:00:00  ... -239.25      -23.93
1        BK0612  2012-06-15 00:00:00  ...    5.69        0.75
2        BK0612  2012-06-18 00:00:00  ...    0.57        0.07
3        BK0612  2012-06-19 00:00:00  ...   -3.31       -0.43
4        BK0612  2012-06-20 00:00:00  ...    0.61        0.08
...         ...                  ...  ...     ...         ...
2707     BK0612  2023-08-02 00:00:00  ...  -10.44       -0.72
2708     BK0612  2023-08-03 00:00:00  ...   14.37        1.00
2709     BK0612  2023-08-04 00:00:00  ...    3.63        0.25
2710     BK0612  2023-08-07 00:00:00  ...  -11.75       -0.80
2711     BK0612  2023-08-08 00:00:00  ...   -5.10       -0.35

[2712 rows x 11 columns]
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
21.2 分时
说明介绍
获取单个东方财富概念指数的分时行情

调用方法
stock.market.get_market_concept_min_east()
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
输入参数
参数	类型	是否必填	默认值	说明
index_code	string	是	BK0612	东方财富概念指数代码；BK开头
返回结果
字段	类型	注释	说明
index_code	string	代码	BK0612
trade_time	time	交易时间	1990-01-01 00:00:00；分时图使用具体的时间
trade_date	date	交易日期	1990-01-01
price	decimal	现价	9.98
avg_price	decimal	均价	9.98
volume	decimal	成交量(股)	64745722
amount	decimal	成交额(元)	934285179.00
change	decimal	涨跌额	-0.02
change_pct	decimal	涨跌幅(%)	-0.16
参考示例
import adata
df = adata.stock.market.get_market_concept_min_east(index_code='BK0612')
print(df)
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
# 结果示例
    index_code           trade_time  ... change  change_pct
0       BK0612  2023-08-08 09:30:00  ...  -3.27       -0.23
1       BK0612  2023-08-08 09:31:00  ...  -4.50       -0.31
2       BK0612  2023-08-08 09:32:00  ...  -5.02       -0.35
3       BK0612  2023-08-08 09:33:00  ...  -4.75       -0.33
4       BK0612  2023-08-08 09:34:00  ...  -6.32       -0.44
..         ...                  ...  ...    ...         ...
236     BK0612  2023-08-08 14:56:00  ...  -4.94       -0.34
237     BK0612  2023-08-08 14:57:00  ...  -4.75       -0.33
238     BK0612  2023-08-08 14:58:00  ...  -4.96       -0.34
239     BK0612  2023-08-08 14:59:00  ...  -4.96       -0.34
240     BK0612  2023-08-08 15:00:00  ...  -5.10       -0.35

[241 rows x 9 columns]
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
21.3 实时
说明介绍
获取当个东方财富概念指数的实时行情，即最新的行情数据。

调用方法
stock.market.get_market_concept_current_east()
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
输入参数
参数	类型	是否必填	默认值	说明
index_code	string	是	BK0612	东方财富概念指数代码；BK开头
返回结果
字段	类型	注释	说明
index_code	string	代码	886041
trade_time	time	交易时间	1990-01-01 00:00:00；返回当前的时间
trade_date	date	交易日期	默认：None
open	decimal	开盘价	9.98
price	decimal	现价	9.98
high	decimal	最高价	9.98
low	decimal	最低价	9.98
volume	decimal	成交量(股)	64745722
amount	decimal	成交额(元)	934285179.00
change	decimal	涨跌额	-0.02
change_pct	decimal	涨跌幅(%)	-0.16
注意：东方财富实时接口未返回交易日期；结果为最新行情数据。
参考示例
import adata
df = adata.stock.market.get_market_concept_current_east(index_code='BK0612')
print(df)
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
# 结果示例
  index_code                 trade_time  ...  change  change_pct
0     BK0612 2023-08-08 19:21:52.012726  ...  -510.0       -0.35

[1 rows x 11 columns]
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
23. 概念资金流向-东方财富-EAST
23.1 近N日所有概念流向
说明介绍
获取所有的概念近(1,5,10)日的资金流向

调用方法
stock.market.all_capital_flow_east()
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
输入参数
参数	类型	是否必填	说明
days_type	int	否	天数类型：1,5,10，默认：近1天
返回结果
字段	类型	注释	说明
index_code	string	概念代码	BK0897
index_name	string	概念名称	IPv6
change_pct	decimal	概念最近N日涨跌幅(%)	-0.68
main_net_inflow	decimal	主力资金净流入(元)	2526394.0
main_net_inflow_rate	decimal	主力资金净流入占比(%)	0.77
max_net_inflow	decimal	特大单净流入(元)	2526394.0
max_net_inflow_rate	decimal	特大单净流入占比(%)	0.77
lg_net_inflow	decimal	大单净流入(元)	2526394.0
lg_net_inflow_rate	decimal	特大单净流入占比(%)	0.77
mid_net_inflow	decimal	中单净流入(元)	2526394.0
mid_net_inflow_rate	decimal	中单净流入占比(%)	0.77
sm_net_inflow	decimal	小单净流入(元)	2526394.0
sm_net_inflow_rate	decimal	小单净流入占比(%)	0.77
stock_code	string	流入最大股代码	000063
stock_name	string	流入最大股名称	中兴通讯
参考示例
import adata
df = adata.stock.market.all_capital_flow_east(days_type=10)
print(df)
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
# 结果示例
    index_code index_name  change_pct  ...  sm_net_inflow_rate  stock_code  stock_name
0       BK0897       IPv6       -0.68  ...                0.00        中兴通讯      000063
1       BK0947       屏下摄像       -2.23  ...                0.77        中兴通讯      000063
..         ...        ...         ...  ...                 ...         ...         ...
573     BK0867       富时罗素       -5.09  ...                3.30        中兴通讯      000063
574     BK0596       融资融券       -6.96  ...                3.01        中兴通讯      000063

[575 rows x 15 columns]
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
40. 指数行情-INDEX MARKET
概念和指数在字典表以及运作的逻辑是一致的，所以参数的命名都为：index_code

40.1 k线
说明介绍
获取单个指数的K线行情

日，周，月K

调用方法
stock.market.get_market_index()
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
输入参数
参数	类型	是否必填	默认值	说明
index_code	string	是	000001	指数代码；例：000001 上证指数
k_type	int	否	1	k线类型：1.日；2.周；3.月 默认：1 日k
start_date	string	否	2020-01-01	开始时间：2020-01-01
返回结果
字段	类型	注释	说明
index_code	string	代码	000001
trade_time	time	交易时间	1990-01-01 00:00:00；分时图使用具体的时间
trade_date	date	交易日期	1990-01-01
open	decimal	开盘价	9.98
close	decimal	收盘价	9.98
high	decimal	最高价	9.98
low	decimal	最低价	9.98
volume	decimal	成交量(股)	64745722
amount	decimal	成交额(元)	934285179.00
change	decimal	涨跌额(元)	-0.02
change_pct	decimal	涨跌幅(%)	-0.16
参考示例
import adata
df = adata.stock.market.get_market_index(index_code='000001', start_date='2022-12-01')
print(df)
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
# 结果示例
     trade_date     open     high  ...           trade_time change change_pct
463  2022-12-01  3187.99  3198.41  ...  2022-12-01 00:00:00  14.13       0.45
464  2022-12-02  3160.58  3170.90  ...  2022-12-02 00:00:00  -9.33      -0.29
465  2022-12-05  3181.92  3213.44  ...  2022-12-05 00:00:00  55.67       1.76
..          ...      ...      ...  ...                  ...    ...        ...
603  2023-07-03  3209.16  3246.86  ...  2023-07-03 00:00:00  41.92       1.31
604  2023-07-04  3241.23  3246.96  ...  2023-07-04 00:00:00   1.37       0.04
605  2023-07-05  3240.05  3241.84  ...  2023-07-05 00:00:00  -22.4      -0.69

[143 rows x 11 columns]
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
40.2 分时
说明介绍
获取单个指数的分时行情

调用方法
stock.market.get_market_index_min()
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
输入参数
参数	类型	是否必填	说明
index_code	string	是	指数代码；例：000001 上证指数
返回结果
字段	类型	注释	说明
index_code	string	代码	000001
trade_time	time	交易时间	1990-01-01 00:00:00；分时图使用具体的时间
trade_date	date	交易日期	1990-01-01
price	decimal	现价	9.98
avg_price	decimal	均价	9.98
volume	decimal	成交量(股)	64745722
amount	decimal	成交额(元)	934285179.00
change	decimal	涨跌额	-0.02
change_pct	decimal	涨跌幅(%)	-0.16
参考示例
import adata
df = adata.stock.market.get_market_index_min(index_code='000001')
print(df)
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
# 结果示例
    index_code           trade_time  trade_date  ...      amount  change change_pct
0       000001  2023-07-06 09:30:00  2023-07-06  ...  2166038100   -7.09      -0.22
1       000001  2023-07-06 09:31:00  2023-07-06  ...  7596315500   -8.18      -0.25
2       000001  2023-07-06 09:32:00  2023-07-06  ...  5490170400   -8.39      -0.26
..         ...                  ...         ...  ...         ...     ...        ...
239     000001  2023-07-06 14:58:00  2023-07-06  ...   171210000  -16.76      -0.52
240     000001  2023-07-06 14:59:00  2023-07-06  ...           0  -16.76      -0.52
241     000001  2023-07-06 15:00:00  2023-07-06  ...  3825890000  -17.38      -0.54

[242 rows x 9 columns]
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
40.3 实时
说明介绍
获取当个指数的实时行情，即最新的指数行情数据。

调用方法
stock.market.get_market_index_current()
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
输入参数
参数	类型	是否必填	说明
index_code	string	是	指数代码；例：000001 上证指数
返回结果
字段	类型	注释	说明
index_code	string	代码	000001
trade_time	time	交易时间	1990-01-01 00:00:00；返回当前的时间
trade_date	date	交易日期	1990-01-01
open	decimal	开盘价	9.98
price	decimal	现价	9.98
high	decimal	最高价	9.98
low	decimal	最低价	9.98
volume	decimal	成交量(股)	64745722
amount	decimal	成交额(元)	934285179.00
参考示例
import adata
df = adata.stock.market.get_market_index_current(index_code='000001')
print(df)
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
# 结果示例
            trade_time  trade_date  ...           amount index_code
0  2023-07-06 18:07:00  2023-07-06  ...  320808310000.00     000001

[1 rows x 9 columns]
STOCK
整个项目API的数据字典，按照模块层级分类命名，描述尽量精简，以便进行查阅。

股票-STOCK-FINANCE
------------
财务指标相关
1. 股票财务核心指标
说明介绍

获取单只股票财务的核心指标

调用方法

stock.finance.get_core_index()
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
输入参数

参数	类型	是否必填	说明
stock_code	string	是	股票代码；例：300033
返回结果

字段	类型	注释	说明
stock_code	string	股票代码	
short_name	string	股票简称	
report_date	date	报告日期	
report_type	date	报告类型	
notice_date	date	公布日期	
basic_eps	decimal	基本每股收益(元)	
diluted_eps	decimal	稀释每股收益(元)	
non_gaap_eps	decimal	扣非每股收益(元)	
net_asset_ps	decimal	每股净资产(元)	
cap_reserve_ps	decimal	每股公积金(元)	
undist_profit_ps	decimal	每股未分配利润(元)	
oper_cf_ps	decimal	每股经营现金流(元)	
total_rev	decimal	营业总收入(元)	
gross_profit	decimal	毛利润(元)	
net_profit_attr_sh	decimal	归属净利润(元)	
non_gaap_net_profit	decimal	扣非净利润(元)	
total_rev_yoy_gr	decimal	营业总收入同比增长(%)	
net_profit_yoy_gr	decimal	归属净利润同比增长(%)	
non_gaap_net_profit_yoy_gr	decimal	扣非净利润同比增长(%)	
total_rev_qoq_gr	decimal	营业总收入滚动环比增长(%)	
net_profit_qoq_gr	decimal	归属净利润滚动环比增长(%)	
non_gaap_net_profit_qoq_gr	decimal	扣非净利润滚动环比增长(%)	
roe_wtd	decimal	净资产收益率(加权)(%)	
roe_non_gaap_wtd	decimal	净资产收益率(扣非/加权)(%)	
roa_wtd	decimal	总资产收益率(加权)(%)	
gross_margin	decimal	毛利率(%)	
net_margin	decimal	净利率(%)	
adv_receipts_to_rev	decimal	预收账款/营业总收入	
net_cf_sales_to_rev	decimal	销售净现金流/营业总收入	
oper_cf_to_rev	decimal	经营净现金流/营业总收入	
eff_tax_rate	decimal	实际税率(%)	
curr_ratio	decimal	流动比率	
quick_ratio	decimal	速动比率	
cash_flow_ratio	decimal	现金流量比率	
asset_liab_ratio	decimal	资产负债率(%)	
equity_multiplier	decimal	权益系数	
equity_ratio	decimal	产权比率	
total_asset_turn_days	decimal	总资产周转天数(天)	
inv_turn_days	decimal	存货周转天数(天)	
acct_recv_turn_days	decimal	应收账款周转天数(天)	
total_asset_turn_rate	decimal	总资产周转率(次)	
inv_turn_rate	decimal	存货周转率(次)	
acct_recv_turn_rate	decimal	应收账款周转率(次)	
参考示例

import adata
df = adata.stock.finance.get_core_index(stock_code='300033')
print(df)
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
Copied!
# 结果示例
   stock_code short_name  ... inv_turn_rate acct_recv_turn_rate
18     300033        同花顺  ...          None           26.699629
50     300033        同花顺  ...          None           10.924870
0      300033        同花顺  ...          None           77.597095
..        ...        ...  ...           ...                 ...
16     300033        同花顺  ...          None           47.873623
17     300033        同花顺  ...          None           42.495436

[66 rows x 45 columns]

SENTIMENT
舆情
风险舆情，提前预警相关风险，尽量规避风险。

舆情也只是做提醒作用，不做具体的推荐，具体的影响因子由自己的模型决定。

舆情-SENTIMENT
1. 近一个月的股票解禁列表
说明介绍
获取最近一个月的股票解禁数据

帮助提前规避解禁股票，对于大额解禁个股参考意义巨大。

调用方法
sentiment.stock_lifting_last_month()
Copied!
Copied!
Copied!
Copied!
输入参数
无

返回结果
字段	类型	注释	说明
stock_code	string	股票代码	300539
short_name	string	股票简称	横河精密
lift_date	date	解禁日期	1.1234
volume	decimal	解禁股数(股)	2023-06-05
amount	decimal	当前解禁市值(元)	根据当前价格计算
ratio	decimal	占总股本比例(%)	0.36
price	decimal	当前价格(元)	13.14
参考示例
import adata
df = adata.sentiment.stock_lifting_last_month()
print(df)
Copied!
Copied!
Copied!
Copied!
# 结果示例
    stock_code short_name   lift_date     volume      amount  ratio  price
0       605399       晨光新材  2023-08-04  201000000  3628000000  64.35  18.05
1       688385       复旦微电  2023-08-04    6000000   344000000   0.73  57.26
2       301038       深水规院  2023-08-04    6435000   121000000   3.75  18.85
3       603351       威尔药业  2023-08-04    1443500    39508900   1.07  27.37
4       301278       快可电子  2023-08-04   22698000  1431000000  27.28  63.04
..         ...        ...         ...        ...         ...    ...    ...
307     837092       汉鑫科技  2023-07-05     762000    11826200   1.59  15.52
308     430564       天润科技  2023-07-05     652900     3923700   0.88   6.01
309     873001       纬达光电  2023-07-05    7682800    51090700   5.00   6.65
310     002847       盐津铺子  2023-07-05     597000    49337300   0.30  82.64
311     301202       朗威股份  2023-07-05   32310100  1454000000  23.69  45.00

[312 rows x 7 columns]

Process finished with exit code 0
Copied!
Copied!
Copied!
Copied!
2. 融资融券余额数据
说明介绍
查询全市场融资融券余额数据

调用方法
sentiment.securities_margin()
Copied!
Copied!
Copied!
Copied!
输入参数
参数	类型	是否必填	说明
start_date	date	否	开始时间；例：2022-01-01，默认：最近一年的数据
返回结果
字段	类型	注释	说明
trade_date	date	交易日期	2023-07-21
rzye	decimal	融资余额（元）	1485586705452
rqye	decimal	融券余额（元）	90400227216
rzrqye	decimal	融资融券余额（元）	1575986932668
rzrqyecz	decimal	融资融券余额差值（元）	1575986932668
参考示例
import adata
df = adata.sentiment.securities_margin(start_date='2022-01-01')
print(df)
Copied!
Copied!
Copied!
Copied!
# 结果示例
     trade_date           rzye          rqye         rzrqye       rzrqyecz
0    2023-07-21  1485586705452   90400227216  1575986932668  1395186478236
1    2023-07-20  1490792912237   91309450769  1582102363006  1399483461468
..          ...            ...           ...            ...            ...
373  2022-01-05  1716321973743  113216333911  1829538307654  1603105639832
374  2022-01-04  1719028439852  115847621780  1834876061632  1603180818072

[375 rows x 5 columns]

Process finished with exit code 0
Copied!
Copied!
Copied!
Copied!
------------
北向资金（沪深港通）-NORTH
北向对A股的影响越来越大，该行情指标可作为一个影响因子

1. 北向实时流入行情
说明介绍
获取北向的实时流入行情

调用方法
sentiment.north.north_flow_current()
Copied!
Copied!
Copied!
Copied!
输入参数
无

返回结果
字段	类型	注释	说明
trade_time	datetime	交易时间	2023-06-01 09:30:00
net_hgt	decimal	沪港通净买入金额（元）	405050400
net_sgt	decimal	深港通净买入金额（元）	151704400
net_tgt	decimal	北向净买入金额（元）	556754800；沪港通和深港通合计
参考示例
import adata
df = adata.sentiment.north.north_flow_current()
print(df)
Copied!
Copied!
Copied!
Copied!
# 结果示例
             trade_time     net_hgt    net_sgt     net_tgt
0 2023-08-01 11:30:00  3506502400  558573500  4065075901

Process finished with exit code 0
Copied!
Copied!
Copied!
Copied!
2. 北向分时流入行情
说明介绍
获取北向的分时流入行情

调用方法
sentiment.north.north_flow_min()
Copied!
Copied!
Copied!
Copied!
输入参数
无

返回结果
字段	类型	注释	说明
trade_time	datetime	交易时间	2023-06-01 09:30:00
net_hgt	decimal	沪港通净买入金额（元）	405050400
net_sgt	decimal	深港通净买入金额（元）	151704400
net_tgt	decimal	北向净买入金额（元）	556754800；沪港通和深港通合计
参考示例
import adata
df = adata.sentiment.north.north_flow_min()
print(df)
Copied!
Copied!
Copied!
Copied!
# 结果示例
             trade_time     net_hgt    net_sgt     net_tgt
0   2023-08-01 09:30:00   405050400  151704400   556754800
1   2023-08-01 09:31:00   577789000  152477000   730266000
2   2023-08-01 09:32:00   495193200   10233700   505426900
..                  ...         ...        ...         ...
118 2023-08-01 11:28:00  4033936100  840697700  4874633800
119 2023-08-01 11:29:00  3506502400  558562100  4065064500
120 2023-08-01 11:30:00  3506502400  558573500  4065075901

[121 rows x 4 columns]
Process finished with exit code 0
Copied!
Copied!
Copied!
Copied!
2. 北向历史流入行情
说明介绍
获取北向的历史流入行情

最早日期：2017-01-01

调用方法
sentiment.north.north_flow()
Copied!
Copied!
Copied!
Copied!
输入参数
参数	类型	是否必填	说明
start_date	date	否	开始时间；例：2022-01-01，默认：最近30日的数据
返回结果
字段	类型	注释	说明
trade_date	date	交易时间	2023-06-01
net_hgt	decimal	沪港通净买入金额（元）	405050400；买入和卖出合计
buy_hgt	decimal	沪港通买入金额（元）	
buy_hgt	decimal	沪港通卖出金额（元）	
net_sgt	decimal	深港通净买入金额（元）	151704400；买入和卖出合计
buy_hgt	decimal	深港通买入金额（元）	
sell_sgt	decimal	深港通卖出金额（元）	
net_tgt	decimal	北向净买入金额（元）	556754800；沪港通和深港通合计
buy_tgt	decimal	北向买入金额（元）	
sell_tgt	decimal	北向卖出金额（元）	
参考示例
import adata
df = adata.sentiment.north.north_flow(start_date='2017-01-01')
print(df)
Copied!
Copied!
Copied!
Copied!
# 结果示例
      trade_date     net_hgt  ...      buy_tgt     sell_tgt
0     2023-07-31  6211770000  ...  78681680000  69334910000
1     2023-07-28  8021100000  ...  69420320000  53017340000
2     2023-07-27  2135580000  ...  51371700000  47624610000
...          ...         ...  ...          ...          ...
1522  2017-01-05   609840000  ...   2550420000   2942820000
1523  2017-01-04   583470000  ...   2525760000   3355420000
1524  2017-01-03   598660000  ...   2104810000   2161100000

[1525 rows x 10 columns]

Process finished with exit code 0
Copied!
Copied!
Copied!
Copied!
------------
热门榜-HOT
1. 东方财富人气榜TOP100
说明介绍
东方财富人气榜TOP100

每次调用都是最新的数据

调用方法
sentiment.hot.pop_rank_100_east()
Copied!
Copied!
Copied!
Copied!
输入参数
无

返回结果
字段	类型	注释	说明
rank	int	排名	100
stock_code	string	股票代码	000799
short_name	string	股票简称	酒鬼酒
price	decimal	最新价格	88
change	decimal	涨跌额	2
change_pct	decimal	涨跌幅（%）	10.0020
参考示例
import adata
df = adata.sentiment.hot.pop_rank_100_east()
print(df)
Copied!
Copied!
Copied!
Copied!
# 结果示例
    rank stock_code short_name  price    change  change_pct
0      1     301301       川宁生物  15.82  3.168746       20.03
1      2     600789       鲁抗医药   7.85  0.780290        9.94
2      3     002085       万丰奥威  16.20 -0.050220       -0.31
3      4     002670       国盛金控  12.72  0.830616        6.53
4      5     603739       蔚蓝生物  15.26  1.529052       10.02
..   ...        ...        ...    ...       ...         ...
95    96     000506      *ST中润   4.60 -0.228160       -4.96
96    97     603019       中科曙光  47.21 -0.467379       -0.99
97    98     603986       兆易创新  82.01  3.395214        4.14
98    99     002709       天赐材料  22.22  1.290982        5.81
99   100     300418       昆仑万维  41.28  1.341600        3.25

[100 rows x 6 columns]
Copied!
Copied!
Copied!
Copied!
2. 同花顺热股TOP100
说明介绍
同花顺热股TOP100

每次调用都是最新的数据

调用方法
sentiment.hot.hot_rank_100_ths()
Copied!
Copied!
Copied!
Copied!
输入参数
参数	类型	是否必填	说明
start_date	date	否	开始时间；例：2022-01-01，默认：最近30日的数据
返回结果
字段	类型	注释	说明
rank	int	排名	100
stock_code	string	股票代码	000799
short_name	string	股票简称	酒鬼酒
change_pct	decimal	涨跌幅（%）	10.002
hot_value	decimal	热度值	432509.0
pop_tag	string	人气标签	首板涨停
concept_tag	string	概念板块	白酒概念;国企改革
参考示例
import adata
df = adata.sentiment.hot.hot_rank_100_ths()
print(df)
Copied!
Copied!
Copied!
Copied!
# 结果示例
    rank stock_code short_name  change_pct  hot_value pop_tag       concept_tag
0      1     000099       中信海直      0.6284  4053176.0    持续上榜  飞行汽车(eVTOL);低空经济
1      2     002085       万丰奥威     -0.3077  2992777.0    持续上榜  飞行汽车(eVTOL);低空经济
2      3     601127        赛力斯     -1.0642  2966129.0     NaN         百度概念;华为概念
3      4     002590       万安科技      8.9172  2376913.0     NaN  低空经济;飞行汽车(eVTOL)
4      5     300107       建新股份     11.3866  2133884.0     NaN    飞行汽车(eVTOL);染料
..   ...        ...        ...         ...        ...     ...               ...
95    96     000858        五粮液      2.6923   441919.0     NaN         超级品牌;白酒概念
96    97     002035       华帝股份     10.0413   439802.0    首板涨停        家用电器;空气能热泵
97    98     300905        宝丽迪     19.9863   439383.0    首板涨停          光刻胶;节能环保
98    99     300829       金丹科技     20.0000   434725.0    首板涨停       可降解塑料;新材料概念
99   100     000799        酒鬼酒     10.0020   432509.0    首板涨停         白酒概念;国企改革

[100 rows x 7 columns]
Copied!
Copied!
Copied!
Copied!
3. 同花热门概念板块TOP20
说明介绍
同花热门概念板块TOP20

每次调用都是最新的数据

调用方法
sentiment.hot.hot_concept_20_ths()
Copied!
Copied!
Copied!
Copied!
输入参数
参数	类型	是否必填	说明
plate_type	int	否	板块类型：默认：1.概念板块；2.行业板块，默认：1.概念板块
返回结果
字段	类型	注释	说明
rank	int	排名	1
concept_code	string	概念代码	881157
concept_name	string	概念名称	证券
change_pct	decimal	涨跌幅（%）	0.2488
hot_value	decimal	热度值	1130204.5
hot_tag	string	连续351天上榜	热度标签
参考示例
import adata
df = adata.sentiment.hot.hot_concept_20_ths()
print(df)
Copied!
Copied!
Copied!
Copied!
# 结果示例
    rank concept_code concept_name  change_pct  hot_value   hot_tag
0      1       881157           证券      0.2488  1130204.5  连续351天上榜
1      2       881109         化学制品      4.5520   640911.5   10天9次上榜
2      3       881145           电力      2.4941   502987.5   连续62天上榜
3      4       881153        房地产开发      0.8296   460370.0  连续351天上榜
..   ...        ...        ...         ...        ...     ...         ...
16    17       881126        汽车零部件      1.8692   116196.0  连续217天上榜
17    18       881141           中药      1.9951   108051.5  连续351天上榜
18    19       881160        景点及旅游     -1.2024   103004.5   连续29天上榜
19    20       881166         国防军工      0.9097    95759.0   连续89天上榜

Process finished with exit code -1

Copied!
Copied!
Copied!
Copied!
--------------
龙虎榜-ALIST
1. 每日龙虎榜列表
说明介绍
获取相应日期的所有龙虎榜列表

调用方法
sentiment.hot.list_a_list_daily()
Copied!
Copied!
Copied!
Copied!
输入参数
参数	类型	是否必填	说明
report_date	date	是	报告日期：默认：当天；2024-07-01
返回结果
字段	类型	注释	说明
trade_date	int	交易日期	2024-07-01
short_name	string	股票简称	全新好
stock_code	string	股票代码	000007
close	decimal	收盘价(元)	5.16000
change_cpt	decimal	涨跌幅(%)	-9.94760
turnover_ratio	decimal	换手率(%)	8.37860
a_net_amount	decimal	龙虎榜净买入额(元)	4939641.98000
a_buy_amount	decimal	龙虎榜买入额(元)	23347567.29000
a_sell_amount	decimal	龙虎榜卖出额(元)	18407925.31000
a_amount	decimal	龙虎榜成交额(元)	41755492.60000
amount	decimal	总成交额(元)	137998593
net_amount_rate	decimal	龙虎榜净买额占总成交额比例(%)	3.57949
a_amount_rate	decimal	龙虎榜成交额占总成交额比例(%)	30.25791
reason	string	上榜原因	日跌幅偏离值达到7%的前5只证券
参考示例
import adata
df = adata.sentiment.hot.list_a_list_daily(report_date='2024-07-04')
print(df)
Copied!
Copied!
Copied!
Copied!
# 结果示例
    trade_date  ...                                    reason
0   2024-07-04  ...                          日跌幅偏离值达到7%的前5只证券
1   2024-07-04  ...  连续三个交易日内，涨幅偏离值累计达到12%的ST证券、*ST证券和未完成股改证券
2   2024-07-04  ...                          日跌幅偏离值达到7%的前5只证券
..         ...  ...                                       ...
59  2024-07-04  ...                          当日换手率达到20%的前5只股票
60  2024-07-04  ...                          当日换手率达到20%的前5只股票
61  2024-07-04  ...                          当日换手率达到20%的前5只股票

Copied!
Copied!
Copied!
Copied!
2. 单只股票龙虎榜信息
说明介绍
获取单只股票龙虎榜信息

注：先获取列表，再根据列表来获取

调用方法
sentiment.hot.get_a_list_info()
Copied!
Copied!
Copied!
Copied!
输入参数
参数	类型	是否必填	说明
report_date	date	否	报告日期：默认：当天；2024-07-01
stock_code	string	是	股票代码：600297
返回结果
字段	类型	注释	说明
trade_date	int	交易日期	2024-07-01
stock_code	string	股票代码	000007
operate_code	string	营业部代码	10678762
operate_name	string	营业部名称	东方财富证券股份有限公司拉萨金融城南环路证券营业部
a_net_amount	decimal	龙虎榜净买入额(元)	4939641.98000
a_buy_amount	decimal	龙虎榜买入额(元)	23347567.29000
a_sell_amount	decimal	龙虎榜卖出额(元)	18407925.31000
a_buy_amount_rate	decimal	龙虎榜买入额占总成交额比例(%)	3.57949
a_sell_amount_rate	decimal	龙虎榜卖出额占总成交额比例(%)	30.25791
reason	string	上榜原因	有价格涨跌幅限制的日收盘价格涨幅偏离值达到7%的前五只证券
参考示例
import adata
df = adata.sentiment.hot.get_a_list_info(stock_code='600297', report_date='2024-07-12')
print(df)
Copied!
Copied!
Copied!
Copied!
# 结果示例

    trade_date  ...                                  reason
3   2024-07-12  ...           有价格涨跌幅限制的日收盘价格涨幅偏离值达到7%的前五只证券
6   2024-07-12  ...           有价格涨跌幅限制的日收盘价格涨幅偏离值达到7%的前五只证券
7   2024-07-12  ...           有价格涨跌幅限制的日收盘价格涨幅偏离值达到7%的前五只证券
..         ...  ...                                       ...
17  2024-07-12  ...  非ST、*ST和S证券连续三个交易日内收盘价格涨幅偏离值累计达到20%的证券
18  2024-07-12  ...  非ST、*ST和S证券连续三个交易日内收盘价格涨幅偏离值累计达到20%的证券
19  2024-07-12  ...  非ST、*ST和S证券连续三个交易日内收盘价格涨幅偏离值累计达到20%的证券

[20 rows x 10 columns]

Copied!
Copied!
Copied!
Copied!
--------------
扫雷-MINE
1. 单只股票扫雷避险信息
说明介绍
获取单只股票扫雷避险信息

调用方法
sentiment.mine.mine_clearance_tdx()
Copied!
Copied!
Copied!
Copied!
输入参数
参数	类型	是否必填	说明
stock_code	string	是	股票代码：600811；st东方
返回结果
字段	类型	注释	说明
stock_code	string	股票代码	600811
short_name	string	股票简称	st东方
score	decimal	评分	1；总的评分，所有项一样
f_type	string	一级类别	财务类风险
s_type	string	二级类别	短期负债风险
t_type	string	三级类别	短期负债风险
reason	string	原因	东方集团：东方集团关于大股东部分股份被强制平仓的公告
参考示例
import adata
df = adata.sentiment.mine.mine_clearance_tdx(stock_code='600811')
print(df)
Copied!
Copied!
Copied!
Copied!
# 结果示例

   stock_code  ...                                             reason
0      600811  ...   截止至2024-09-30，货币资金占短期负债和一年内到期非流动负债不足40%，利息保障倍数<3
1      600811  ...                  截止2024-09-30,该公司财务费用高于归属母公司股东净利润。
2      600811  ...                     截止20240930，公司营业收入相比去年同期降幅超过50%
               ...                                        ... 
15     600811  ...                                   会计师事务所出具保留意见审计报告
16     600811  ...                                             股价小于1元
17     600811  ...                                              *ST股票

[18 rows x 6 columns]






