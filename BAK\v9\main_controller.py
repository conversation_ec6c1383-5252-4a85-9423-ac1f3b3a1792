"""
主控制器模块 - V7.0 四大心法完整版
负责程序调度、任务管理和主程序入口
严格遵循四大心法：周期为王、龙头不死、先手预判、数据为基

四大心法详解：
第一心法：周期为王，情绪为相 (总开关)
- 通过量化昨日涨停股今日表现（晋级率、核按钮率、平均溢价）评估市场情绪
- 输出明确的市场状态和建议仓位，作为所有买入决策的总开关

第二心法：龙头不死，主线不灭 (主战场)
- 通过分析当日涨停梯队和板块聚集度识别市场总龙头和主线战场
- 只有属于主线战场的股票才有资格进入评分环节

第三心法：先手预判，买在分歧 (信号生成)
- 建立统一的龙头战法评分体系，对主线战场内的股票进行量化打分
- 根据市场情绪动态调整买入信号的触发阈值

第四心法：数据为基，应对为上 (系统健壮性)
- 建立三级容灾机制：akshare -> adata -> TPDOG
- 对不同数据源的原始数据进行标准化处理
"""

import schedule
import time as time_module
import logging
import os
import pandas as pd
from datetime import datetime, time
from collections import defaultdict, Counter, deque
from tabulate import tabulate

# --- 股票过滤配置 ---
# 过滤8开头的股票（北交所等）
EXCLUDED_STOCK_PREFIXES = ['8']  # 可以根据需要添加更多前缀

# 导入分离的模块
from market_data_provider import (
    test_network_connection,
    load_stock_concept_cache,
    update_stock_concept_cache,
    is_trading_time,
    is_trading_day,
    get_last_trading_day,
    AKSHARE_AVAILABLE,
    get_stock_concepts,
    get_zt_pool_with_backup,
    get_current_stock_quotes,
    get_individual_fund_flow_with_backup,
    ak,
    get_sector_fund_flow_with_backup,
    convert_to_float
)
from theme_analyzer import (
    analyze_yesterday_limit_up_performance,
    analyze_intraday_flow_shift,
    get_stock_concepts,
    analyze_reseal_opportunities,
    analyze_concept_main_themes,
    analyze_industry_main_themes,
    analyze_capital_leader_signal,
    CAPITAL_LEADER_INFO
)
from signal_generator import (
    write_signal_file_atomically,
    task_scan_market_for_signals
)

# 导入必要的库
import requests
import json
import pickle

# --- 全局配置 ---
DEBUG_MODE = True
# DEBUG_MODE = False
#开启交易时间限制
ENABLE_TRADING_TIME_CHECK = False
NOTIFICATION_FILE = 'D:/flow_buy.ebk'
SELL_NOTIFICATION_FILE = 'D:/flow_sell.ebk'

# --- 运行模式配置 ---
RUN_MODE = 'REALTIME'  # 'REALTIME' 或 'BACKTEST'
BACKTEST_DATE = '20250718'

# --- 全局变量 ---
TODAY_NOTIFICATION_CACHE = {}
is_market_scan_running = False

# 【报告生成】新增全局变量，用于存储每日战报的核心数据
DAILY_REPORT_DATA = {
    'date': datetime.now().strftime('%Y-%m-%d'),
    'sentiment': {},
    'mainline': {},
    'yesterday_review': {},
    'intraday_flow': [],
    'limit_up_summary': {},
    'first_board_focus': {},
    'events': []
}

# --- 【新增】全局交易时间缓存，避免重复判断 ---
GLOBAL_TRADING_STATUS = {
    'is_trading_day': None,
    'is_trading_time': None,
    'last_check_time': None,
    'cache_duration': 60  # 缓存60秒
}

# --- 【第一心法优化】历史市场状态缓存，用于周期切换感知 ---
MARKET_HISTORY_CACHE = {
    'previous_sentiment': None,  # 前一日情绪评分
    'previous_zt_count': 0,      # 前一日涨停家数
    'previous_mainlines': [],    # 前一日主线情况
    'last_save_date': None       # 最后保存日期
}

# === 【新增】全局交易状态检查函数 ===
def get_global_trading_status():
    """
    获取全局交易状态，带缓存机制避免重复判断
    返回: (is_trading_day, is_trading_time)
    """
    global GLOBAL_TRADING_STATUS

    current_time = datetime.now()

    # 检查缓存是否有效
    if (GLOBAL_TRADING_STATUS['last_check_time'] is not None and
        (current_time - GLOBAL_TRADING_STATUS['last_check_time']).total_seconds() < GLOBAL_TRADING_STATUS['cache_duration']):
        # 使用缓存
        return GLOBAL_TRADING_STATUS['is_trading_day'], GLOBAL_TRADING_STATUS['is_trading_time']

    # 缓存过期或首次调用，重新判断
    try:
        trading_day = is_trading_day()
        trading_time = is_trading_time() if trading_day else False

        # 更新缓存
        GLOBAL_TRADING_STATUS.update({
            'is_trading_day': trading_day,
            'is_trading_time': trading_time,
            'last_check_time': current_time
        })

        return trading_day, trading_time

    except Exception as e:
        logging.error(f"获取交易状态失败: {e}")
        # 异常时返回保守值
        return False, False

def save_market_history_data(sentiment_data, zt_count, mainlines):
    """
    【第一心法优化】保存当日市场状态数据，供次日周期切换感知使用
    """
    global MARKET_HISTORY_CACHE

    try:
        current_date = datetime.now().strftime('%Y%m%d')

        # 更新内存缓存
        MARKET_HISTORY_CACHE.update({
            'previous_sentiment': sentiment_data,
            'previous_zt_count': zt_count,
            'previous_mainlines': mainlines,
            'last_save_date': current_date
        })

        # 保存到文件
        history_file = f"data/{current_date}/market_history_cache.json"
        os.makedirs(os.path.dirname(history_file), exist_ok=True)

        with open(history_file, 'w', encoding='utf-8') as f:
            json.dump(MARKET_HISTORY_CACHE, f, ensure_ascii=False, indent=2, default=str)

        logging.info(f"保存市场历史数据: {history_file}")

    except Exception as e:
        logging.error(f"保存市场历史数据失败: {e}")

def load_previous_market_history():
    """
    【第一心法优化】加载前一日市场状态数据
    """
    global MARKET_HISTORY_CACHE

    try:
        # 获取前一交易日
        from datetime import datetime, timedelta
        import akshare as ak

        # 简单的前一交易日计算
        today = datetime.now()
        previous_date = today - timedelta(days=1)

        # 如果是周末，往前推到周五
        while previous_date.weekday() >= 5:  # 5=Saturday, 6=Sunday
            previous_date = previous_date - timedelta(days=1)

        previous_date = previous_date.strftime('%Y%m%d')

        if not previous_date:
            return False

        history_file = f"data/{previous_date}/market_history_cache.json"

        if os.path.exists(history_file):
            with open(history_file, 'r', encoding='utf-8') as f:
                cached_data = json.load(f)
                MARKET_HISTORY_CACHE.update(cached_data)
                logging.info(f"成功加载前一日市场历史数据: {previous_date}")
                return True
        else:
            logging.info(f"前一日市场历史数据文件不存在: {history_file}")
            return False

    except Exception as e:
        logging.error(f"加载前一日市场历史数据失败: {e}")
        return False


# 文件: main_controller.py

def _find_resonance_mainlines(concept_themes, industry_themes):
    """
    【第二心法优化】寻找共振主线
    找出同时出现在概念和行业主线中的板块，这些是最强主线
    """
    if not concept_themes or not industry_themes:
        return []

    resonance_mainlines = []

    # 清理行业主线名称（去掉[行业]前缀）
    clean_industry_themes = []
    for theme in industry_themes:
        clean_name = theme.replace('[行业]', '') if theme.startswith('[行业]') else theme
        clean_industry_themes.append(clean_name)

    # 寻找概念和行业的交集
    for concept in concept_themes:
        # 直接匹配
        if concept in clean_industry_themes:
            resonance_mainlines.append(concept)
            continue

        # 模糊匹配（包含关系）
        for industry in clean_industry_themes:
            if concept in industry or industry in concept:
                # 避免重复添加
                if concept not in resonance_mainlines and industry not in resonance_mainlines:
                    resonance_mainlines.append(f"{concept}({industry})")  # 返回更清晰的共振对
                break

    # 【共振优化】不仅返回组合名，也返回原始的概念名，便于后续匹配
    # 例如，如果找到 "半导体(半导体及元件)"，则将 "半导体" 也加入共振列表
    original_concepts_in_resonance = []
    for res_theme in resonance_mainlines:
        # 提取括号前的概念名
        original_concept = res_theme.split('(')[0]
        if original_concept not in original_concepts_in_resonance:
            original_concepts_in_resonance.append(original_concept)

    # 合并原始概念和直接匹配的概念
    final_resonance_list = list(set(resonance_mainlines + original_concepts_in_resonance))

    return final_resonance_list

def _calculate_auction_score(stock_code):
    """
    【第三心法优化】计算竞价强度评分
    """
    try:
        from market_data_provider import get_stocks_auction_info_with_cache
        from datetime import datetime

        today_str = datetime.now().strftime('%Y%m%d')
        auction_data = get_stocks_auction_info_with_cache([stock_code], today_str)

        # 修复：正确判断返回数据（函数返回DataFrame）
        if auction_data is None or auction_data.empty:
            return {'score': 0, 'reasons': []}

        # 查找对应股票代码的数据
        stock_code_6 = str(stock_code).zfill(6)
        matching_rows = auction_data[auction_data['代码'].astype(str).str.zfill(6) == stock_code_6]

        if matching_rows.empty:
            return {'score': 0, 'reasons': []}

        # 取第一行数据
        auction_info = matching_rows.iloc[0].to_dict()

        score = 0
        reasons = []

        # 根据实际字段进行评分
        # 竞价涨幅评分（对应 '竞价涨幅' 字段）
        auction_change = auction_info.get('竞价涨幅', 0) or 0
        try:
            auction_change = float(auction_change)
            if auction_change > 7:  # 竞价涨幅 > 7%
                score += 2
                reasons.append("高开强势")
            elif auction_change > 3:  # 竞价涨幅 > 3%
                score += 1
                reasons.append("高开积极")
        except (ValueError, TypeError):
            pass

        # 竞价金额评分（对应 '竞价金额' 字段）
        auction_amount = auction_info.get('竞价金额', 0) or 0
        try:
            auction_amount = float(auction_amount)
            if auction_amount > 100000000:  # 竞价金额 > 1亿
                score += 3
                reasons.append("游资抢筹")
            elif auction_amount > 50000000:  # 竞价金额 > 5000万
                score += 1
                reasons.append("竞价活跃")
        except (ValueError, TypeError):
            pass

        # 竞价换手率评分（对应 '竞价换手率' 字段）
        auction_turnover = auction_info.get('竞价换手率', 0) or 0
        try:
            auction_turnover = float(auction_turnover)
            if auction_turnover > 5:  # 竞价换手率 > 5%
                score += 2
                reasons.append("换手活跃")
            elif auction_turnover > 2:  # 竞价换手率 > 2%
                score += 1
                reasons.append("有资金关注")
        except (ValueError, TypeError):
            pass

        return {'score': score, 'reasons': reasons}

    except Exception as e:
        logging.warning(f"计算竞价评分失败 {stock_code}: {e}")
        return {'score': 0, 'reasons': []}

def _calculate_intraday_score(stock_code):
    """
    【第三心法优化】计算分时强度评分
    """
    try:
        from market_data_provider import get_stocks_intraday_pattern
        from datetime import datetime

        today_str = datetime.now().strftime('%Y%m%d')
        intraday_data = get_stocks_intraday_pattern([stock_code], today_str)

        # 修复：正确判断返回数据（函数返回DataFrame）
        if intraday_data is None or intraday_data.empty:
            return {'score': 0, 'reasons': []}

        # 查找对应股票代码的数据
        stock_code_6 = str(stock_code).zfill(6)
        matching_rows = intraday_data[intraday_data['代码'].astype(str).str.zfill(6) == stock_code_6]

        if matching_rows.empty:
            return {'score': 0, 'reasons': []}

        # 取第一行数据
        intraday_info = matching_rows.iloc[0].to_dict()

        score = 0
        reasons = []

        # 根据实际字段进行评分
        # 基于开盘价、最高价、最低价、收盘价、昨收进行分时强度评估

        try:
            open_price = float(intraday_info.get('开盘价', 0) or 0)
            high_price = float(intraday_info.get('最高价', 0) or 0)
            low_price = float(intraday_info.get('最低价', 0) or 0)
            close_price = float(intraday_info.get('收盘价', 0) or 0)
            yesterday_close = float(intraday_info.get('昨收', 0) or 0)

            if all([open_price, high_price, low_price, close_price, yesterday_close]):
                # 计算涨跌幅
                change_pct = (close_price - yesterday_close) / yesterday_close * 100

                # 计算振幅
                amplitude = (high_price - low_price) / yesterday_close * 100

                # 强势评分：收盘价接近最高价
                if close_price / high_price > 0.95:  # 收盘价在最高价95%以上
                    score += 3
                    reasons.append("强势封板")
                elif close_price / high_price > 0.85:  # 收盘价在最高价85%以上
                    score += 1
                    reasons.append("走势强势")

                # 开盘强度评分
                open_change = (open_price - yesterday_close) / yesterday_close * 100
                if open_change > 5:  # 高开5%以上
                    score += 2
                    reasons.append("开盘强势")
                elif open_change > 2:  # 高开2%以上
                    score += 1
                    reasons.append("开盘积极")

                # 振幅控制评分（振幅过大说明分歧大）
                if amplitude > 15:  # 振幅超过15%
                    score -= 2
                    reasons.append("分歧巨大")
                elif amplitude > 10:  # 振幅超过10%
                    score -= 1
                    reasons.append("有分歧")

        except (ValueError, TypeError):
            # 如果数据转换失败，给予基础评分
            pass

        return {'score': score, 'reasons': reasons}

    except Exception as e:
        logging.warning(f"计算分时评分失败 {stock_code}: {e}")
        return {'score': 0, 'reasons': []}

# === 数据保存相关函数 ===
def get_date_folder():
    """获取按日期命名的文件夹路径（如 data/20250721）"""
    date_str = datetime.now().strftime('%Y%m%d')
    folder = os.path.join('data', date_str)
    os.makedirs(folder, exist_ok=True)
    return folder

def save_raw_data(data, filename_prefix, data_type="csv"):
    """
    保存原始数据到按日期命名的文件夹

    Args:
        data: 要保存的数据（DataFrame或dict）
        filename_prefix: 文件名前缀
        data_type: 数据类型 ("csv", "json", "pkl")
    """
    try:
        date_folder = get_date_folder()
        timestamp = datetime.now().strftime('%H%M%S')

        if data_type == "csv" and isinstance(data, pd.DataFrame):
            filename = f"{filename_prefix}_{timestamp}.csv"
            filepath = os.path.join(date_folder, filename)
            data.to_csv(filepath, index=False, encoding='utf-8-sig')
            print(f"✅ 保存原始数据: {filepath}")
            logging.info(f"保存原始数据: {filepath}")

        elif data_type == "json":
            filename = f"{filename_prefix}_{timestamp}.json"
            filepath = os.path.join(date_folder, filename)
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)
            print(f"✅ 保存原始数据: {filepath}")
            logging.info(f"保存原始数据: {filepath}")

        elif data_type == "pkl":
            filename = f"{filename_prefix}_{timestamp}.pkl"
            filepath = os.path.join(date_folder, filename)
            with open(filepath, 'wb') as f:
                pickle.dump(data, f)
            print(f"✅ 保存原始数据: {filepath}")
            logging.info(f"保存原始数据: {filepath}")

    except Exception as e:
        logging.error(f"保存原始数据失败 [{filename_prefix}]: {e}")
        print(f"❌ 保存原始数据失败 [{filename_prefix}]: {e}")

def save_analysis_report(report_data, report_name):
    """
    保存分析报告到按日期命名的文件夹

    Args:
        report_data: 报告数据（dict或str）
        report_name: 报告名称
    """
    try:
        date_folder = get_date_folder()
        timestamp = datetime.now().strftime('%H%M%S')

        # 保存为JSON格式
        filename = f"{report_name}_{timestamp}.json"
        filepath = os.path.join(date_folder, filename)

        if isinstance(report_data, str):
            # 如果是字符串，包装成dict
            report_data = {
                'report_content': report_data,
                'timestamp': datetime.now().isoformat(),
                'report_name': report_name
            }
        elif isinstance(report_data, dict):
            # 添加时间戳
            report_data['timestamp'] = datetime.now().isoformat()
            report_data['report_name'] = report_name

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2, default=str)

        print(f"✅ 保存分析报告: {filepath}")
        logging.info(f"保存分析报告: {filepath}")

        # 同时保存为文本格式便于查看
        txt_filename = f"{report_name}_{timestamp}.txt"
        txt_filepath = os.path.join(date_folder, txt_filename)

        with open(txt_filepath, 'w', encoding='utf-8') as f:
            if isinstance(report_data.get('report_content'), str):
                f.write(report_data['report_content'])
            else:
                f.write(json.dumps(report_data, ensure_ascii=False, indent=2, default=str))

        print(f"✅ 保存文本报告: {txt_filepath}")
        logging.info(f"保存文本报告: {txt_filepath}")

    except Exception as e:
        logging.error(f"保存分析报告失败 [{report_name}]: {e}")
        print(f"❌ 保存分析报告失败 [{report_name}]: {e}")

# === 四大心法核心数据结构 ===
# 第一心法：市场情绪总开关数据
MARKET_SENTIMENT_DATA = {
    'status': '混沌期',  # 强势期、分化期、混沌期、冰点期
    'position_ratio': 0.5,  # 建议仓位 0.0-1.0
    'score_threshold': 12,  # 动态阈值
    'last_update_time': None
}

# 第二心法：主线战场数据
MAINLINE_BATTLEFIELD_DATA = {
    'market_leader': {'code': None, 'name': None, 'boards': 0},
    'strong_sectors': [],  # 主线战场板块列表
    'limit_up_ladder': {},  # 涨停梯队分布
    'last_update_time': None
}

# 第三心法：龙头战法评分缓存
LEADER_STRATEGY_CACHE = {}

# 第四心法：数据源状态监控
DATA_SOURCE_STATUS = {
    'akshare': True,
    'adata': False,
    'tpdog': False,
    'last_check_time': None
}

def _adjust_sentiment_with_flow_shift(sentiment_data, rising_sectors):
    """
    【V8.0 新增】基于资金流突变动态修正市场情绪
    当资金攻击榜出现高分板块时，认为市场找到新突破口，情绪转暖
    """
    if rising_sectors is None or rising_sectors.empty:
        return sentiment_data

    # 检查是否有突变分超过0.8且排名变化巨大的新板块
    top_rising = rising_sectors.head(1)
    if not top_rising.empty:
        top_score = top_rising.iloc[0]['shift_score']
        rank_change = top_rising.iloc[0]['rank_change']

        if top_score > 0.8 and rank_change > 10:
            # 动态提升仓位建议
            original_ratio = sentiment_data['position_ratio']
            adjusted_ratio = min(1.0, original_ratio + 0.2)  # 大胆提升20%仓位

            print(f"🔥 检测到强势资金突变，动态调整仓位: {original_ratio:.1%} -> {adjusted_ratio:.1%}")

            # 更新全局数据
            MARKET_SENTIMENT_DATA['position_ratio'] = adjusted_ratio
            sentiment_data['position_ratio'] = adjusted_ratio

    return sentiment_data

# --- 配置日志 ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('stock_monitor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# ===== 四大心法核心函数实现 =====

def assess_market_sentiment_v7(yesterday_zt_pool_df=None, today_zt_pool_df=None, yesterday_str=None, today_str=None):
    """
    第一心法：周期为王，情绪为相 (总开关)
    通过量化昨日涨停股今日表现，综合评估市场情绪状态

    Args:
        yesterday_zt_pool_df: DataFrame, 昨日涨停股池数据（可选，避免重复获取）
        today_zt_pool_df: DataFrame, 今日涨停股池数据（可选，避免重复获取）
        yesterday_str: str, 昨日日期字符串（可选）
        today_str: str, 今日日期字符串（可选）

    Returns:
        dict: {
            'status': str,  # 强势期、分化期、混沌期、冰点期
            'position_ratio': float,  # 建议仓位 0.0-1.0
            'score_threshold': int,  # 买入信号动态阈值
            'metrics': dict  # 详细指标
        }
    """
    global MARKET_SENTIMENT_DATA

    try:
        print("\n🎯 第一心法：市场情绪总开关评估...")

        # 【第一心法优化】加载前一日市场历史数据
        load_previous_market_history()

        # 获取昨日涨停股今日表现数据
        from datetime import datetime, timedelta
        from market_data_provider import get_last_trading_day

        # 如果没有传入日期参数，则计算
        if yesterday_str is None or today_str is None:
            current_date = datetime.now()
            if is_trading_day():
                today_str = current_date.strftime("%Y%m%d")
                yesterday_str = get_last_trading_day()
            else:
                # 非交易日使用最近的两个交易日
                today_str = get_last_trading_day()
                yesterday_str = get_last_trading_day(today_str)

        # 调用昨日涨停股复盘分析（传入已获取的数据，避免重复调用）
        review_result = analyze_yesterday_limit_up_performance(yesterday_str, today_str, yesterday_zt_pool_df, today_zt_pool_df)

        if not review_result or 'market_sentiment' not in review_result:
            print("❌ 无法获取昨日涨停股表现数据，使用默认情绪评估")
            return _get_default_sentiment()

        metrics = review_result['market_sentiment']
        success_rate = metrics.get('success_rate', 0)
        nuclear_rate = metrics.get('nuclear_rate', 0)
        avg_premium = metrics.get('avg_premium', 0)
        premium_std = metrics.get('premium_std', 0)

        # 情绪评估逻辑
        status, position_ratio, score_threshold = _calculate_market_status(
            success_rate, nuclear_rate, avg_premium, premium_std
        )

        # 更新全局数据
        MARKET_SENTIMENT_DATA.update({
            'status': status,
            'position_ratio': position_ratio,
            'score_threshold': score_threshold,
            'last_update_time': datetime.now()
        })

        print(f"📊 市场情绪评估结果:")
        print(f"   状态: {status}")
        print(f"   建议仓位: {position_ratio:.1%}")
        print(f"   买入阈值: {score_threshold}分")
        print(f"   晋级率: {success_rate:.1f}% | 核按钮率: {nuclear_rate:.1f}%")

        # 【新增】保存市场情绪数据
        sentiment_data = {
            'status': status,
            'position_ratio': position_ratio,
            'score_threshold': score_threshold,
            'metrics': metrics,
            'review_result': review_result
        }
        save_analysis_report(sentiment_data, "market_sentiment_analysis")

        # 【第一心法优化】保存当日市场状态数据供次日使用
        try:
            zt_count = len(today_zt_pool_df) if today_zt_pool_df is not None and not today_zt_pool_df.empty else 0
            mainlines = MAINLINE_BATTLEFIELD_DATA.get('strong_sectors', [])
            save_market_history_data(sentiment_data, zt_count, mainlines)
        except Exception as e:
            logging.warning(f"保存市场历史数据失败: {e}")

        return sentiment_data

    except Exception as e:
        logging.error(f"市场情绪评估失败: {e}")
        print(f"❌ 市场情绪评估失败: {e}")
        return _get_default_sentiment()

def _calculate_market_status(success_rate, nuclear_rate, avg_premium, premium_std):
    """
    【V8.0 游资心法版】根据关键指标计算市场状态
    - 结合晋级率、核按钮率、平均溢价、市场总龙头表现进行综合评判
    - 【第一心法优化】加入周期切换感知机制
    """
    global MARKET_HISTORY_CACHE

    # 1. 核心指标量化
    # 晋级率评分 (权重最高)
    promotion_score = (success_rate / 100) * 10

    # 亏钱效应评分 (一票否决项)
    fear_score = (1 - min(1, nuclear_rate / 20)) * 10  # 核按钮率超过20%即为0分

    # 赚钱效应评分
    premium_score = 0
    if avg_premium > 3: premium_score = 10
    elif avg_premium > 1: premium_score = 8
    elif avg_premium > 0: premium_score = 6
    elif avg_premium > -2: premium_score = 4
    else: premium_score = 2

    # 2. 综合情绪分计算
    # 权重: 晋级强度 > 亏钱效应 > 赚钱效应
    sentiment_score = promotion_score * 0.5 + fear_score * 0.3 + premium_score * 0.2

    # 3. 【第一心法优化】周期切换感知机制
    previous_sentiment = MARKET_HISTORY_CACHE.get('previous_sentiment', {})
    if previous_sentiment:
        prev_status = previous_sentiment.get('status', '')

        # 检查是否为早盘时间（9:25-9:40）
        current_time = datetime.now().time()
        is_early_morning = time(9, 25) <= current_time <= time(9, 40)

        if prev_status == "冰点期" and is_early_morning:
            # 获取当前涨停家数进行周期切换判断
            try:
                from market_data_provider import get_limit_up_pool_with_backup
                today_zt_df = get_limit_up_pool_with_backup()
                current_zt_count = len(today_zt_df) if today_zt_df is not None and not today_zt_df.empty else 0

                # 检查连板股是否被核按钮
                high_board_nuclear = nuclear_rate > 10  # 连板股核按钮率超过10%

                if current_zt_count > 15 and not high_board_nuclear:
                    # 周期切换信号：前日冰点 + 今日早盘涨停家数>15 + 连板股未被核按钮
                    sentiment_score = min(sentiment_score + 2.0, 10.0)  # 强制向上修正一个等级
                    print(f"🔥 周期切换感知：前日{prev_status} → 今日早盘涨停{current_zt_count}家且连板未核按钮，情绪分+2.0！")

            except Exception as e:
                logging.warning(f"周期切换感知检查失败: {e}")

    # 4. 龙头表现修正 (一票否决/一票加成)
    leader_boards = MAINLINE_BATTLEFIELD_DATA.get('market_leader', {}).get('boards', 0)
    if leader_boards >= 7:
        sentiment_score = max(sentiment_score, 8.0) # 市场有绝对龙头，情绪不会差
        print("🔥 龙头修正：市场高度打开，情绪分强制提升！")
    elif nuclear_rate > 15:
        sentiment_score = min(sentiment_score, 3.0) # 核按钮率过高，强制降低情绪分
        print("🧊 龙头修正：亏钱效应放大，情绪分强制降低！")

    # 5. 根据情绪分输出状态和策略
    if sentiment_score >= 8.0:
        return "强势期", 1.0, 8  # 仓位100%，阈值8分
    elif sentiment_score >= 6.0:
        return "分化期", 0.7, 10 # 仓位70%，阈值10分
    elif sentiment_score >= 4.0:
        return "混沌期", 0.4, 12 # 仓位40%，阈值12分
    else:
        return "冰点期", 0.1, 999 # 仓位10%，只看不做

def _get_default_sentiment():
    """
    获取默认情绪评估结果
    """
    return {
        'status': '混沌期',
        'position_ratio': 0.4,
        'score_threshold': 12,
        'metrics': {}
    }

def _adjust_sentiment_with_flow_shift(sentiment_data, rising_sectors):
    """
    【V8.0 新增】基于资金流突变动态修正市场情绪
    当资金攻击榜出现高分板块时，认为市场找到新突破口，情绪转暖
    """
    if rising_sectors is None or rising_sectors.empty:
        return sentiment_data

    # 检查是否有突变分超过0.8且排名变化巨大的新板块
    top_rising = rising_sectors.head(1)
    if not top_rising.empty:
        top_score = top_rising.iloc[0]['shift_score']
        rank_change = top_rising.iloc[0]['rank_change']

        if top_score > 0.8 and rank_change > 10:
            # 动态提升仓位建议
            original_ratio = sentiment_data['position_ratio']
            adjusted_ratio = min(1.0, original_ratio + 0.1)  # 最多提升到100%

            print(f"🔥 检测到强势资金突变，动态调整仓位: {original_ratio:.1%} -> {adjusted_ratio:.1%}")

            # 更新全局数据
            MARKET_SENTIMENT_DATA['position_ratio'] = adjusted_ratio
            sentiment_data['position_ratio'] = adjusted_ratio

    return sentiment_data

def analyze_today_mainline_and_limit_ups_v7(rising_sectors=None, today_zt_pool_df=None):
    """
    第二心法：龙头不死，主线不灭 (主战场)
    通过分析当日涨停梯队和板块聚集度，识别市场总龙头和主线战场

    Args:
        rising_sectors: DataFrame, 资金攻击榜数据 (V8.0新增)
        today_zt_pool_df: DataFrame, 今日涨停股池数据（可选，避免重复获取）

    Returns:
        dict: {
            'market_leader': dict,  # 市场总龙头信息
            'strong_sectors': list,  # 主线战场板块列表
            'limit_up_ladder': dict  # 涨停梯队分布
        }
    """
    global MAINLINE_BATTLEFIELD_DATA

    try:
        print("\n👑 第二心法：主线战场识别...")

        # 1. 获取今日涨停池数据（如果没有传入，则获取）
        if today_zt_pool_df is None:
            from market_data_provider import get_zt_pool_with_backup
            from datetime import datetime as dt

            # 获取当前交易日期
            if is_trading_day():
                today_str = dt.now().strftime("%Y%m%d")
            else:
                today_str = get_last_trading_day()

            zt_pool_df = get_zt_pool_with_backup(today_str)
        else:
            zt_pool_df = today_zt_pool_df

        if zt_pool_df is None or zt_pool_df.empty:
            print("❌ 无法获取涨停池数据")
            return _get_default_mainline_data()

        # 2. 分析涨停梯队分布
        limit_up_ladder = _analyze_limit_up_ladder(zt_pool_df)

        # 3. 识别市场总龙头（最高板）
        market_leader = _identify_market_leader(zt_pool_df)

        # 4. 分析板块聚集度，识别主线战场
        strong_sectors = _identify_strong_sectors(zt_pool_df, rising_sectors)

        # 5. 更新全局数据
        MAINLINE_BATTLEFIELD_DATA.update({
            'market_leader': market_leader,
            'strong_sectors': strong_sectors,
            'limit_up_ladder': limit_up_ladder,
            'last_update_time': datetime.now()
        })

        # 6. 输出结果
        print(f"📊 主线战场识别结果:")
        if market_leader['code']:
            print(f"   市场总龙头: {market_leader['name']}({market_leader['code']}) - {market_leader['boards']}板")
        else:
            print(f"   市场总龙头: 无明确龙头")

        print(f"   主线战场板块({len(strong_sectors)}个):")
        for i, sector in enumerate(strong_sectors[:5]):  # 显示前5个
            print(f"     {i+1}. {sector['name']} - {sector['count']}只涨停")

        print(f"   涨停梯队分布: ", end="")
        for boards, count in sorted(limit_up_ladder.items(), reverse=True):
            if count > 0:
                print(f"{boards}板({count}只) ", end="")
        print()

        # 【新增】保存主线战场数据
        mainline_data = {
            'market_leader': market_leader,
            'strong_sectors': strong_sectors,
            'limit_up_ladder': limit_up_ladder
        }
        save_analysis_report(mainline_data, "mainline_battlefield_analysis")

        # 【新增】保存涨停股池原始数据
        if zt_pool_df is not None and not zt_pool_df.empty:
            save_raw_data(zt_pool_df, "limit_up_pool", "csv")

        return mainline_data

    except Exception as e:
        logging.error(f"主线战场识别失败: {e}")
        print(f"❌ 主线战场识别失败: {e}")
        return _get_default_mainline_data()

def _analyze_limit_up_ladder(zt_pool_df):
    """
    分析涨停梯队分布
    """
    ladder = defaultdict(int)

    for _, row in zt_pool_df.iterrows():
        # 使用标准化后的中文字段名
        boards = row.get('连板数', 1)
        if pd.isna(boards):
            boards = 1
        ladder[int(boards)] += 1

    return dict(ladder)

def _identify_market_leader(zt_pool_df):
    """
    识别市场总龙头（最高板）
    """
    if zt_pool_df.empty:
        return {'code': None, 'name': None, 'boards': 0}

    # 找到连板数最高的股票
    max_boards = 0
    leader_info = {'code': None, 'name': None, 'boards': 0}

    for _, row in zt_pool_df.iterrows():
        # 使用标准化后的中文字段名
        boards = row.get('连板数', 1)
        if pd.isna(boards):
            boards = 1
        boards = int(boards)

        if boards > max_boards:
            max_boards = boards
            leader_info = {
                'code': str(row['代码']).zfill(6),
                'name': row['名称'],
                'boards': boards
            }

    return leader_info

def _identify_strong_sectors(zt_pool_df, rising_sectors=None):
    """
    识别主线战场板块（涨停家数最多的板块）

    Args:
        zt_pool_df: DataFrame, 涨停股池数据
        rising_sectors: DataFrame, 资金攻击榜数据（可选）
    """
    # 统计各概念/行业的涨停家数
    concept_counts = defaultdict(int)
    industry_counts = defaultdict(int)

    for _, row in zt_pool_df.iterrows():
        # 使用标准化后的中文字段名
        stock_code = str(row['代码']).zfill(6)

        # 统计概念
        try:
            concepts = get_stock_concepts(stock_code)
            for concept in concepts:
                concept_counts[concept] += 1
        except:
            pass

        # 统计行业
        try:
            industry = row.get('所属行业', '')
            if industry:
                industry_counts[industry] += 1
        except:
            pass

    # 合并概念和行业，选出涨停家数最多的板块
    strong_sectors = []

    # 添加概念板块
    for concept, count in concept_counts.items():
        if count >= 2:  # 至少2只涨停才算强势板块
            strong_sectors.append({
                'name': concept,
                'type': '概念',
                'count': count
            })

    # 添加行业板块
    for industry, count in industry_counts.items():
        if count >= 2:
            strong_sectors.append({
                'name': industry,
                'type': '行业',
                'count': count
            })

    # 【V8.0 新增】动态加入资金攻击榜前三名板块
    if rising_sectors is not None and not rising_sectors.empty:
        rising_top3 = rising_sectors.head(3)
        for _, row in rising_top3.iterrows():
            sector_name = row['名称']
            # 检查是否已经在强势板块列表中
            if not any(s['name'] == sector_name for s in strong_sectors):
                strong_sectors.append({
                    'name': sector_name,
                    'type': '概念',
                    'count': 1,  # 临时加入，设置为1
                    'source': '资金攻击榜'  # 标记来源
                })
                print(f"🔥 动态加入资金攻击榜板块: {sector_name}")

    # 按涨停家数排序
    strong_sectors.sort(key=lambda x: x['count'], reverse=True)

    return strong_sectors[:10]  # 返回前10个强势板块

def _get_default_mainline_data():
    """
    获取默认主线数据
    """
    return {
        'market_leader': {'code': None, 'name': None, 'boards': 0},
        'strong_sectors': [],
        'limit_up_ladder': {}
    }

def _calculate_stock_score_v7(stock_info, mainline_data, sentiment_data, rising_sectors=None):
    """
    【升级版】第三心法：先手预判，买在分歧 (信号生成)
    统一的龙头战法评分函数，对主线战场内的股票进行量化打分
    新增：先锋卡位分、封单强度分，精准锁定市场辨识度最高的真龙
    【共振优化】：新增共振主线评分项，赋予最高权重

    Args:
        stock_info: dict, 股票基本信息
        mainline_data: dict, 主线战场数据 (包含'resonance_themes'键)
        sentiment_data: dict, 市场情绪数据
        rising_sectors: DataFrame, 资金攻击榜数据 (V8.0新增)

    Returns:
        dict: {
            'score': int,  # 总评分
            'reasons': list,  # 得分原因
            'is_qualified': bool  # 是否达到买入阈值
        }
    """
    try:
        score = 0
        reasons = []

        stock_code = stock_info.get('code', '')
        stock_name = stock_info.get('name', '')
        boards = stock_info.get('boards', 1)
        first_seal_time = stock_info.get('first_seal_time', '15:00:00')
        seal_fund = stock_info.get('seal_fund', 0)
        circulating_market_cap = stock_info.get('circulating_market_cap', 0)

        # === 硬性约束：只有主线战场内的股票才能评分 ===
        stock_concepts = get_stock_concepts(stock_code)
        stock_industry = stock_info.get('industry', '')

        strong_sector_names = [s['name'] for s in mainline_data['strong_sectors']]
        is_in_mainline = any(concept in strong_sector_names for concept in stock_concepts) or \
                        stock_industry in strong_sector_names

        if not is_in_mainline:
            return {'score': 0, 'reasons': ['不在主线战场'], 'is_qualified': False}

        # === 【新增】资金总龙头评分 (最高优先级之一) ===
        if CAPITAL_LEADER_INFO['code'] == stock_code:
            score += 12  # 给予超高12分，几乎必定触发
            reasons.append("🔥资金总龙头🔥")

        # === 【共振优化】共振主线评分 (最高优先级) ===
        resonance_themes = mainline_data.get('resonance_themes', [])
        is_in_resonance = any(concept in resonance_themes for concept in stock_concepts)
        if is_in_resonance:
            score += 8  # 给予8分重奖，代表最高确定性
            reasons.append("🔥行业概念共振🔥")

        # === 龙头地位评分 ===
        market_leader = mainline_data['market_leader']

        # 市场总龙头（最高板）：+10分
        if stock_code == market_leader.get('code'):
            score += 10
            reasons.append("市场总龙头")

        # 板块龙头判断：在所属板块内是否为最高板
        elif _is_sector_leader(stock_code, stock_concepts, boards):
            score += 7
            reasons.append("板块龙头")

        # 2板晋3板（市场核心辨识度）：+6分
        if boards == 3:
            score += 6
            reasons.append("3板核心")
        elif boards == 2:
            score += 4
            reasons.append("2板进阶")

        # === 【新增】先锋卡位评分 (体现速度) ===
        # 10:00前封板的都是强势板，给予加分
        if first_seal_time and first_seal_time < '10:00:00':
            score += 4
            reasons.append(f"早盘卡位({first_seal_time})")
        elif first_seal_time and first_seal_time < '11:00:00':
            score += 2
            reasons.append(f"盘中卡位({first_seal_time})")

        # === 【新增】封单强度评分 (体现共识) ===
        if circulating_market_cap > 0 and seal_fund > 0:
            seal_ratio = seal_fund / circulating_market_cap
            if seal_ratio > 0.1:  # 封单占流通市值超过10%，极强
                score += 5
                reasons.append(f"封单极强({seal_ratio:.1%})")
            elif seal_ratio > 0.05:  # 封单占流通市值超过5%，很强
                score += 3
                reasons.append(f"封单很强({seal_ratio:.1%})")

        # === 主线强度评分 ===
        # 属于当日资金最认可、涨停家数最多的主战场板块：+5分
        if mainline_data['strong_sectors']:
            top_sector = mainline_data['strong_sectors'][0]
            if any(concept == top_sector['name'] for concept in stock_concepts) or \
               stock_industry == top_sector['name']:
                score += 5
                reasons.append(f"最强板块({top_sector['name']})")

        # 所在板块内有市场总龙头：+3分
        if market_leader.get('code') and any(
            concept in [s['name'] for s in mainline_data['strong_sectors'][:3]]
            for concept in stock_concepts
        ):
            score += 3
            reasons.append("龙头板块")

        # === 资金强度与预期差评分 ===
        # 这里简化处理，实际应该结合资金流数据
        if boards >= 2:  # 连板股通常有资金关注
            score += 3
            reasons.append("连板资金")

        # === 价格行为评分 ===
        # 简化处理，实际应该结合分时数据
        if boards == 1:  # 首板更有想象空间
            score += 2
            reasons.append("首板启动")

        # === 【第三心法优化】竞价强度评分 ===
        auction_score = _calculate_auction_score(stock_code)
        score += auction_score['score']
        if auction_score['reasons']:
            reasons.extend(auction_score['reasons'])

        # === 【第三心法优化】分时强度评分 ===
        intraday_score = _calculate_intraday_score(stock_code)
        score += intraday_score['score']
        if intraday_score['reasons']:
            reasons.extend(intraday_score['reasons'])

        # === 【V8.0 新增】资金攻击榜评分 ===
        if rising_sectors is not None and not rising_sectors.empty:
            # 获取资金攻击榜前三名板块
            rising_sectors_list = rising_sectors.head(3)['名称'].tolist()
            is_in_accelerating_theme = any(concept in rising_sectors_list for concept in stock_concepts)

            if is_in_accelerating_theme:
                score += 8  # 给予8分重奖，代表这是在分歧转一致的临界点介入
                reasons.append("新兴主线+资金加速")

        # === 【新增】暗转明评分 ===
        # 检查股票所属概念是否在暗线观察池中且出现了首板涨停
        from theme_analyzer import DARK_LINE_OBSERVATION_POOL
        try:
            # 检查股票的概念是否有暗转明信号
            for concept in stock_concepts:
                if concept in DARK_LINE_OBSERVATION_POOL.get('sectors', {}):
                    # 如果是首板且该概念之前在暗线观察池中，给予极高加分
                    if boards == 1:  # 首板涨停
                        score += 8  # 直接+8分，理由是"主线预期+资金点火"
                        reasons.append("暗转明信号")
                        break
        except Exception as e:
            logging.warning(f"暗转明评分计算失败: {e}")

        # 判断是否达到买入阈值
        threshold = sentiment_data.get('score_threshold', 12)
        is_qualified = score >= threshold

        return {
            'score': score,
            'reasons': reasons,
            'is_qualified': is_qualified
        }

    except Exception as e:
        logging.error(f"股票评分计算失败: {e}")
        return {'score': 0, 'reasons': ['评分失败'], 'is_qualified': False}

def _is_sector_leader(stock_code, stock_concepts, stock_boards):
    """
    判断是否为板块龙头（在所属板块内是否为最高板）
    """
    try:
        # 简化实现：如果是3板以上，认为是板块龙头
        return stock_boards >= 3
    except:
        return False

# 文件: main_controller.py

def generate_buy_signals_v7(mainline_data, sentiment_data, rising_sectors=None, today_zt_pool_df=None, reseal_df=None):
    """
    第三心法：先手预判，买在分歧 (信号生成)
    基于四大心法生成高质量买入信号

    Args:
        mainline_data: dict, 主线战场数据
        sentiment_data: dict, 市场情绪数据
        rising_sectors: DataFrame, 资金攻击榜数据 (V8.0新增)
        today_zt_pool_df: DataFrame, 今日涨停股池数据（可选，避免重复获取）
        reseal_df: DataFrame, 烂板回封数据（V8.0新增）

    Returns:
        list: 买入信号列表
    """
    try:
        print("\n� 第三心法：龙头战法信号生成...")

        # 检查总开关：冰点期不生成任何买入信号
        if sentiment_data['status'] == '冰点期':
            print("🧊 市场处于冰点期，总开关关闭，不生成买入信号")
            return []

        # 获取涨停池数据作为候选股票（如果没有传入，则获取）
        if today_zt_pool_df is None:
            from datetime import datetime

            # 获取当前交易日期
            if is_trading_day():
                today_str = datetime.now().strftime("%Y%m%d")
            else:
                today_str = get_last_trading_day()

            zt_pool_df = get_zt_pool_with_backup(today_str)
        else:
            zt_pool_df = today_zt_pool_df

        if zt_pool_df is None or zt_pool_df.empty:
            print("❌ 无法获取涨停池数据")
            return []

        buy_signals = []
        qualified_stocks = []

        # 1. 【最高优先级】处理烂板回封信号
        if reseal_df is not None and not reseal_df.empty:
            print("\n  🎯 处理高优先级 [烂板回封] 信号...")
            for _, row in reseal_df.head(3).iterrows(): # 取强度分最高的前3名
                signal = f"{row['代码']},{row['名称']},分歧转一致,{row['回封强度分']}分-{row['原因']}"
                buy_signals.append(signal)
                print(f"    🔥  {signal}")

        # 2. 处理原有的龙头战法评分 (可以增加逻辑避免重复添加)
        existing_codes = {s.split(',')[0] for s in buy_signals}

        # 对每只涨停股进行评分
        for _, row in zt_pool_df.iterrows():
            stock_info = {
                'code': str(row['代码']).zfill(6),
                'name': row['名称'],
                'boards': row.get('连板数', 1),
                'industry': row.get('所属行业', ''),
                'turnover_rate': row.get('换手率', 0),
                'seal_fund': row.get('封单资金', 0)
            }

            # 如果已经是回封信号，则不再重复添加
            if stock_info['code'] in existing_codes:
                continue

            # 计算评分
            score_result = _calculate_stock_score_v7(stock_info, mainline_data, sentiment_data, rising_sectors)

            if score_result['is_qualified']:
                qualified_stocks.append({
                    'stock_info': stock_info,
                    'score_result': score_result
                })

        # 按评分排序
        qualified_stocks.sort(key=lambda x: x['score_result']['score'], reverse=True)

        # 生成买入信号
        for item in qualified_stocks[:10]:  # 最多选择前10只
            stock_info = item['stock_info']
            score_result = item['score_result']

            signal = f"{stock_info['code']},{stock_info['name']},龙头战法,{score_result['score']}分-{'+'.join(score_result['reasons'])}"
            buy_signals.append(signal)

        if buy_signals:
            print(f"✅ 生成 {len(buy_signals)} 个龙头战法买入信号:")
            for i, signal in enumerate(buy_signals):
                print(f"   {i+1}. {signal}")
        else:
            print("� 暂无符合龙头战法条件的买入信号")

        return buy_signals

    except Exception as e:
        logging.error(f"买入信号生成失败: {e}")
        print(f"❌ 买入信号生成失败: {e}")
        return []

def generate_catch_up_signals(mainline_data, sentiment_data):
    """
    【V8.0 乘胜追击版】生成主线内部的补涨/加仓信号
    心法："强者恒强，后排补涨"。当主线得到市场反复确认后，
    如果龙头一字板或封单巨大无法买入，资金就会去挖掘板块内形态好、有辨识度的后排个股
    """
    print("\n" + "🚀" * 15 + " 捕捉主线补涨机会 " + "🚀" * 15)

    # 核心心法：只在情绪稳定、主线明确时发动追击
    if sentiment_data['status'] not in ['强势期', '分化期']:
        print("  市场情绪不稳，不考虑补涨机会。")
        return []

    if not mainline_data['strong_sectors']:
        print("  主线板块未确立，无法寻找补涨目标。")
        return []

    # 心法二：10:30后市场方向更明确，是发动攻击的好时机
    current_time = datetime.now().time()
    target_time = datetime.strptime('10:30', '%H:%M').time()
    if current_time < target_time:
        print("  未到10:30，盘面分歧较大，等待方向明确。")
        return []

    try:
        # 获取最强的1-2个主线板块
        top_sectors = [s['name'] for s in mainline_data['strong_sectors'][:2]]
        print(f"  🎯 锁定最强主线: {', '.join(top_sectors)}")

        # 获取全市场实时行情数据
        all_quotes = get_current_stock_quotes()
        if all_quotes is None or all_quotes.empty:
            print("  ❌ 无法获取实时行情数据")
            return []

        # 筛选候选股
        candidates = []
        for _, row in all_quotes.iterrows():
            stock_code = str(row['代码']).zfill(6)
            change_pct = row.get('涨跌幅', 0)
            volume_ratio = row.get('量比', 0)

            # 【新增】过滤8开头的股票
            if any(stock_code.startswith(prefix) for prefix in EXCLUDED_STOCK_PREFIXES):
                continue

            # 条件1: 涨幅在 +5% 到 +9.8% 之间 (即将冲击涨停)
            if not (5 < change_pct < 9.8):
                continue

            # 条件2: 属于最强主线板块
            stock_concepts = get_stock_concepts(stock_code)
            if not any(concept in top_sectors for concept in stock_concepts):
                continue

            # 条件3: 量比放大 (量在价先)
            if volume_ratio < 2.0:
                continue

            candidates.append({
                'code': stock_code,
                'name': row.get('名称', ''),
                'change_pct': change_pct,
                'volume_ratio': volume_ratio,
                'concepts': stock_concepts
            })

        if not candidates:
            print("  暂未发现符合条件的补涨候选股。")
            return []

        print(f"  ✅ 发现 {len(candidates)} 只补涨候选股，进行评分排序...")

        # 此处可以加入更复杂的评分，例如结合分时图的攻击形态等
        # 简化处理：按涨幅排序，最高的作为信号
        candidates.sort(key=lambda x: x['change_pct'], reverse=True)

        best_candidate = candidates[0]
        matching_concept = next((concept for concept in best_candidate['concepts'] if concept in top_sectors), best_candidate['concepts'][0] if best_candidate['concepts'] else '未知')
        reason = f"主线补涨,{matching_concept},涨幅{best_candidate['change_pct']:.1f}%,量比{best_candidate['volume_ratio']:.1f}"
        signal = f"{best_candidate['code']},{best_candidate['name']},{reason}"

        print(f"  🔥 生成补涨信号: {signal}")
        return [signal]

    except Exception as e:
        logging.error(f"生成补涨信号失败: {e}")
        print(f"❌ 生成补涨信号失败: {e}")
        return []

def check_data_source_status():
    """
    第四心法：数据为基，应对为上 (系统健壮性)
    检查各数据源状态，确保数据获取的稳定性
    """
    global DATA_SOURCE_STATUS

    try:
        print("\n🔧 第四心法：数据源状态检查...")

        # 检查akshare状态
        try:
            if ak is not None:
                test_df = ak.tool_trade_date_hist_sina()
                DATA_SOURCE_STATUS['akshare'] = test_df is not None and not test_df.empty
            else:
                DATA_SOURCE_STATUS['akshare'] = False
        except:
            DATA_SOURCE_STATUS['akshare'] = False

        # 检查adata状态（如果已安装）
        try:
            import adata
            test_df = adata.stock.info.all_code()
            DATA_SOURCE_STATUS['adata'] = test_df is not None and not test_df.empty
        except:
            DATA_SOURCE_STATUS['adata'] = False

        # 检查TPDOG状态
        try:
            response = requests.get('https://www.tpdog.com/api/hs/stocks/list?type=sz', timeout=5)
            DATA_SOURCE_STATUS['tpdog'] = response.status_code == 200
        except:
            DATA_SOURCE_STATUS['tpdog'] = False

        DATA_SOURCE_STATUS['last_check_time'] = datetime.now()

        # 输出状态
        print(f"📊 数据源状态:")
        print(f"   akshare: {'✅' if DATA_SOURCE_STATUS['akshare'] else '❌'}")
        print(f"   adata: {'✅' if DATA_SOURCE_STATUS['adata'] else '❌'}")
        print(f"   TPDOG: {'✅' if DATA_SOURCE_STATUS['tpdog'] else '❌'}")

        # 如果所有数据源都不可用，发出警告
        if not any(DATA_SOURCE_STATUS[key] for key in ['akshare', 'adata', 'tpdog']):
            print("⚠️ 警告：所有数据源都不可用，系统将使用缓存数据运行")
            logging.warning("所有数据源都不可用")

        return DATA_SOURCE_STATUS

    except Exception as e:
        logging.error(f"数据源状态检查失败: {e}")
        print(f"❌ 数据源状态检查失败: {e}")
        return DATA_SOURCE_STATUS

def run_continuous_task(task_name, job_func):
    """
    【新增】连续任务执行器，任务完成后立即触发下一个任务
    """
    import threading
    global TASK_RUNNING, LAST_TASK_TIMES

    def wrapped_job():
        global TASK_RUNNING
        try:
            print(f"🔄 开始执行任务: {task_name}")
            start_time = datetime.now()

            job_func()

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            LAST_TASK_TIMES[task_name] = end_time

            print(f"✅ 任务完成: {task_name} (耗时 {duration:.1f}秒)")

        except Exception as e:
            logging.error(f"连续任务执行异常 [{task_name}]: {e}", exc_info=True)
            print(f"❌ 连续任务执行异常 [{task_name}]: {e}")
            # 确保任务状态标志被正确重置
            global is_market_scan_running
            if task_name in ['four_methods', 'traditional_scan']:
                is_market_scan_running = False
        finally:
            TASK_RUNNING = False
            # 【关键】任务完成后立即触发下一个任务
            trigger_next_continuous_task()

    if not TASK_RUNNING:
        TASK_RUNNING = True
        job_thread = threading.Thread(target=wrapped_job)
        job_thread.daemon = True
        job_thread.start()
    else:
        print(f"⏳ 任务 {task_name} 跳过，其他任务正在执行中")

def run_threaded(job_func):
    """
    【保留】传统线程执行器，用于定时任务
    """
    import threading

    def wrapped_job():
        try:
            job_func()
        except Exception as e:
            logging.error(f"后台任务执行异常: {e}", exc_info=True)
            print(f"❌ 后台任务执行异常: {e}")

    job_thread = threading.Thread(target=wrapped_job)
    job_thread.daemon = True
    job_thread.start()

def trigger_next_continuous_task():
    """
    【修复】触发下一个连续任务
    循环轮转执行所有任务，无时间间隔限制
    """
    global CURRENT_TASK_INDEX

    # 【修复】使用全局交易状态，避免重复判断
    # 如果开启了交易时间限制，则检查交易时间
    if ENABLE_TRADING_TIME_CHECK:
        trading_day, trading_time = get_global_trading_status()
        if not (trading_day and trading_time):
            current_time = datetime.now().strftime('%H:%M:%S')
            print(f"⏰ 非交易时间（当前时间: {current_time}），跳过连续任务执行")
            return

    # 【修改】任务配置列表 - 按执行顺序排列
    task_configs = [
        {
            'name': 'position_scan',
            'func': task_scan_positions_for_sell_signals,
        },
        {
            'name': 'four_methods',
            'func': task_scan_market_with_four_methods_v7,
        },
        {
            'name': 'mainline_analysis',
            'func': task_mainline_analysis,
        },
        {
            'name': 'limit_up_review',
            'func': task_limit_up_performance_review,
        },
        {
            'name': 'traditional_scan',
            'func': task_scan_market_for_signals,
        }
    ]

    # 【修复】循环轮转执行任务
    if task_configs:
        current_task = task_configs[CURRENT_TASK_INDEX]
        task_name = current_task['name']
        task_func = current_task['func']

        print(f"⚡ 触发下一个任务: {task_name} (第{CURRENT_TASK_INDEX + 1}/{len(task_configs)}个)")

        # 更新任务索引，实现循环轮转
        CURRENT_TASK_INDEX = (CURRENT_TASK_INDEX + 1) % len(task_configs)

        run_continuous_task(task_name, task_func)

# task_scan_market_for_signals 函数已移至 signal_generator.py 模块

# 文件: main_controller.py

# 文件: main_controller.py

def task_scan_market_with_four_methods_v7():
    """
    【V7.1 游资心法版】主循环任务
    严格遵循四大心法的完整交易系统，并修正了主线识别逻辑
    """
    global is_market_scan_running, DAILY_REPORT_DATA

    # === 前置检查 ===
    if is_market_scan_running:
        logging.warning("市场扫描任务仍在运行中，跳过本次调度。")
        return

    is_market_scan_running = True
    task_start_time = datetime.now()
    logging.info(f"--- [四大心法V7.0任务开始] @ {task_start_time.strftime('%H:%M:%S')} ---")

    try:
        print("\n" + "=" * 80)
        print("🎯 【四大心法交易系统 V7.0】启动")
        print("=" * 80)

        # 【报告生成】检查日期，如果新的一天开始了，重置报告数据
        today_str_report = datetime.now().strftime('%Y-%m-%d')
        if DAILY_REPORT_DATA['date'] != today_str_report:
            DAILY_REPORT_DATA = {
                'date': today_str_report, 'sentiment': {}, 'mainline': {},
                'yesterday_review': {}, 'intraday_flow': [],
                'limit_up_summary': {}, 'first_board_focus': {}, 'events': []
            }
            print(f"新的一天，已重置每日战报数据: {today_str_report}")

        # === 统一获取涨停股池数据，供后续所有模块使用 ===
        print("\n📊 获取涨停股池数据...")
        if is_trading_day():
            today_str = datetime.now().strftime("%Y%m%d")
            yesterday_str = get_last_trading_day()
        else:
            today_str = get_last_trading_day()
            yesterday_str = get_last_trading_day(today_str)

        today_zt_pool_df = get_zt_pool_with_backup(today_str)
        yesterday_zt_pool_df = get_zt_pool_with_backup(yesterday_str)

        # === 第四心法：数据基石检查 ===
        print("\n� 第四心法：数据基石稳固检查...")
        data_status = check_data_source_status()

        # === 第一心法：市场情绪总开关 ===
        print("\n🎯 第一心法：市场情绪总开关评估...")
        sentiment_data = assess_market_sentiment_v7(yesterday_zt_pool_df, today_zt_pool_df, yesterday_str, today_str)
        DAILY_REPORT_DATA['sentiment'] = sentiment_data  # 【报告生成】记录情绪数据

        # === 【新增】盘中核心：识别资金总龙头 ===
        print("\n💰 识别资金总龙头 (资金断层分析)...")
        ranked_fund_flow_df = get_individual_fund_flow_with_backup("个股资金流")
        analyze_capital_leader_signal(ranked_fund_flow_df)  # 该函数会更新全局变量 CAPITAL_LEADER_INFO

        # === 【V8.0 新增】盘中资金流突变监控 ===
        print("\n" + "⚡" * 15 + " 盘中资金流突变监控 " + "⚡" * 15)
        rising_sectors, fading_sectors = analyze_intraday_flow_shift()
        # 【报告生成】记录资金流突变
        if rising_sectors is not None and not rising_sectors.empty:
            DAILY_REPORT_DATA['intraday_flow'].append({
                'time': datetime.now().strftime('%H:%M'),
                'rising': rising_sectors.head(3).to_dict('records')
            })

        # 【新增】保存资金流突变数据
        if rising_sectors is not None and not rising_sectors.empty:
            save_raw_data(rising_sectors, "rising_sectors", "csv")
        if fading_sectors is not None and not fading_sectors.empty:
            save_raw_data(fading_sectors, "fading_sectors", "csv")

        # === 【游资视角优化】主线辨析 (投机 vs 趋势) ===
        print("\n" + "🧭" * 15 + " 主线辨析 (投机 vs 趋势) " + "🧭" * 15)
        try:
            # 数据准备
            concept_fund_flow_df = get_sector_fund_flow_with_backup("概念资金流")

            # 1. 投机主线分析 (核心看涨停共振)
            speculative_mainlines = []
            if today_zt_pool_df is not None and not today_zt_pool_df.empty:
                zt_concepts = defaultdict(list)
                for _, row in today_zt_pool_df.iterrows():
                    stock_code = str(row['代码']).zfill(6)
                    # 过滤8开头的股票
                    if any(stock_code.startswith(prefix) for prefix in EXCLUDED_STOCK_PREFIXES):
                        continue
                    concepts = get_stock_concepts(stock_code)
                    for concept in concepts:
                        zt_concepts[concept].append({
                            'name': row['名称'],
                            'boards': int(row.get('连板数', 1))
                        })

                # 筛选出有板块效应的主线 (至少3家涨停 或 有3板以上龙头)
                for concept, stocks in zt_concepts.items():
                    max_boards = max(s['boards'] for s in stocks) if stocks else 0
                    if len(stocks) >= 3 or max_boards >= 3:
                        speculative_mainlines.append({
                            'name': concept,
                            'limit_ups': len(stocks),
                            'leader_height': max_boards
                        })

                # 排序
                if speculative_mainlines:
                    speculative_mainlines.sort(key=lambda x: (x['limit_ups'], x['leader_height']), reverse=True)

            # 2. 趋势主线分析 (核心看资金流入)
            trend_mainlines = []
            if concept_fund_flow_df is not None and not concept_fund_flow_df.empty:
                # 确保数据格式正确
                concept_fund_flow_df['今日主力净流入-净额'] = concept_fund_flow_df['今日主力净流入-净额'].apply(
                    convert_to_float)
                concept_fund_flow_df['今日涨跌幅'] = concept_fund_flow_df['今日涨跌幅'].apply(convert_to_float)

                top_fund_flow = concept_fund_flow_df.head(10)
                for _, row in top_fund_flow.iterrows():
                    sector = row['名称']
                    flow = row['今日主力净流入-净额']
                    change = row['今日涨跌幅']
                    limit_ups = len(zt_concepts.get(sector, []))

                    # 趋势主线判定：资金流入巨大 & 板块波动不大 & 涨停家数少
                    if flow > 15 * 100_000_000 and change < 3 and limit_ups <= 2:
                        trend_mainlines.append({
                            'name': sector,
                            'flow': flow,
                            'change': change
                        })

            # 3. 输出辨析结论
            print("核心结论：市场主线特征辨析")
            if speculative_mainlines:
                top_spec_mainline = speculative_mainlines[0]
                print(f"投机主线 (我的主战场): 【{top_spec_mainline['name']}】及其延伸")
                print(
                    f"证据: 涨停家数 {top_spec_mainline['limit_ups']}家, 板块龙头高度 {top_spec_mainline['leader_height']}板，形成强大板块共振。这是情绪之所在，是热钱之所向。")
            else:
                print("投机主线：今日盘面无明显投机主线，热点散乱。")

            if trend_mainlines:
                top_trend_mainline = trend_mainlines[0]
                print(f"趋势主线 (机构的压舱石): 【{top_trend_mainline['name']}】")
                print(
                    f"证据: 板块资金净流入高达 {top_trend_mainline['flow'] / 1e8:.2f}亿，但板块涨幅仅 {top_trend_mainline['change']:.2f}%，波动率低。这是机构资金的战场，追求趋势确定性。")
            else:
                print("趋势主线：今日无明显大资金流入的趋势板块。")

            print("我的视角：在“投机主线”这片烈火烹油的战场结束前，分心去啃“趋势主线”这块硬骨头，是战略上的失误。")
            print("🧭" * 30)

        except Exception as e:
            print(f"❌ 主线辨析分析失败: {e}")
            logging.error(f"主线辨析分析失败: {e}")

        # === 第二心法：主线战场识别 (观潮法V5-合力版驱动) ===
        print("\n👑 第二心法：主线战场识别 (观潮法V5-合力版驱动)...")
        concept_themes = analyze_concept_main_themes(sentiment_data, today_zt_pool_df)
        industry_themes = analyze_industry_main_themes(sentiment_data, today_zt_pool_df)

        # 【新增】寻找共振主线
        resonance_mainlines = _find_resonance_mainlines(concept_themes, industry_themes)

        # 更新全局变量和mainline_data
        mainline_data = {
            'market_leader': MAINLINE_BATTLEFIELD_DATA['market_leader'],
            'strong_sectors': [{'name': s, 'count': 0} for s in list(set(concept_themes + industry_themes))],
            'limit_up_ladder': _analyze_limit_up_ladder(today_zt_pool_df),
            'resonance_themes': resonance_mainlines,
        }

        # 【新增】烂板回封狙击模块
        reseal_df = analyze_reseal_opportunities()
        if reseal_df is not None and not reseal_df.empty:
            print("\n🔥🔥🔥【分歧转一致监控 (烂板回封)】🔥🔥🔥")
            print(tabulate(reseal_df, headers='keys', tablefmt='psql', showindex=False, floatfmt=".2f"))
            save_raw_data(reseal_df, "reseal_opportunities", "csv")

        if rising_sectors is not None and not rising_sectors.empty:
            display_rising = rising_sectors[
                ['名称', 'current_rank', 'rank_change', 'flow_increment', 'shift_score']].head(5)
            display_rising.columns = ['板块名称', '当前排名', '排名变化', '5分钟净流入(万)', '突变分']
            # 【修复】flow_increment经过单位一致性处理后已经是万元单位，不需要再除以10000
            display_rising['5分钟净流入(万)'] = display_rising['5分钟净流入(万)'].round(2)

            print("\n🔥🔥🔥【资金攻击榜 TOP 5】🔥🔥🔥")
            print(tabulate(display_rising, headers='keys', tablefmt='psql', showindex=False, floatfmt=".2f"))

        if fading_sectors is not None and not fading_sectors.empty:
            display_fading = fading_sectors[['名称', 'current_rank', 'rank_change', 'flow_increment']].head(3)
            display_fading.columns = ['板块名称', '当前排名', '排名变化', '5分钟净流入(万)']
            # 【修复】同样不需要除以10000
            display_fading['5分钟净流入(万)'] = display_fading['5分钟净流入(万)'].round(2)

            print("\n🧊🧊🧊【资金流出榜 TOP 3】🧊🧊🧊")
            print(tabulate(display_fading, headers='keys', tablefmt='psql', showindex=False))

        # === 【V8.0 新增】基于资金流突变动态修正市场情绪 ===
        sentiment_data = _adjust_sentiment_with_flow_shift(sentiment_data, rising_sectors)

        # === 第三心法：龙头战法信号生成 ===
        buy_signals = generate_buy_signals_v7(mainline_data, sentiment_data, rising_sectors, today_zt_pool_df, reseal_df)

        # === 写入信号文件 ===
        if buy_signals:
            unique_signals = sorted(list(set(buy_signals)))
            write_signal_file_atomically(NOTIFICATION_FILE, unique_signals, overwrite=True)
            print(f"✅ 成功写入 {len(unique_signals)} 个买入信号到 {NOTIFICATION_FILE}")
            save_analysis_report({'signals': unique_signals}, "buy_signals")
        else:
            print("📝 暂无符合四大心法条件的买入信号")
            write_signal_file_atomically(NOTIFICATION_FILE, [], overwrite=True)

        print("\n✅ 四大心法V7.0扫描任务完成")

    except Exception as e:
        logging.error(f"四大心法V7.0扫描任务异常: {e}", exc_info=True)
        print(f"❌ 四大心法V7.0扫描任务异常: {e}")
    finally:
        is_market_scan_running = False
        task_end_time = datetime.now()
        duration = (task_end_time - task_start_time).total_seconds()
        logging.info(f"--- [四大心法V7.0任务结束] 耗时 {duration:.1f}秒 ---")



def generate_buy_signals(sentiment_result, limit_up_result):
    """
    根据分析结果生成买入信号
    """
    try:
        signals = []
        
        # 基于龙头信息生成信号
        if limit_up_result.get('leader_info'):
            leader = limit_up_result['leader_info']
            if leader.get('boards', 0) >= 2:  # 至少2板
                signal = f"{leader['code']},{leader['name']},龙头信号,{leader['boards']}板"
                signals.append(signal)
        
        # 基于热门板块生成信号
        hot_sectors = limit_up_result.get('hot_sectors', [])
        for sector in hot_sectors[:3]:  # 前3个热门板块
            if sector['count'] >= 3:  # 至少3只涨停
                # 选择板块内的代表股票
                for stock_info in sector['stocks'][:1]:  # 每个板块选1只
                    if '(' in stock_info and ')' in stock_info:
                        stock_name = stock_info.split('(')[0]
                        stock_code = stock_info.split('(')[1].replace(')', '')
                        signal = f"{stock_code},{stock_name},板块信号,{sector['name']}"
                        signals.append(signal)
        
        # 写入信号文件
        if signals:
            write_signal_file_atomically(NOTIFICATION_FILE, signals)
            print(f"📝 生成 {len(signals)} 个买入信号")
            for signal in signals:
                print(f"   {signal}")
        else:
            print("📝 暂无符合条件的买入信号")
            
    except Exception as e:
        logging.error(f"生成买入信号失败: {e}")
        print(f"❌ 生成买入信号失败: {e}")

def evaluate_sell_condition(main_net, main_ratio, super_net, super_ratio, large_net, large_ratio,
                           medium_net, medium_ratio, small_net, small_ratio, historical_data):
    """
    【移植自原始版本】根据输入的资金流数据和历史数据，评估是否满足卖出条件
    返回: 一个元组 (is_sell_signal, reasons)
          is_sell_signal: 布尔值，True表示满足卖出条件
          reasons: 列表，包含满足条件的具体原因
    """
    from market_data_provider import convert_to_float

    if not historical_data.empty:
        min_main_net = historical_data['main_net_inflow'].min()
        down_days = historical_data[historical_data['change_pct'] < 0]
        avg_down_main_net = down_days['main_net_inflow'].mean() if not down_days.empty else 0
        limit_down_days = historical_data[historical_data['change_pct'] <= -9.9]
        avg_limit_down_main_net = limit_down_days['main_net_inflow'].mean() if not limit_down_days.empty else 0

    # 基础卖出条件
    sell_conditions = {
        '主力净流出>5000万或净占比<-5%': main_net < -5000 or main_ratio < -5,
        '超大单净占比<-5%且大单净占比<-2%': super_ratio < -5 and large_ratio < -2,
        '全为流出且主力流出>散户流入': (main_net < 0 and super_net < 0 and large_net < 0 and
                                    medium_net < 0 and small_net < 0 and abs(main_net) > abs(small_net)),
        '至少三个净占比为负': sum([main_ratio < 0, super_ratio < 0, large_ratio < 0,
                                medium_ratio < 0, small_ratio < 0]) >= 3,
        '主力及超大单净占比为负且流出>5000万': (main_ratio < 0 and super_ratio < 0 and
                                              (main_net < -5000 or super_net < -5000)),
        '至少两个净占比为负且主力/超大单/大单净额较大': (sum([main_ratio < 0, super_ratio < 0, large_ratio < 0]) >= 2 and
                                                    (main_net < -3000 or super_net < -3000 or large_net < -3000))
    }

    # 历史数据相关的额外卖出条件
    additional_sell_conditions = {}
    if not historical_data.empty:
        additional_sell_conditions = {
            '主力净流出绝对值>历史最大流出': abs(main_net) > abs(min_main_net),
            '主力净流出>下跌日均值': main_net < avg_down_main_net,
            '主力净流出绝对值>跌停日均值2倍': abs(main_net) > abs(avg_limit_down_main_net) * 2
        }

    # 卖出条件映射
    sell_conditions_map = {
        12: all(sell_conditions.values()),
        1: sell_conditions['主力净流出>5000万或净占比<-5%'],
        2: sell_conditions['超大单净占比<-5%且大单净占比<-2%'],
        3: sell_conditions['全为流出且主力流出>散户流入'],
        4: sell_conditions['至少三个净占比为负'] and (additional_sell_conditions.get('主力净流出>下跌日均值', True)),
        5: sell_conditions['主力及超大单净占比为负且流出>5000万'],
        6: sell_conditions['至少两个净占比为负且主力/超大单/大单净额较大'] and (additional_sell_conditions.get('主力净流出>下跌日均值', True)),
        7: additional_sell_conditions.get('主力净流出绝对值>历史最大流出', False) or additional_sell_conditions.get('主力净流出绝对值>跌停日均值2倍', False),
        8: additional_sell_conditions.get('主力净流出>下跌日均值', False)
    }

    # 检查满足的条件
    satisfied_conditions = [str(k) for k, v in sell_conditions_map.items() if v]

    return len(satisfied_conditions) > 0, satisfied_conditions

def task_scan_positions_for_sell_signals():
    """
    【完整实现】扫描持仓生成卖出信号
    移植自原始get_all_capital_flow_east_mod.py的完整逻辑
    """
    global is_market_scan_running

    if is_market_scan_running:
        logging.warning("市场扫描任务仍在运行中，跳过持仓检查。")
        print("市场扫描任务仍在运行中，跳过持仓检查。")
        return

    try:
        print("\n🔍 扫描持仓生成卖出信号...")

        positions_file = 'D:/current_positions.txt'
        sell_signals = []
        sell_details = []

        checked_count = 0
        sell_count = 0
        no_sell_count = 0
        error_count = 0

        # 检查持仓文件是否存在
        if not os.path.exists(positions_file):
            logging.warning("持仓文件 D:/current_positions.txt 不存在，跳过持仓检查。")
            print("持仓文件 D:/current_positions.txt 不存在，跳过持仓检查。")
            return

        # 读取持仓股票
        with open(positions_file, 'r', encoding='utf-8') as f:
            held_stocks = [line.strip() for line in f if line.strip()]

        if not held_stocks:
            logging.info("持仓文件为空，不执行持仓股卖出检查。")
            print("持仓文件为空，不执行持仓股卖出检查。")
            return

        print(f"📋 读取到 {len(held_stocks)} 只持仓股票")

        # 处理每只持仓股票
        for stock_code_full in held_stocks:
            try:
                checked_count += 1
                stock_code_6_digit = stock_code_full.replace('sz', '').replace('sh', '').replace('bj', '').zfill(6)

                # 【新增】过滤8开头的股票
                if any(stock_code_6_digit.startswith(prefix) for prefix in EXCLUDED_STOCK_PREFIXES):
                    print(f"⚠️  跳过8开头股票: {stock_code_6_digit}")
                    continue

                # 获取股票名称（简化处理）
                stock_name = f"股票{stock_code_6_digit}"

                # 获取资金流数据
                from market_data_provider import convert_to_float
                df_flow = None

                # 【新增】检查是否在交易时间内，决定接口优先级
                from datetime import datetime
                now = datetime.now()
                current_time = now.time()

                # 交易时间：09:30~11:30 和 13:00~15:00
                morning_start = datetime.strptime("09:30", "%H:%M").time()
                morning_end = datetime.strptime("11:30", "%H:%M").time()
                afternoon_start = datetime.strptime("13:00", "%H:%M").time()
                afternoon_end = datetime.strptime("15:00", "%H:%M").time()

                is_trading_time = (morning_start <= current_time <= morning_end) or \
                                 (afternoon_start <= current_time <= afternoon_end)
                is_weekday = now.weekday() < 5

                # 确定市场代码
                if stock_code_6_digit.startswith(('600', '601', '603', '605', '688')):
                    market = 'sh'
                else:
                    market = 'sz'

                # 【修改】盘中优先使用TPDog，盘后优先使用akshare
                # 【关键修复】初始化df_flow变量
                df_flow = None

                if is_trading_time and is_weekday:
                    # 盘中优先使用TPDog接口
                    try:
                        from market_data_provider import load_tpdog_token, get_tpdog_stock_fund_flow
                        tpdog_token = load_tpdog_token()
                        if tpdog_token:
                            logging.info(f"[盘中优先] 使用TPDog获取持仓股 {stock_code_full} 的资金流数据")
                            tpdog_df = get_tpdog_stock_fund_flow(tpdog_token, stock_code_6_digit)
                            if tpdog_df is not None and not tpdog_df.empty:
                                # 转换TPDog数据格式以匹配akshare格式
                                main_in_ratio = tpdog_df['主力流入比例'].iloc[0] if '主力流入比例' in tpdog_df.columns else 0
                                main_out_ratio = tpdog_df['主力流出比例'].iloc[0] if '主力流出比例' in tpdog_df.columns else 0
                                main_net_ratio = main_in_ratio - main_out_ratio  # 计算主力净流入占比

                                df_flow = pd.DataFrame({
                                    '股票代码': [stock_code_6_digit],
                                    '股票名称': [tpdog_df['股票名称'].iloc[0] if '股票名称' in tpdog_df.columns else stock_name],
                                    '主力净流入-净额': [tpdog_df['主力净流入'].iloc[0] if '主力净流入' in tpdog_df.columns else 0],
                                    '主力净流入-净占比': [main_net_ratio],
                                    '超大单净流入-净额': [0],  # TPDog接口不提供超大单数据，设为0
                                    '超大单净流入-净占比': [0],
                                    '大单净流入-净额': [0],  # TPDog接口不提供大单数据，设为0
                                    '大单净流入-净占比': [0],
                                    '中单净流入-净额': [tpdog_df['散户净流入'].iloc[0] if '散户净流入' in tpdog_df.columns else 0],  # 用散户数据代替中单
                                    '中单净流入-净占比': [0],
                                    '小单净流入-净额': [0],
                                    '小单净流入-净占比': [0]
                                })
                                logging.info(f"TPDog成功获取持仓股 {stock_code_full} 的资金流数据")
                            else:
                                logging.warning(f"TPDog获取持仓股 {stock_code_full} 的资金流数据为空")
                                df_flow = None
                        else:
                            logging.warning(f"TPDog token未配置，跳过TPDog接口")
                            df_flow = None
                    except Exception as tpdog_e:
                        logging.error(f"TPDog获取持仓股 {stock_code_full} 资金流失败: {tpdog_e}")
                        df_flow = None

                    # 如果TPDog失败，再尝试akshare
                    if df_flow is None or (df_flow is not None and df_flow.empty):
                        try:
                            import akshare as ak
                            df_flow = ak.stock_individual_fund_flow(stock=stock_code_6_digit, market=market)
                            if df_flow is not None and not df_flow.empty:
                                logging.info(f"[TPDog失败后] akshare成功获取持仓股 {stock_code_full} 的资金流数据")
                            else:
                                logging.warning(f"akshare获取持仓股 {stock_code_full} 的资金流数据为空")
                                df_flow = None
                        except Exception as e:
                            logging.warning(f"akshare获取持仓股 {stock_code_full} 资金流失败: {e}")
                            df_flow = None

                    # 如果akshare也失败，尝试adata接口
                    if df_flow is None or (df_flow is not None and df_flow.empty):
                        try:
                            import adata
                            adata_df = adata.stock.market.get_capital_flow(stock_code=stock_code_6_digit)
                            if adata_df is not None and not adata_df.empty:
                                # 转换adata数据格式以匹配akshare格式
                                latest_adata = adata_df.iloc[-1]
                                df_flow = pd.DataFrame({
                                    '股票代码': [stock_code_6_digit],
                                    '股票名称': [stock_name],
                                    '主力净流入-净额': [latest_adata['main_net_inflow']],
                                    '主力净流入-净占比': [0],  # adata不提供占比数据
                                    '超大单净流入-净额': [latest_adata['max_net_inflow']],
                                    '超大单净流入-净占比': [0],
                                    '大单净流入-净额': [latest_adata['lg_net_inflow']],
                                    '大单净流入-净占比': [0],
                                    '中单净流入-净额': [latest_adata['mid_net_inflow']],
                                    '中单净流入-净占比': [0],
                                    '小单净流入-净额': [latest_adata['sm_net_inflow']],
                                    '小单净流入-净占比': [0]
                                })
                                logging.info(f"[akshare失败后] adata成功获取持仓股 {stock_code_full} 的资金流数据")
                            else:
                                logging.warning(f"adata获取持仓股 {stock_code_full} 的资金流数据为空")
                                df_flow = None
                        except Exception as e:
                            logging.warning(f"adata获取持仓股 {stock_code_full} 资金流失败: {e}")
                            df_flow = None
                else:
                    # 盘后优先使用akshare接口
                    try:
                        import akshare as ak
                        df_flow = ak.stock_individual_fund_flow(stock=stock_code_6_digit, market=market)
                        if df_flow is not None and not df_flow.empty:
                            logging.info(f"[盘后优先] akshare成功获取持仓股 {stock_code_full} 的资金流数据")
                        else:
                            logging.warning(f"akshare获取持仓股 {stock_code_full} 的资金流数据为空")
                            df_flow = None
                    except Exception as e:
                        logging.warning(f"akshare获取持仓股 {stock_code_full} 资金流失败: {e}")
                        df_flow = None

                    # 如果akshare失败，尝试adata接口
                    if df_flow is None or (df_flow is not None and df_flow.empty):
                        try:
                            import adata
                            adata_df = adata.stock.market.get_capital_flow(stock_code=stock_code_6_digit)
                            if adata_df is not None and not adata_df.empty:
                                # 转换adata数据格式以匹配akshare格式
                                latest_adata = adata_df.iloc[-1]
                                df_flow = pd.DataFrame({
                                    '股票代码': [stock_code_6_digit],
                                    '股票名称': [stock_name],
                                    '主力净流入-净额': [latest_adata['main_net_inflow']],
                                    '主力净流入-净占比': [0],  # adata不提供占比数据
                                    '超大单净流入-净额': [latest_adata['max_net_inflow']],
                                    '超大单净流入-净占比': [0],
                                    '大单净流入-净额': [latest_adata['lg_net_inflow']],
                                    '大单净流入-净占比': [0],
                                    '中单净流入-净额': [latest_adata['mid_net_inflow']],
                                    '中单净流入-净占比': [0],
                                    '小单净流入-净额': [latest_adata['sm_net_inflow']],
                                    '小单净流入-净占比': [0]
                                })
                                logging.info(f"[akshare失败后] adata成功获取持仓股 {stock_code_full} 的资金流数据")
                            else:
                                logging.warning(f"adata获取持仓股 {stock_code_full} 的资金流数据为空")
                                df_flow = None
                        except Exception as e:
                            logging.warning(f"adata获取持仓股 {stock_code_full} 资金流失败: {e}")
                            df_flow = None

                # 检查是否获取到数据
                if df_flow is None or df_flow.empty:
                    logging.warning(f"无法获取持仓股 {stock_code_full} 的资金流数据。")
                    error_count += 1
                    continue

                # 【关键修复】再次确保df_flow不为None且不为空，并获取最新数据
                try:
                    if df_flow is None or df_flow.empty:
                        raise ValueError("df_flow is None or empty")
                    latest_flow = df_flow.iloc[-1]
                    if latest_flow is None:
                        raise ValueError("latest_flow is None")
                except (AttributeError, IndexError, ValueError) as e:
                    logging.warning(f"获取持仓股 {stock_code_full} 资金流数据时出错: {e}")
                    error_count += 1
                    continue

                # 【修复】提取资金流数据，兼容不同的列名格式
                # akshare单个股票接口的列名格式
                main_net = convert_to_float(latest_flow.get('主力净流入-净额',
                           latest_flow.get('今日主力净流入-净额', 0))) / 10000
                main_ratio = convert_to_float(latest_flow.get('主力净流入-净占比',
                            latest_flow.get('今日主力净流入-净占比', 0)))
                super_net = convert_to_float(latest_flow.get('超大单净流入-净额',
                           latest_flow.get('今日超大单净流入-净额', 0))) / 10000
                super_ratio = convert_to_float(latest_flow.get('超大单净流入-净占比',
                             latest_flow.get('今日超大单净流入-净占比', 0)))
                large_net = convert_to_float(latest_flow.get('大单净流入-净额',
                           latest_flow.get('今日大单净流入-净额', 0))) / 10000
                large_ratio = convert_to_float(latest_flow.get('大单净流入-净占比',
                             latest_flow.get('今日大单净流入-净占比', 0)))
                medium_net = convert_to_float(latest_flow.get('中单净流入-净额',
                            latest_flow.get('今日中单净流入-净额', 0))) / 10000
                medium_ratio = convert_to_float(latest_flow.get('中单净流入-净占比',
                               latest_flow.get('今日中单净流入-净占比', 0)))
                small_net = convert_to_float(latest_flow.get('小单净流入-净额',
                           latest_flow.get('今日小单净流入-净额', 0))) / 10000
                small_ratio = convert_to_float(latest_flow.get('小单净流入-净占比',
                              latest_flow.get('今日小单净流入-净占比', 0)))

                # 获取历史数据（简化处理，使用空DataFrame）
                historical_data = pd.DataFrame()

                # 评估卖出条件
                is_sell, reasons = evaluate_sell_condition(main_net, main_ratio, super_net, super_ratio,
                                                         large_net, large_ratio, medium_net, medium_ratio,
                                                         small_net, small_ratio, historical_data)

                if is_sell:
                    sell_count += 1
                    reason_str = "; ".join(reasons)
                    log_msg = f"【持仓卖出信号】: 股票 {stock_code_full} ({stock_name}) 满足卖出条件: [{reason_str}]"
                    logging.info(log_msg)
                    print(log_msg)
                    sell_signals.append(stock_code_6_digit)
                    sell_details.append(f"{stock_code_full} ({stock_name}): [{reason_str}]")
                else:
                    no_sell_count += 1
                    logging.info(f"【持仓状态更新】: 股票 {stock_code_full} ({stock_name}) 未触发卖出。")

            except Exception as e:
                error_count += 1
                logging.error(f"处理持仓股 {stock_code_full} 时发生错误: {e}")
                print(f"处理持仓股 {stock_code_full} 时发生错误: {e}")
                continue

        # 写入卖出信号文件
        if sell_signals:
            write_signal_file_atomically(SELL_NOTIFICATION_FILE, sell_signals, overwrite=False)

        # 输出报告
        print("\n" + "=" * 20 + " 持仓股检查报告 " + "=" * 20)
        report_msg = (
            f"报告摘要: 共检查 {checked_count} 个股票 | "
            f"卖出信号 {sell_count} 个 | "
            f"无信号 {no_sell_count} 个 | "
            f"失败 {error_count} 个。"
        )
        print(report_msg)
        logging.info(report_msg)

        if sell_details:
            print("--- 卖出信号详情 ---")
            for detail in sell_details:
                print(detail)
        print("=" * 58)

    except Exception as e:
        logging.error(f"卖出信号扫描失败: {e}")
        print(f"❌ 卖出信号扫描失败: {e}")

def task_yesterday_limit_up_review():
    """
    【新增】昨日涨停股复盘及晋级分析任务
    每日开盘前执行，为当日交易决策提供关键情报支持
    """
    try:
        print("\n" + "🎯" * 20)
        print("🎯 启动昨日涨停股复盘及晋级分析...")
        print("🎯" * 20)

        # 导入必要的函数
        from datetime import datetime, timedelta
        from market_data_provider import is_trading_day, get_last_trading_day

        # 【修复】非交易日处理逻辑
        current_date = datetime.now()
        current_date_str = current_date.strftime("%Y%m%d")

        # 如果当前是非交易日，获取最近的两个交易日进行复盘
        if not is_trading_day():
            print(f"📅 当前日期 {current_date_str} 为非交易日，获取最近交易日数据进行复盘...")
            today_str = get_last_trading_day()  # 最近的交易日作为"今日"
            yesterday_str = get_last_trading_day(today_str)  # 再往前一个交易日作为"昨日"
            print(f"📅 使用交易日: 昨日={yesterday_str}, 今日={today_str}")
        else:
            # 交易日正常处理
            today = current_date
            yesterday = today - timedelta(days=1)

            # 如果是周一，昨日应该是上周五
            if today.weekday() == 0:  # 周一
                yesterday = today - timedelta(days=3)

            yesterday_str = yesterday.strftime("%Y%m%d")
            today_str = today.strftime("%Y%m%d")
            print(f"📅 交易日正常处理: 昨日={yesterday_str}, 今日={today_str}")

        # 执行复盘分析
        review_result = analyze_yesterday_limit_up_performance(yesterday_str, today_str)

        if review_result:
            print("✅ 昨日涨停股复盘分析完成")

            # 输出关键指标摘要
            if 'market_sentiment' in review_result:
                metrics = review_result['market_sentiment']
                print(f"\n📊 关键指标摘要:")
                print(f"   晋级成功率: {metrics.get('success_rate', 0):.1f}%")
                print(f"   核按钮率: {metrics.get('nuclear_rate', 0):.1f}%")
                print(f"   平均溢价: {metrics.get('avg_premium', 0):+.1f}%")
                print(f"   溢价分化度: {metrics.get('premium_std', 0):.1f}")

                # 根据指标给出操作建议
                success_rate = metrics.get('success_rate', 0)
                nuclear_rate = metrics.get('nuclear_rate', 0)

                if nuclear_rate > 20:
                    print("⚠️ 操作建议: 核按钮率过高，建议谨慎操作")
                elif success_rate > 60:
                    print("🚀 操作建议: 晋级成功率高，市场情绪良好，可积极参与")
                elif success_rate > 40:
                    print("⚖️ 操作建议: 晋级成功率中等，聚焦强势个股")
                else:
                    print("🧊 操作建议: 晋级成功率低，建议观望")

            # 【新增】保存昨日涨停股复盘数据
            save_analysis_report(review_result, "yesterday_limit_up_review")
        else:
            print("❌ 昨日涨停股复盘分析失败")

    except Exception as e:
        logging.error(f"昨日涨停股复盘分析失败: {e}")
        print(f"❌ 昨日涨停股复盘分析失败: {e}")

def task_mainline_analysis():
    """
    【新增】专门的主线分析任务
    包含：概念主线识别系统、行业主线识别系统
    """
    try:
        print("\n" + "="*60)
        print("🎯 【主线识别系统】")
        print("="*60)

        # 1. 概念主线分析
        print("\n📊 概念主线识别分析...")
        from theme_analyzer import analyze_concept_main_themes
        concept_themes = analyze_concept_main_themes()

        # 保存概念主线数据
        if concept_themes:
            concept_data = {
                'concept_themes': concept_themes,
                'analysis_type': '概念主线识别',
                'timestamp': datetime.now().isoformat()
            }
            save_analysis_report(concept_data, "concept_mainline_analysis")

        # 2. 行业主线分析
        print("\n🏭 行业主线识别分析...")
        from theme_analyzer import analyze_industry_main_themes
        industry_themes = analyze_industry_main_themes()

        # 保存行业主线数据
        if industry_themes:
            industry_data = {
                'industry_themes': industry_themes,
                'analysis_type': '行业主线识别',
                'timestamp': datetime.now().isoformat()
            }
            save_analysis_report(industry_data, "industry_mainline_analysis")

        # 【第二心法优化】寻找共振主线
        resonance_mainlines = _find_resonance_mainlines(concept_themes, industry_themes)
        if resonance_mainlines:
            print(f"\n🔥🔥🔥 【共振主线发现】: {', '.join(resonance_mainlines)} 🔥🔥🔥")
            print("💡 这些板块同时出现在概念和行业主线中，是最强主线！")

            # 保存共振主线数据
            resonance_data = {
                'resonance_mainlines': resonance_mainlines,
                'concept_themes': concept_themes,
                'industry_themes': industry_themes,
                'analysis_type': '共振主线识别',
                'timestamp': datetime.now().isoformat()
            }
            save_analysis_report(resonance_data, "resonance_mainline_analysis")

        # 3. 量化评分版主线分析
        print("\n📈 量化评分主线分析...")
        from theme_analyzer import analyze_main_themes_quantitative
        quantitative_results = analyze_main_themes_quantitative()

        # 保存量化主线数据
        if quantitative_results:
            quantitative_data = {
                'quantitative_results': quantitative_results,
                'analysis_type': '量化评分主线分析',
                'timestamp': datetime.now().isoformat()
            }
            save_analysis_report(quantitative_data, "quantitative_mainline_analysis")

        print("✅ 主线识别系统分析完成")

    except Exception as e:
        logging.error(f"主线识别系统分析失败: {e}")
        print(f"❌ 主线识别系统分析失败: {e}")

def task_limit_up_performance_review():
    """
    【新增】涨停股表现复盘任务（实时循环版）
    定期执行涨停股今日表现分析
    """
    try:
        print("\n" + "="*60)
        print("📊 【涨停股今日表现复盘】")
        print("="*60)

        # 获取今日日期
        today_str = datetime.now().strftime('%Y%m%d')
        yesterday_str = get_last_trading_day()  # 已经是字符串格式YYYYMMDD

        print(f"📅 复盘日期: {yesterday_str} → {today_str}")

        # 执行复盘分析
        review_result = analyze_yesterday_limit_up_performance(yesterday_str, today_str)

        if review_result:
            print("✅ 涨停股今日表现复盘完成")

            # 输出关键指标摘要
            if 'market_sentiment' in review_result:
                metrics = review_result['market_sentiment']
                print(f"\n📊 关键指标摘要:")
                print(f"   晋级成功率: {metrics.get('success_rate', 0):.1f}%")
                print(f"   核按钮率: {metrics.get('nuclear_rate', 0):.1f}%")
                print(f"   平均溢价: {metrics.get('avg_premium', 0):+.1f}%")

            # 保存复盘数据
            save_analysis_report(review_result, "limit_up_performance_review")
        else:
            print("❌ 涨停股今日表现复盘失败")

    except Exception as e:
        logging.error(f"涨停股今日表现复盘失败: {e}")
        print(f"❌ 涨停股今日表现复盘失败: {e}")

# 【报告生成】新增一个专门用于生成和保存每日战报的函数
# 文件: main_controller.py

def task_generate_end_of_day_report():
    """
    【V2.0 游资战报版】在收盘后，聚合全天数据，生成一份完整的复盘战报。
    """
    global DAILY_REPORT_DATA
    print("\n" + "=" * 80)
    print("🔥 生成每日超短线复盘战报...")
    print("=" * 80)

    try:
        # 确保有数据可供分析
        if not DAILY_REPORT_DATA.get('sentiment') or not DAILY_REPORT_DATA.get('mainline'):
            print("❌ 数据不足，无法生成战报。请确保盘中扫描任务已至少运行一次。")
            return

        # --- 数据准备：获取最新、最全的收盘数据 ---
        today_str = datetime.now().strftime("%Y%m%d")
        yesterday_str = get_last_trading_day(today_str)

        # 重新获取昨日涨停复盘的最终收盘数据
        review_result = analyze_yesterday_limit_up_performance(yesterday_str, today_str)
        if review_result:
            DAILY_REPORT_DATA['yesterday_review'] = review_result

        # 【核心升级】重新获取今日最终的涨停池数据，用于梯队结构分析
        from market_data_provider import get_zt_pool_with_backup
        today_zt_pool_df = get_zt_pool_with_backup(today_str)
        if today_zt_pool_df is not None and not today_zt_pool_df.empty:
            # 标准化列名以确保兼容性
            if '连板数' not in today_zt_pool_df.columns and 'consecutive_boards' in today_zt_pool_df.columns:
                today_zt_pool_df.rename(columns={'consecutive_boards': '连板数'}, inplace=True)
            if '代码' not in today_zt_pool_df.columns and 'code' in today_zt_pool_df.columns:
                today_zt_pool_df.rename(columns={'code': '代码'}, inplace=True)
            if '名称' not in today_zt_pool_df.columns and 'name' in today_zt_pool_df.columns:
                today_zt_pool_df.rename(columns={'name': '名称'}, inplace=True)

        # 重新获取今日最终的涨停梯队和主线情况
        from theme_analyzer import analyze_today_limit_ups
        limit_up_summary = analyze_today_limit_ups()
        if limit_up_summary:
            DAILY_REPORT_DATA['limit_up_summary'] = limit_up_summary

        # --- 开始构建Markdown报告 ---
        report_md = f"# {DAILY_REPORT_DATA['date']} 超短线盘面复盘战报 (四大心法版)\n\n"

        # --- 1. 市场情绪与周期诊断 ---
        senti = DAILY_REPORT_DATA.get('sentiment', {})
        metrics = senti.get('metrics', {})
        senti_status = senti.get('status', '未知')
        senti_emoji = {'强势期': '🔥', '分化期': '⚖️', '混沌期': '🌫️', '冰点期': '🧊'}.get(senti_status, '❓')

        report_md += "## 一、市场情绪与周期诊断 (总开关)\n\n"
        report_md += f"- **市场状态**: {senti_emoji} **{senti_status}**\n"
        report_md += f"- **核心指标**: 晋级率: {metrics.get('success_rate', 0):.1f}%, 核按钮率: {metrics.get('nuclear_rate', 0):.1f}%, 平均溢价: {metrics.get('avg_premium', 0):+.2f}%\n"

        senti_interpretation = "解读生成中..."
        if senti_status == '强势期':
            senti_interpretation = "赚钱效应爆棚，亏钱效应极弱。市场处于主升浪，是积极进攻、大胆做多的窗口期。龙头不倒，积极围绕主线龙头和支线补涨操作。"
        elif senti_status == '分化期':
            senti_interpretation = "结构性行情，强者恒强，弱者淘汰。操作难度加大，需聚焦核心主线和龙头，控制仓位，多看少动。"
        elif senti_status == '混沌期':
            senti_interpretation = "方向不明，热点轮动快，持续性差。多看少动，管住手，等待市场选出新方向。仓位应降至最低。"
        elif senti_status == '冰点期':
            senti_interpretation = "亏钱效应明显，恐慌情绪蔓延。空仓是最好的策略，耐心等待情绪修复的信号（如V型反转或长腿阳线）。"

        report_md += f"- **盘感解读**: {senti_interpretation}\n"
        report_md += f"- **建议仓位**: **{senti.get('position_ratio', 0):.0%}**\n\n"

        # --- 2. 主线战场与龙头复盘 ---
        mainline = DAILY_REPORT_DATA.get('mainline', {})
        leader = mainline.get('market_leader', {})
        resonance = mainline.get('resonance_themes', [])

        report_md += "## 二、今日主战场与龙头复盘 (主战场)\n\n"

        if CAPITAL_LEADER_INFO.get('code'):
            report_md += f"- **资金总龙头**: **{CAPITAL_LEADER_INFO['name']} ({CAPITAL_LEADER_INFO['code']})** - {CAPITAL_LEADER_INFO['reason']}\n"

        report_md += f"- **市场总龙头**: **{leader.get('name', '无')} ({leader.get('code', '')})** - **{leader.get('boards', 0)}板**\n"
        if resonance:
            report_md += f"- **核心共振主线**: **🔥{' / '.join(resonance)}🔥**\n"

        report_md += "- **主线梯队**:\n"
        top_concepts = mainline.get('concept_themes', [])[:2]
        top_industries = [t.replace('[行业]', '') for t in mainline.get('industry_themes', [])[:1]]

        if top_concepts: report_md += f"    - **[概念主线]**: {', '.join(top_concepts)}\n"
        if top_industries: report_md += f"    - **[行业支线]**: {', '.join(top_industries)}\n\n"

        # --- 3. 梯队晋级与亏钱效应 ---
        review = DAILY_REPORT_DATA.get('yesterday_review', {})
        grouped_results = review.get('grouped_results', {})

        report_md += "## 三、梯队晋级与亏钱效应 (强弱鉴别)\n\n"

        # --- 【核心升级】连板梯队的昭示 ---
        echelon_narrative = ""
        if leader.get('code'):
            leader_boards = leader.get('boards', 0)
            echelon_narrative += f"- **总龙头**：`{leader.get('name')}` 顶出 `{leader_boards}` 连板的高度，是市场的灵魂，是情绪的定海神针。它的存在，为所有后排的股票打开了想象空间，定义了本轮周期的最高天花板。\n"

            # 寻找中军核心 (例如3板到最高板-1的股票)
            mid_tier_core_df = pd.DataFrame()
            if today_zt_pool_df is not None and not today_zt_pool_df.empty and '连板数' in today_zt_pool_df.columns:
                mid_tier_core_df = today_zt_pool_df[
                    (today_zt_pool_df['连板数'] >= 3) & (today_zt_pool_df['连板数'] < leader_boards)]

            if not mid_tier_core_df.empty:
                mid_tier_stocks = [f"{row['名称']}({int(row['连板数'])}板)" for _, row in mid_tier_core_df.iterrows()]
                echelon_narrative += f"- **中军核心**：市场出现了以 `{', '.join(mid_tier_stocks)}` 为代表的高位核心力量。\n"
                echelon_narrative += "- **盘面解读**：这说明本轮行情并非个股的单打独斗，而是有清晰主线、有集团冲锋的板块行情。**梯队完整、结构健康，这是最适合接力的盘面**。\n\n"
            else:
                echelon_narrative += "- **盘面解读**：总龙头一骑绝尘，但后排中军未能跟上，形成**高位真空**。需警惕龙头倒下后情绪快速退潮的风险。\n\n"
        else:
            echelon_narrative += "- **盘面解读**：市场**群龙无首**，高度板缺失，热点散乱，持续性差。当前环境以轮动为主，操作难度极大，建议多看少动。\n\n"
        report_md += echelon_narrative
        # --- 升级结束 ---

        if review:
            report_md += "- **昨日晋级/淘汰情况**:\n"
            promotion_summary = []
            if grouped_results.get('最高板'): promotion_summary.append(
                f"最高板梯队({len(grouped_results['最高板'])}只)表现强势")
            if grouped_results.get('高板'): promotion_summary.append(
                f"高板梯队({len(grouped_results['高板'])}只)出现分歧")
            if grouped_results.get('炸板/淘汰区'): promotion_summary.append(
                f"{len(grouped_results['炸板/淘汰区'])}只昨日涨停股被淘汰")
            report_md += f"    - {', '.join(promotion_summary) if promotion_summary else '无明显晋级'}\n"

            report_md += "- **主要亏钱效应**: \n"
            nuclear_stocks = [res for res in review.get('analysis_results', []) if res['晋级结果'] == '核按钮']
            if nuclear_stocks:
                top_nuclear = nuclear_stocks[0]
                report_md += f"    - **核按钮代表**: {top_nuclear['名称']} ({top_nuclear['收盘溢价']:.1f}%) - {top_nuclear['核心题材']}\n\n"
            else:
                report_md += "    - 今日亏钱效应极弱，市场情绪良好。\n\n"

        # --- 4. 盘中异动与新题材 ---
        report_md += "## 四、盘中异动与新题材 (先手预判)\n\n"

        report_md += "- **资金攻击方向 (盘中突变)**:\n"
        last_flow = DAILY_REPORT_DATA['intraday_flow'][-1] if DAILY_REPORT_DATA['intraday_flow'] else None
        if last_flow:
            rising_names = [item['名称'] for item in last_flow.get('rising', [])]
            report_md += f"    - **{last_flow['time']}后**: 资金明显攻击 **{', '.join(rising_names)}** 等方向，出现抢筹迹象。\n"
        else:
            report_md += "    - 盘中资金攻击方向不明确，呈普涨或普跌格局。\n"

        report_md += "- **新发酵题材 (首板聚集区)**:\n"
        report_md += "    - *[待整合首板分析数据]*\n\n"

        # --- 5. 明日策略展望 ---
        report_md += "## 五、明日策略展望 (应对为上)\n\n"

        strategy = "明日策略生成中..."
        if senti_status == '强势期' and leader.get('code'):
            strategy = (
                f"1. **核心战场**: 围绕总龙头 **{leader['name']}** 所在 **{' / '.join(resonance) if resonance else mainline.get('concept_themes', ['未知'])[0]}** 板块做高低切换。关注后排补涨和活口。\n"
                f"2. **备选方向**: 关注今日资金尾盘抢筹的 **{' / '.join(rising_names) if last_flow else '新方向'}**，观察明日竞价强度，寻找换手龙头。\n"
                f"3. **情绪监控**: 核心观察指标是 **{leader['name']}** 是否继续超预期，以及明日的连板晋级率。若龙头倒下且晋级率低于20%，则周期可能进入退潮，应立即收缩仓位。")
        else:
            strategy = "市场混沌，多看少动。等待市场出现明确的共振主线和核心龙头再考虑参与。保住本金是第一要务。"

        report_md += strategy + "\n"

        # --- 保存报告 ---
        report_folder = get_date_folder()
        report_filename = os.path.join(report_folder, f"daily_report_{DAILY_REPORT_DATA['date']}.md")
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(report_md)

        print(f"✅ 每日复盘战报已生成: {report_filename}")

    except Exception as e:
        logging.error(f"生成每日战报失败: {e}", exc_info=True)
        print(f"❌ 生成每日战报失败: {e}")

# === 连续任务调度配置 ===
TASK_QUEUE = []
TASK_RUNNING = False
CURRENT_TASK_INDEX = 0  # 【新增】当前任务索引，用于循环轮转
LAST_TASK_TIMES = {
    'four_methods': None,
    'traditional_scan': None,
    'position_scan': None,
    'mainline_analysis': None,
    'limit_up_review': None
}

def schedule_jobs():
    """
    【V7.0 连续任务版】设置连续执行的任务队列
    任务完成后立即进行下一个任务，实现类似实时的连续扫描
    """
    try:
        # 清除所有现有任务（不再使用schedule库的定时功能）
        schedule.clear()

        # 【保留】每日定时任务仍使用schedule
        schedule.every().day.at("09:20").do(run_threaded, task_yesterday_limit_up_review)
        schedule.every().day.at("15:05").do(run_threaded, task_yesterday_limit_up_review)

        # 【报告生成】新增每日收盘后的战报生成任务
        schedule.every().day.at("15:15").do(run_threaded, task_generate_end_of_day_report)

        print("✅ 连续任务调度设置完成:")
        print("   - 持仓扫描: 循环轮转执行（第1个任务）")
        print("   - 四大心法V7.0扫描: 循环轮转执行（第2个任务，包含资金加速度、炸板监控、烂板回封）")
        print("   - 主线识别系统: 循环轮转执行（第3个任务，概念主线+行业主线+量化评分）")
        print("   - 涨停股表现复盘: 循环轮转执行（第4个任务，实时复盘分析）")
        print("   - 传统市场扫描: 循环轮转执行（第5个任务，备用扫描）")
        print("   - 昨日涨停股复盘: 每日9:20和15:05（定时任务）")
        print("   - 【新增】每日复盘战报: 每日15:15（定时任务）")
        print("   ⚡ 任务执行模式: 循环轮转，无时间间隔，一个任务完成立即执行下一个任务")
        logging.info("连续任务调度设置完成：所有重要监控功能已纳入循环轮转任务，并新增每日战报生成。")

    except Exception as e:
        logging.error(f"设置任务调度失败: {e}")
        print(f"❌ 设置任务调度失败: {e}")

def run_backtest():
    """
    回测模式主函数
    """
    try:
        print(f"🔄 开始回测模式，日期: {BACKTEST_DATE}")
        
        # 执行一次完整的市场扫描
        task_scan_market_for_signals()
        
        print("✅ 回测完成")
        
    except Exception as e:
        logging.error(f"回测执行失败: {e}")
        print(f"❌ 回测执行失败: {e}")

def main():
    """
    主程序入口
    """
    # 网络连接测试
    test_network_connection()
    
    logging.info(f"启动股票监控系统... 当前模式: {RUN_MODE}")
    logging.info(f"akshare 接口状态: {AKSHARE_AVAILABLE}")
    
    if RUN_MODE == 'BACKTEST':
        # 回测模式
        print(f"--- 进入回测模式，日期: {BACKTEST_DATE} ---")
        
        # 初始化概念缓存
        if not load_stock_concept_cache():
            print("❌ 回测模式需要概念缓存，请先在实时模式下运行一次")
            return
        
        run_backtest()
        
    else:
        # 实时模式
        print("--- 进入实时监控模式 ---")
        
        # 初始化概念缓存
        print("\n🔄 初始化概念板块缓存...")
        if not load_stock_concept_cache():
            print("缓存不存在或已过期，开始更新缓存...")
            update_stock_concept_cache()
        else:
            # 【修复】确保缓存正确加载，验证几个测试股票
            from market_data_provider import STOCK_CONCEPT_CACHE
            test_codes = ['001360', '002017', '688585']
            cache_working = all(STOCK_CONCEPT_CACHE.get(code, []) for code in test_codes)
            if cache_working:
                print(f"✅ 概念缓存验证通过，包含 {len(STOCK_CONCEPT_CACHE)} 只股票")
            else:
                print("⚠️ 概念缓存验证失败，重新加载...")
                update_stock_concept_cache()
        print("概念板块缓存初始化完成。\n")
        
        # 【修复】加载历史推送缓存 - 处理纯文本格式的信号文件
        try:
            if os.path.exists(NOTIFICATION_FILE) and os.path.getsize(NOTIFICATION_FILE) > 0:
                # 读取纯文本格式的信号文件
                with open(NOTIFICATION_FILE, 'r', encoding='utf-8') as f:
                    lines = [line.strip() for line in f.readlines() if line.strip()]

                if lines:
                    print(f"📚 加载 {len(lines)} 条历史推送信号到缓存")
                    for line in lines:
                        try:
                            # 解析信号格式：股票代码,信号类型,描述,其他信息
                            parts = line.split(',')
                            if len(parts) >= 2:
                                stock_code = parts[0].strip().zfill(6)
                                signal_type = parts[1].strip()
                                cache_key = (stock_code, signal_type)
                                TODAY_NOTIFICATION_CACHE[cache_key] = {'signal': line}
                        except Exception as parse_e:
                            logging.warning(f"解析信号行失败: {line}, 错误: {parse_e}")
        except Exception as e:
            logging.error(f"加载推送历史失败: {e}")
        
        # 设置定时任务
        schedule_jobs()
        
        # 【修复】首次启动立即执行连续任务 - 检查交易时间限制配置
        if ENABLE_TRADING_TIME_CHECK:
            trading_day, trading_time = get_global_trading_status()
            if trading_day and trading_time:
                print("🚀 首次启动，启动连续任务调度...")
                # 立即触发第一个任务
                trigger_next_continuous_task()
            else:
                current_time = datetime.now().strftime('%H:%M:%S')
                print(f"⏰ 非交易时间（当前时间: {current_time}），等待交易时间开始连续任务")
        else:
            print("🚀 首次启动，启动连续任务调度（交易时间限制已关闭）...")
            # 立即触发第一个任务
            trigger_next_continuous_task()

        # 启动调度循环
        print("🔄 启动连续任务调度器...")
        while True:
            # 执行schedule库的定时任务（如复盘分析）
            schedule.run_pending()

            # 【修复】在交易时间内，如果没有任务在运行，触发连续任务 - 检查交易时间限制配置
            if ENABLE_TRADING_TIME_CHECK:
                trading_day, trading_time = get_global_trading_status()
                if (trading_day and trading_time) and not TASK_RUNNING:
                    trigger_next_continuous_task()
            else:
                # 交易时间限制关闭，直接执行任务
                if not TASK_RUNNING:
                    trigger_next_continuous_task()

            # 短暂休眠，避免CPU占用过高
            time_module.sleep(1)

if __name__ == "__main__":
    main()
