#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本：验证get_all_capital_flow_east_v3.py的修改
测试内容：
1. 行业资金流前五名功能
2. 概念资金流前五名功能
3. TPDOG备用接口功能
"""

import sys
import os
import pandas as pd
import akshare as ak
from datetime import datetime
import logging
from dotenv import load_dotenv

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 加载.env文件
load_dotenv()

def test_akshare_interfaces():
    """测试akshare接口是否正常工作"""
    print("=" * 50)
    print("测试 AKShare 接口")
    print("=" * 50)
    
    try:
        # 测试行业资金流接口
        print("🔄 测试行业资金流接口...")
        sector_df = ak.stock_sector_fund_flow_rank(indicator="今日", sector_type="行业资金流")
        if not sector_df.empty:
            print(f"✅ 行业资金流接口成功，获取 {len(sector_df)} 条数据")
            print("前5名行业:")
            top_5_sectors = sector_df.head(5)['名称'].tolist()
            for i, sector in enumerate(top_5_sectors, 1):
                print(f"  {i}. {sector}")
        else:
            print("❌ 行业资金流接口返回空数据")
            
    except Exception as e:
        print(f"❌ 行业资金流接口失败: {e}")
    
    try:
        # 测试概念资金流接口
        print("\n🔄 测试概念资金流接口...")
        concept_df = ak.stock_sector_fund_flow_rank(indicator="今日", sector_type="概念资金流")
        if not concept_df.empty:
            print(f"✅ 概念资金流接口成功，获取 {len(concept_df)} 条数据")
            print("前5名概念:")
            top_5_concepts = concept_df.head(5)['名称'].tolist()
            for i, concept in enumerate(top_5_concepts, 1):
                print(f"  {i}. {concept}")
        else:
            print("❌ 概念资金流接口返回空数据")
            
    except Exception as e:
        print(f"❌ 概念资金流接口失败: {e}")

def test_sector_summary_interface():
    """测试个股资金流接口"""
    print("\n" + "=" * 50)
    print("测试个股资金流接口")
    print("=" * 50)
    
    try:
        # 先获取一个行业名称
        sector_df = ak.stock_sector_fund_flow_rank(indicator="今日", sector_type="行业资金流")
        if not sector_df.empty:
            test_sector = sector_df.iloc[0]['名称']
            print(f"🔄 测试行业 '{test_sector}' 的个股资金流...")
            
            stock_summary_df = ak.stock_sector_fund_flow_summary(symbol=test_sector, indicator="今日")
            if not stock_summary_df.empty:
                print(f"✅ 个股资金流接口成功，获取 {len(stock_summary_df)} 条数据")
                print(f"'{test_sector}' 前3只股票:")
                for i, row in stock_summary_df.head(3).iterrows():
                    print(f"  {i+1}. {row.get('名称', 'N/A')} - 净流入: {row.get('今日主力净流入-净额', 'N/A')}")
            else:
                print(f"❌ '{test_sector}' 个股资金流数据为空")
        else:
            print("❌ 无法获取行业数据进行测试")
            
    except Exception as e:
        print(f"❌ 个股资金流接口测试失败: {e}")

def test_tpdog_interface():
    """测试TPDOG接口（需要配置token）"""
    print("\n" + "=" * 50)
    print("测试 TPDOG 接口")
    print("=" * 50)
    
    # 检查环境变量中是否有TPDOG token
    tpdog_token = os.getenv('TPDOG_TOKEN')
    if not tpdog_token or tpdog_token.strip() == "":
        print("⚠️ 未配置TPDOG_TOKEN环境变量，跳过TPDOG接口测试")
        print("   如需测试TPDOG接口，请在.env文件中设置: TPDOG_TOKEN=your_token")
        return

    print(f"🔑 已加载TPDOG_TOKEN: {tpdog_token[:10]}...")  # 只显示前10个字符
    
    import requests
    
    try:
        # 测试行业版块资金流（非交易时间使用样例数据）
        print("🔄 测试TPDOG行业版块资金流接口...")
        url = f"https://www.tpdog.com/api/hs/current/bk_funds?bk_type=bki&field=m_net&sort=2&t=1&token={tpdog_token}"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if (data.get('code') == 1000 or data.get('code') == 1002) and data.get('content'):
                status = "示例数据" if data.get('code') == 1002 else "实时数据"
                print(f"✅ TPDOG行业接口成功，获取 {len(data['content'])} 条{status}")
                print("前5名行业:")
                for i, item in enumerate(data['content'][:5], 1):
                    print(f"  {i}. {item['name']} - 净流入: {item['m_net']}")
            else:
                print(f"❌ TPDOG行业接口返回错误: {data}")
        else:
            print(f"❌ TPDOG行业接口请求失败，状态码: {response.status_code}")
            
    except Exception as e:
        print(f"❌ TPDOG行业接口测试失败: {e}")
    
    try:
        # 测试概念版块资金流（非交易时间使用样例数据）
        print("\n🔄 测试TPDOG概念版块资金流接口...")
        url = f"https://www.tpdog.com/api/hs/current/bk_funds?bk_type=bkc&field=m_net&sort=2&t=1&token={tpdog_token}"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if (data.get('code') == 1000 or data.get('code') == 1002) and data.get('content'):
                status = "示例数据" if data.get('code') == 1002 else "实时数据"
                print(f"✅ TPDOG概念接口成功，获取 {len(data['content'])} 条{status}")
                print("前5名概念:")
                for i, item in enumerate(data['content'][:5], 1):
                    print(f"  {i}. {item['name']} - 净流入: {item['m_net']}")
            else:
                print(f"❌ TPDOG概念接口返回错误: {data}")
        else:
            print(f"❌ TPDOG概念接口请求失败，状态码: {response.status_code}")
            
    except Exception as e:
        print(f"❌ TPDOG概念接口测试失败: {e}")

def main():
    """主测试函数"""
    print("开始测试资金流接口修改...")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试akshare接口
    test_akshare_interfaces()
    
    # 测试个股资金流接口
    test_sector_summary_interface()
    
    # 测试TPDOG接口
    test_tpdog_interface()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)
    print("说明:")
    print("1. 如果akshare接口测试成功，说明主要功能正常")
    print("2. 如果TPDOG接口测试失败，可能是因为:")
    print("   - 未在.env文件中配置TPDOG_TOKEN")
    print("   - 当前不在交易时间(9:30-11:30/13:00-15:00)")
    print("   - 网络连接问题或token无效")
    print("3. 修改后的脚本会在akshare失败时自动尝试TPDOG接口")
    print("4. 请在项目根目录创建.env文件并设置: TPDOG_TOKEN=your_token")

if __name__ == "__main__":
    main()
