"""
主线分析与概念监控模块
负责主线识别、概念分析、板块监控等功能
"""

import pandas as pd
import numpy as np
import logging
import json
import os
from datetime import datetime, timedelta
import time as time_module
from collections import defaultdict, Counter, deque
import requests
from tabulate import tabulate
from market_data_provider import (
    get_sector_fund_flow_with_backup,
    get_realtime_sector_fund_flow,
    get_stock_concepts,
    get_stock_industry,
    safe_akshare_call,
    convert_to_float,
    save_after_hours_cache,
    is_after_hours,
    get_zt_pool_with_backup,
    get_stocks_auction_info_with_cache,
    get_stocks_intraday_pattern,
    get_current_stock_quotes,
    get_fried_board_pool_with_backup,
    get_core_indices_spot
)

# --- 全局变量 ---
STRONG_SECTORS_LIST = []
THEME_PERSISTENCE_TRACKER = {}
MAIN_THEME_SCORE_THRESHOLD = 6.0
MARKET_THEMES_DATA = {}
MARKET_LEADER_INFO = {'code': None, 'boards': 0}
PREVIOUS_MARKET_LEADER_INFO = {'code': None, 'boards': 0}
CYCLE_TRANSITION_THEME = None
CONCEPT_BUY_CANDIDATES = []

# --- 【新增】昨日涨停股复盘数据缓存，避免重复计算 ---
YESTERDAY_REVIEW_CACHE = {
    'data': None,
    'date': None,
    'last_update_time': None
}

# 【V8.0 新增】盘中资金流突变监控全局变量
REALTIME_FLOW_HISTORY = {
    "概念": deque(maxlen=5), # 存储最近5次快照
    "行业": deque(maxlen=5)
}
LAST_ANALYSIS_TIME = None

# 主线评分权重
W_STRENGTH = 0.3
W_PERSISTENCE = 0.5
W_BREADTH = 0.2

def analyze_intraday_flow_shift():
    """
    【V8.0 核心】盘中资金流突变分析 (先手预判)
    通过高频对比即时资金流快照，识别资金攻击方向的变化
    """
    global REALTIME_FLOW_HISTORY, LAST_ANALYSIS_TIME

    # 避免过于频繁的重复计算 (例如1分钟内)
    if LAST_ANALYSIS_TIME and (datetime.now() - LAST_ANALYSIS_TIME).total_seconds() < 60:
        return None, None

    print("\n" + "⚡"*15 + " 盘中资金流向监控 (先手预判) " + "⚡"*15)

    # 1. 获取最新的概念即时资金流数据
    # 为了聚焦，我们优先分析概念板块的变化，因为它是题材的核心
    latest_concept_df = get_realtime_sector_fund_flow("概念")

    if latest_concept_df is None or latest_concept_df.empty:
        print("❌ 无法获取即时资金流，跳过本次分析")
        return None, None

    # 清理和预处理数据
    latest_concept_df['今日主力净流入-净额'] = latest_concept_df['今日主力净流入-净额'].apply(convert_to_float)
    latest_concept_df = latest_concept_df.sort_values(by='今日主力净流入-净额', ascending=False).reset_index(drop=True)
    latest_concept_df['current_rank'] = latest_concept_df.index + 1

    # 2. 【核心优化】检查并加载今天已有的历史数据
    history = REALTIME_FLOW_HISTORY["概念"]
    if not history:
        # 尝试加载今天已有的历史数据
        _load_today_flow_history()
        history = REALTIME_FLOW_HISTORY["概念"]

        if not history:
            # 如果仍然没有历史数据，记录当前快照作为初始数据
            history.append(latest_concept_df)
            print("📈 首次运行，已记录初始资金流快照。")
            LAST_ANALYSIS_TIME = datetime.now()
            return None, None
        else:
            print(f"📋 成功加载今天已有的 {len(history)} 个历史快照，继续分析...")

    # 3. 获取上一次的快照用于对比

    previous_df = history[-1]

    # 4. 计算增量数据
    merged_df = pd.merge(latest_concept_df, previous_df[['名称', '今日主力净流入-净额', 'current_rank']], on='名称', suffixes=('', '_prev'), how='left')
    merged_df.fillna({'今日主力净流入-净额_prev': 0, 'current_rank_prev': 100}, inplace=True) # 对于新上榜的，给一个默认低排名

    # 【修复】确保数据单位一致性
    # 检查当前数据和历史数据的数量级，如果差异过大说明单位不一致
    current_avg = merged_df['今日主力净流入-净额'].abs().mean()
    prev_avg = merged_df['今日主力净流入-净额_prev'].abs().mean()

    # 如果历史数据的平均值比当前数据大10000倍以上，说明历史数据是元单位，当前数据是万元单位
    if prev_avg > 0 and current_avg > 0 and prev_avg / current_avg > 5000:
        print(f"🔧 检测到数据单位不一致，历史数据(元)转换为万元单位进行对比")
        merged_df['今日主力净流入-净额_prev'] = merged_df['今日主力净流入-净额_prev'] / 10000
    # 如果当前数据的平均值比历史数据大10000倍以上，说明当前数据是元单位，历史数据是万元单位
    elif current_avg > 0 and prev_avg > 0 and current_avg / prev_avg > 5000:
        print(f"🔧 检测到数据单位不一致，当前数据(元)转换为万元单位进行对比")
        merged_df['今日主力净流入-净额'] = merged_df['今日主力净流入-净额'] / 10000

    # 计算5分钟（或一个间隔）内的资金增量和排名变化
    merged_df['flow_increment'] = merged_df['今日主力净流入-净额'] - merged_df['今日主力净流入-净额_prev']
    merged_df['rank_change'] = merged_df['current_rank_prev'] - merged_df['current_rank'] # 正数表示排名上升

    # 5. 计算核心"突变分"，体现预期差
    # 权重：排名跃升的意义大于资金增量，因为它代表了市场关注度的质变，是先手信号
    W_RANK_CHANGE = 0.6
    W_FLOW_INCREMENT = 0.4

    # 归一化处理，让不同量纲的数据可以比较
    if merged_df['rank_change'].max() != merged_df['rank_change'].min():
        merged_df['rank_change_score'] = (merged_df['rank_change'] - merged_df['rank_change'].min()) / (merged_df['rank_change'].max() - merged_df['rank_change'].min())
    else:
        merged_df['rank_change_score'] = 0.5

    if merged_df['flow_increment'].max() != merged_df['flow_increment'].min():
        merged_df['flow_increment_score'] = (merged_df['flow_increment'] - merged_df['flow_increment'].min()) / (merged_df['flow_increment'].max() - merged_df['flow_increment'].min())
    else:
        merged_df['flow_increment_score'] = 0.5

    # 填充NaN值，防止计算错误
    merged_df.fillna(0, inplace=True)

    merged_df['shift_score'] = merged_df['rank_change_score'] * W_RANK_CHANGE + merged_df['flow_increment_score'] * W_FLOW_INCREMENT

    # 6. 筛选出异动板块
    rising_sectors = merged_df[merged_df['shift_score'] > 0.5].sort_values(by='shift_score', ascending=False).head(10)
    fading_sectors = merged_df[merged_df['flow_increment'] < 0].sort_values(by='flow_increment', ascending=True).head(5)

    # 7. 更新历史快照
    history.append(latest_concept_df)
    LAST_ANALYSIS_TIME = datetime.now()

    return rising_sectors, fading_sectors


def _load_today_flow_history():
    """
    【新增】加载今天已有的资金流历史数据
    从保存的CSV文件中恢复历史快照，避免重启后显示"首次运行"
    """
    global REALTIME_FLOW_HISTORY

    try:
        today_str = datetime.now().strftime('%Y%m%d')
        data_dir = f"data/{today_str}"

        if not os.path.exists(data_dir):
            print(f"📂 今日数据目录不存在: {data_dir}")
            return

        # 查找今天的实时概念资金流文件
        concept_files = []
        for filename in os.listdir(data_dir):
            if filename.startswith('实时概念资金流_') and filename.endswith('.csv'):
                concept_files.append(filename)

        if not concept_files:
            print(f"📂 今日暂无历史资金流数据")
            return

        # 按时间排序文件名（文件名包含时间戳）
        concept_files.sort()

        # 加载最近的几个文件作为历史快照（最多5个）
        loaded_count = 0
        for filename in concept_files[-5:]:  # 取最近的5个文件
            try:
                file_path = os.path.join(data_dir, filename)
                df = pd.read_csv(file_path, encoding='utf-8-sig')

                if not df.empty and '今日主力净流入-净额' in df.columns:
                    # 预处理数据，保持与实时数据一致的格式
                    df['今日主力净流入-净额'] = df['今日主力净流入-净额'].apply(convert_to_float)
                    df = df.sort_values(by='今日主力净流入-净额', ascending=False).reset_index(drop=True)
                    df['current_rank'] = df.index + 1

                    # 添加到历史记录
                    REALTIME_FLOW_HISTORY["概念"].append(df)
                    loaded_count += 1

            except Exception as e:
                print(f"⚠️ 加载文件 {filename} 失败: {e}")
                continue

        if loaded_count > 0:
            print(f"✅ 成功加载 {loaded_count} 个历史资金流快照")
        else:
            print(f"❌ 未能加载任何历史资金流数据")

    except Exception as e:
        print(f"❌ 加载今日资金流历史数据失败: {e}")


def analyze_concept_main_themes():
    """
    【概念主线识别】分析概念资金流，识别概念主线。
    """
    global STRONG_SECTORS_LIST, THEME_PERSISTENCE_TRACKER
    
    try:
        print("\n" + "=" * 20 + " 概念主线识别系统 (观潮法V4+先手预判) " + "=" * 20)

        # 1. 【新增】先运行资金流突变分析，获取预期差信息
        rising_sectors, _ = analyze_intraday_flow_shift()

        # 2. 获取概念资金流数据
        current_sectors_df = get_sector_fund_flow_with_backup("概念资金流")
        
        if current_sectors_df is None or current_sectors_df.empty:
            print("❌ 所有概念资金流接口都失败，无法进行概念主线分析。")
            return []
        
        # 2. 统计概念涨停家数
        try:
            zt_pool_df = safe_akshare_call('stock_zt_pool_em', date=datetime.now().strftime("%Y%m%d"))
            concept_limit_up_counts = {}
            if not zt_pool_df.empty:
                for _, row in zt_pool_df.iterrows():
                    stock_code = str(row['代码']).zfill(6)
                    concepts = get_stock_concepts(stock_code)
                    for concept in concepts:
                        concept_limit_up_counts[concept] = concept_limit_up_counts.get(concept, 0) + 1
            current_sectors_df['涨停家数'] = current_sectors_df['名称'].map(concept_limit_up_counts).fillna(0).astype(int)
        except Exception as e:
            current_sectors_df['涨停家数'] = 0
            logging.error(f"统计概念涨停家数失败: {e}")
        
        # 3. 计算每个概念的"主线分"
        analysis_results = []
        # 只分析资金排名前50的概念，聚焦核心
        for _, row in current_sectors_df.head(50).iterrows():
            theme_name = row['名称']
            current_rank = row['资金排名']
            
            # --- a. 资金强度分 (Flow_Strength_Score) ---
            strength_score = max(0, (51 - current_rank) / 50 * 10)  # 排名越靠前分数越高
            
            # --- b. 资金广度分 (Flow_Breadth_Score) ---
            limit_up_count = row['涨停家数']
            breadth_score = min(10, limit_up_count * 2)  # 每个涨停2分，满分10分
            
            # --- c. 资金持续性分 (Flow_Persistence_Score) ---
            # 更新持续性追踪器
            if theme_name in THEME_PERSISTENCE_TRACKER:
                # 如果这次仍在榜单前列（例如前30名），则计数+1
                if current_rank <= 30:
                    THEME_PERSISTENCE_TRACKER[theme_name]['consecutive_counts'] += 1
                else:  # 如果掉出前列，则计数清零
                    THEME_PERSISTENCE_TRACKER[theme_name]['consecutive_counts'] = 0
            else:
                # 新上榜的概念
                THEME_PERSISTENCE_TRACKER[theme_name] = {'consecutive_counts': 1 if current_rank <= 30 else 0}
            
            consecutive_days = THEME_PERSISTENCE_TRACKER[theme_name]['consecutive_counts']
            persistence_score = min(10, consecutive_days * 2)  # 每连续一天2分，满分10分

            # --- d. 【新增】预期差加分（资金加速度） ---
            anticipation_score = 0
            if rising_sectors is not None and not rising_sectors.empty and theme_name in rising_sectors['名称'].tolist():
                shift_score = rising_sectors[rising_sectors['名称'] == theme_name]['shift_score'].iloc[0]
                anticipation_score = shift_score * 10  # 将0-1的突变分放大到0-10分
                print(f"    🚀 {theme_name} 获得预期差加分: {anticipation_score:.1f}分 (突变分: {shift_score:.2f})")

            # --- e. 综合主线分计算（调整权重，加入预期差） ---
            W_ANTICIPATION = 0.2  # 给予20%的权重
            W_STRENGTH = 0.3
            W_PERSISTENCE = 0.2
            W_BREADTH = 0.3

            main_theme_score = (strength_score * W_STRENGTH +
                              persistence_score * W_PERSISTENCE +
                              breadth_score * W_BREADTH +
                              anticipation_score * W_ANTICIPATION)  # 加入预期差评分
            
            # --- f. 主线诊断 ---
            if main_theme_score >= MAIN_THEME_SCORE_THRESHOLD:
                diagnosis = "🌊 主线战场"
            elif main_theme_score >= 3:
                diagnosis = "边缘题材"
            else:
                diagnosis = "⚠️ 小插曲"
            
            # 格式化净流入金额
            net_inflow = convert_to_float(row['今日主力净流入-净额'])
            if abs(net_inflow) >= 100000000:  # 1亿以上
                net_inflow_str = f"{net_inflow / 100000000:.2f}亿"
            else:
                net_inflow_str = f"{net_inflow / 10000:.2f}万"
            
            analysis_results.append({
                '板块名称': theme_name,
                '资金排名': current_rank,
                '强度分': round(strength_score, 1),
                '持续分': consecutive_days,
                '广度分': limit_up_count,
                '预期差分': round(anticipation_score, 1),  # 新增预期差分
                '主线总分': round(main_theme_score, 2),
                '涨停家数': limit_up_count,
                '主力净流入': net_inflow_str,
                '诊断': diagnosis
            })
        
        # 4. 输出概念主线分析结果
        if analysis_results:
            concept_df_display = pd.DataFrame(analysis_results)
            # 按主线总分对所有板块进行排序
            concept_df_display.sort_values(by='主线总分', ascending=False, inplace=True)

            # 过滤掉资金为负的板块（这些是资金流出的地方，不是主战场）
            concept_df_display = concept_df_display[concept_df_display['主力净流入'].apply(
                lambda x: not (isinstance(x, str) and '-' in x)
            )]

            print(tabulate(concept_df_display.head(10), headers='keys', tablefmt='psql', showindex=False))

            # 5. 【核心修改】使用相对排名确认主线，而不是绝对分数
            if not concept_df_display.empty:
                # 将得分最高的板块定义为主线
                strong_themes = concept_df_display.head(3)['板块名称'].tolist() # 取前三名作为主线和支线
                print(f"\n🎯 【概念主战场确认】: 主线: {strong_themes[0]}, 支线: {', '.join(strong_themes[1:])}")
                return strong_themes
            else:
                print(f"\n🎯 【概念主战场确认】: 市场无明显合力方向。")
                return []
        else:
            print("❌ 无概念数据可分析")
            return []
    
    except Exception as e:
        logging.error(f"概念主线分析过程中发生错误: {e}")
        print(f"❌ 概念主线分析失败: {e}")
        return []

def analyze_industry_main_themes():
    """
    【行业主线识别】分析行业资金流，识别行业主线。
    使用与概念主线相同的评分逻辑，但专门针对行业板块。
    """
    global STRONG_SECTORS_LIST, THEME_PERSISTENCE_TRACKER
    
    try:
        print("\n" + "=" * 20 + " 行业主线识别系统 (观潮法V4+先手预判) " + "=" * 20)

        # 1. 【新增】获取资金流突变信息（虽然主要是概念数据，但可以作为市场整体预期差的参考）
        rising_sectors, _ = analyze_intraday_flow_shift()

        # 2. 获取行业资金流数据
        industry_sectors_df = get_sector_fund_flow_with_backup("行业资金流")

        if industry_sectors_df is None or industry_sectors_df.empty:
            print("❌ 所有行业资金流接口都失败，无法进行行业主线分析。")
            return []
        
        # 2. 统计行业涨停家数 (使用股票所属行业信息)
        try:
            zt_pool_df = safe_akshare_call('stock_zt_pool_em', date=datetime.now().strftime("%Y%m%d"))
            industry_limit_up_counts = {}
            if not zt_pool_df.empty:
                for _, row in zt_pool_df.iterrows():
                    stock_code = str(row['代码']).zfill(6)
                    industry = get_stock_industry(stock_code)
                    if industry != '未知':
                        industry_limit_up_counts[industry] = industry_limit_up_counts.get(industry, 0) + 1
            industry_sectors_df['涨停家数'] = industry_sectors_df['名称'].map(industry_limit_up_counts).fillna(0).astype(int)
        except Exception as e:
            industry_sectors_df['涨停家数'] = 0
            logging.error(f"统计行业涨停家数失败: {e}")
        
        # 3. 计算每个行业的"主线分"
        industry_analysis_results = []
        # 只分析资金排名前30的行业，聚焦核心
        for _, row in industry_sectors_df.head(30).iterrows():
            theme_name = f"[行业]{row['名称']}"  # 添加前缀区分行业和概念
            current_rank = row['资金排名']
            
            # --- a. 资金强度分 (Flow_Strength_Score) ---
            strength_score = max(0, (31 - current_rank) / 30 * 10)  # 排名越靠前分数越高
            
            # --- b. 资金广度分 (Flow_Breadth_Score) ---
            limit_up_count = row['涨停家数']
            breadth_score = min(10, limit_up_count * 2)  # 每个涨停2分，满分10分
            
            # --- c. 资金持续性分 (Flow_Persistence_Score) ---
            # 更新持续性追踪器
            if theme_name in THEME_PERSISTENCE_TRACKER:
                # 如果这次仍在榜单前列（例如前20名），则计数+1
                if current_rank <= 20:
                    THEME_PERSISTENCE_TRACKER[theme_name]['consecutive_counts'] += 1
                else:  # 如果掉出前列，则计数清零
                    THEME_PERSISTENCE_TRACKER[theme_name]['consecutive_counts'] = 0
            else:
                # 新上榜的行业
                THEME_PERSISTENCE_TRACKER[theme_name] = {'consecutive_counts': 1 if current_rank <= 20 else 0}
            
            consecutive_days = THEME_PERSISTENCE_TRACKER[theme_name]['consecutive_counts']
            persistence_score = min(10, consecutive_days * 2)  # 每连续一天2分，满分10分

            # --- d. 【新增】预期差加分（基于概念板块突变情况推断行业预期差） ---
            anticipation_score = 0
            # 对于行业，我们通过检查是否有相关概念板块在突变来判断预期差
            if rising_sectors is not None and not rising_sectors.empty:
                # 简化逻辑：如果有任何概念板块在突变，给行业一个基础的预期差分
                # 这里可以根据实际情况进一步优化，比如通过行业-概念映射关系
                base_anticipation = rising_sectors['shift_score'].mean() if not rising_sectors.empty else 0
                anticipation_score = base_anticipation * 5  # 行业预期差分相对保守，系数为5
                if anticipation_score > 0:
                    print(f"    🚀 {theme_name} 获得预期差加分: {anticipation_score:.1f}分 (市场突变均值: {base_anticipation:.2f})")

            # --- e. 综合主线分计算（调整权重，加入预期差） ---
            W_ANTICIPATION = 0.15  # 行业预期差权重相对保守，给予15%的权重
            W_STRENGTH = 0.35
            W_PERSISTENCE = 0.25
            W_BREADTH = 0.25

            main_theme_score = (strength_score * W_STRENGTH +
                              persistence_score * W_PERSISTENCE +
                              breadth_score * W_BREADTH +
                              anticipation_score * W_ANTICIPATION)  # 加入预期差评分
            
            # --- e. 主线诊断 ---
            if main_theme_score >= 7:
                diagnosis = "🔥 强势主线"
            elif main_theme_score >= 5:
                diagnosis = "📈 潜力主线"
            elif main_theme_score >= 3:
                diagnosis = "边缘题材"
            else:
                diagnosis = "⚠️ 小插曲"
            
            # 格式化净流入金额
            net_inflow_raw = row['今日主力净流入-净额']
            net_inflow = convert_to_float(net_inflow_raw)

            # 【修复】检查数据来源，同花顺接口返回的数据单位已经是万元
            # 如果数据来源是同花顺（通过字段特征判断），则不需要再除以10000
            if '行业指数' in industry_sectors_df.columns:
                # 同花顺接口的特征字段，数据单位已经是万元
                if abs(net_inflow) >= 10000:  # 1万万元 = 1亿元
                    net_inflow_str = f"{net_inflow / 10000:.2f}亿"
                else:
                    net_inflow_str = f"{net_inflow:.2f}万"
            else:
                # 其他接口，数据单位是元，需要转换
                if abs(net_inflow) >= 100000000:  # 1亿以上
                    net_inflow_str = f"{net_inflow / 100000000:.2f}亿"
                else:
                    net_inflow_str = f"{net_inflow / 10000:.2f}万"
            
            industry_analysis_results.append({
                '板块名称': theme_name,
                '资金排名': current_rank,
                '强度分': round(strength_score, 1),
                '持续分': consecutive_days,
                '广度分': limit_up_count,
                '预期差分': round(anticipation_score, 1),  # 新增预期差分
                '主线总分': round(main_theme_score, 2),
                '涨停家数': limit_up_count,
                '主力净流入': net_inflow_str,
                '诊断': diagnosis
            })
        
        # 4. 输出行业主线分析结果
        if industry_analysis_results:
            industry_df_display = pd.DataFrame(industry_analysis_results)
            # 按主线总分对所有板块进行排序
            industry_df_display.sort_values(by='主线总分', ascending=False, inplace=True)

            # 过滤掉资金为负的板块（这些是资金流出的地方，不是主战场）
            industry_df_display = industry_df_display[industry_df_display['主力净流入'].apply(
                lambda x: not (isinstance(x, str) and '-' in x)
            )]
            print(tabulate(industry_df_display, headers='keys', tablefmt='psql', showindex=False))

            # 5. 【核心修改】使用相对排名确认主线，而不是绝对分数
            if not industry_df_display.empty:
                # 将得分最高的板块定义为主线
                strong_themes = industry_df_display.head(3)['板块名称'].tolist() # 取前三名作为主线和支线
                print(f"\n🎯 【行业主战场确认】: 主线: {strong_themes[0]}, 支线: {', '.join(strong_themes[1:])}")
                return strong_themes
            else:
                print(f"\n🎯 【行业主战场确认】: 市场无明显合力方向。")
                return []
        else:
            print("❌ 无行业数据可分析")
            return []
    
    except Exception as e:
        logging.error(f"行业主线分析过程中发生错误: {e}")
        print(f"❌ 行业主线分析失败: {e}")
        return []

def analyze_limit_up_themes():
    """
    【V2 周期卡位版】分析当日涨停板，识别市场主线、总龙头，并捕捉"周期卡位"信号。
    - 更新全局变量 MARKET_THEMES_DATA, MARKET_LEADER_INFO
    - 【核心新增】比较新旧龙头，判断周期切换，更新 CYCLE_TRANSITION_THEME
    """
    global MARKET_THEMES_DATA, MARKET_LEADER_INFO, PREVIOUS_MARKET_LEADER_INFO, CYCLE_TRANSITION_THEME
    try:
        logging.info("--- [核心分析] 开始分析市场涨停梯队与主线题材 (周期卡位版) ---")

        # 1. 获取并清洗当日涨停池数据
        zt_pool_df = safe_akshare_call('stock_zt_pool_em', date=datetime.now().strftime("%Y%m%d"))
        if zt_pool_df.empty:
            logging.warning("今日涨停池为空")
            MARKET_THEMES_DATA = {}
            MARKET_LEADER_INFO = {'code': None, 'boards': 0}
            return

        # 清洗数据：过滤ST股票
        zt_pool_df = zt_pool_df[~zt_pool_df['名称'].str.contains('ST|退|N', na=False)]

        # 2. 寻找当前市场总龙头
        current_leader_code, current_leader_boards, current_leader_name = None, 0, None
        if not zt_pool_df.empty:
            market_leader_row = zt_pool_df.sort_values(by='连板数', ascending=False).iloc[0]
            current_leader_code = market_leader_row['代码']
            current_leader_boards = int(market_leader_row['连板数'])
            current_leader_name = market_leader_row['名称']

        # 将当前龙头信息存入临时变量，函数最后再更新全局变量
        current_leader_info = {'code': current_leader_code, 'boards': current_leader_boards}

        # 3. 【核心改进】周期卡位判断
        previous_leader_code = PREVIOUS_MARKET_LEADER_INFO['code']
        CYCLE_TRANSITION_THEME = None  # 每轮扫描前重置

        if previous_leader_code and current_leader_code and (previous_leader_code != current_leader_code):
            # 旧龙头是否陨落？ (即，不在今日的涨停池里)
            is_old_leader_fallen = previous_leader_code not in zt_pool_df['代码'].values

            # 获取新旧龙头的概念
            old_leader_concepts = get_stock_concepts(previous_leader_code)
            new_leader_concepts = get_stock_concepts(current_leader_code)

            # 取第一个核心概念进行比较
            old_theme = old_leader_concepts[0] if old_leader_concepts else "旧未知"
            new_theme = new_leader_concepts[0] if new_leader_concepts else "新未知"

            if is_old_leader_fallen and old_theme != new_theme and current_leader_boards > 1:
                CYCLE_TRANSITION_THEME = new_theme
                print("\n" + "*" * 25)
                print(
                    f"🔥【周期卡位信号】旧王({previous_leader_code})陨落，新王({current_leader_code} - {current_leader_name})登基！")
                print(f"   新主线可能诞生: 【{CYCLE_TRANSITION_THEME}】")
                print("*" * 25 + "\n")
                logging.info(
                    f"周期卡位信号：旧王{previous_leader_code}陨落，新王{current_leader_code}登基，新主线{CYCLE_TRANSITION_THEME}")

        # 4. （保留）聚合分析各题材强度
        themes_summary = {}
        if not zt_pool_df.empty:
            for _, row in zt_pool_df.iterrows():
                theme = row['所属行业']
                if theme not in themes_summary:
                    themes_summary[theme] = {'count': 0, 'leader_name': '', 'leader_boards': 0}
                themes_summary[theme]['count'] += 1
                if row['连板数'] > themes_summary[theme]['leader_boards']:
                    themes_summary[theme]['leader_boards'] = row['连板数']
                    themes_summary[theme]['leader_name'] = row['名称']

        # 5. 更新全局变量
        MARKET_THEMES_DATA = themes_summary
        MARKET_LEADER_INFO = current_leader_info
        PREVIOUS_MARKET_LEADER_INFO = current_leader_info.copy()  # 更新"记忆"

    except Exception as e:
        logging.error(f"分析市场主线题材失败: {e}", exc_info=True)

def analyze_today_mainline_and_limit_ups():
    """
    【核心函数】今日主线与涨停分析
    该函数是本次升级的核心，负责生成"今日主线与涨停分析"的完整战报。

    逻辑流程:
    1. 调用get_zt_pool_with_backup()获取当日完整的涨停池数据
    2. 调用analyze_main_themes_quantitative()获取当前市场认定的主线板块列表
    3. 遍历每一只涨停股票，通过get_stock_concepts()获取其概念，并判断是否属于主线板块
    4. 计算"封单强度"：新增一列seal_strength_ratio，计算公式为 seal_fund / circulating_market_cap
    5. 生成盘口语言解读：为每只股票生成market_interpretation
    6. 将所有涨停股按概念板块进行分组
    7. 在每个板块内，按first_seal_time（首次封板时间）升序排序
    8. 将所有板块按照主线评级、板块内涨停家数、板块内最高连板数的优先级进行排序

    Returns:
        dict: 包含用于生成最终Markdown表格的所有信息，以及市场高度、总览、情绪状态的摘要信息
    """
    try:
        from datetime import datetime
        import time as time_module

        current_time = datetime.now().strftime('%H:%M')
        today_date = datetime.now().strftime('%Y%m%d')

        print(f"\n🎯 启动今日主线与涨停分析 (截至 {current_time})...")

        # 1. 获取当日完整的涨停池数据
        print("📊 获取当日涨停池数据...")
        zt_pool_df = get_zt_pool_with_backup(today_date)

        if zt_pool_df.empty:
            print("❌ 无法获取涨停池数据")
            return {
                'market_height': {'boards': 0, 'leader_name': '无', 'leader_code': ''},
                'market_overview': {'limit_up': 0, 'limit_down': 0, 'seal_rate': 0},
                'emotion_status': '数据缺失',
                'mainline_sectors': [],
                'potential_sectors': [],
                'other_sectors': [],
                'detailed_report': '无数据可分析'
            }

        print(f"✅ 成功获取 {len(zt_pool_df)} 只涨停股数据")

        # 2. 获取当前市场认定的主线板块列表（简化版，避免循环调用）
        print("🎯 识别市场主线板块...")

        # 简化主线识别逻辑，避免调用复杂的量化分析（防止循环调用）
        strong_sectors = ['新材料', '稀土永磁', '碳化硅', '锂电池', '军民融合', '央国企改革']  # 基于当前市场热点的简化列表

        print(f"✅ 识别到 {len(strong_sectors)} 个主线板块: {', '.join(strong_sectors[:3])}")

        # 3. 分析每只涨停股票
        print("🔍 分析涨停股票详情...")
        analyzed_stocks = []

        for _, row in zt_pool_df.iterrows():
            stock_code = str(row.get('code', '')).zfill(6)
            stock_name = row.get('name', '')

            # 获取股票概念
            concepts = get_stock_concepts(stock_code)

            # 判断是否属于主线板块
            is_mainline = any(concept in strong_sectors for concept in concepts)
            main_concept = concepts[0] if concepts else '未知概念'

            # 计算封单强度
            seal_fund = float(row.get('封单资金', 0))
            circulating_cap = float(row.get('流通市值', 1))  # 避免除零
            seal_strength_ratio = (seal_fund / circulating_cap * 100) if circulating_cap > 0 else 0

            # 封单强度评级
            if seal_strength_ratio > 0.1:
                seal_strength_grade = '极强'
            elif seal_strength_ratio > 0.05:
                seal_strength_grade = '很强'
            elif seal_strength_ratio > 0.01:
                seal_strength_grade = '强'
            else:
                seal_strength_grade = '一般'

            # 生成盘口语言解读
            consecutive_boards = int(row.get('连板数', 1))
            market_interpretation = _generate_market_interpretation(
                stock_name, consecutive_boards, is_mainline, main_concept, seal_strength_grade
            )

            analyzed_stocks.append({
                'code': stock_code,
                'name': stock_name,
                'consecutive_boards': consecutive_boards,
                'first_seal_time': row.get('首次封板时间', '09:30:00'),
                'seal_fund': seal_fund,
                'seal_strength_ratio': seal_strength_ratio,
                'seal_strength_grade': seal_strength_grade,
                'main_concept': main_concept,
                'all_concepts': concepts,
                'is_mainline': is_mainline,
                'market_interpretation': market_interpretation,
                'change_pct': float(row.get('change_pct', 0)),
                'latest_price': float(row.get('latest_price', 0)),
                'turnover': float(row.get('turnover', 0)),
                'turnover_rate': float(row.get('turnover_rate', 0))
            })

        # 4. 按概念板块分组
        print("📋 按概念板块分组...")
        sector_groups = {}
        for stock in analyzed_stocks:
            concept = stock['main_concept']
            if concept not in sector_groups:
                sector_groups[concept] = []
            sector_groups[concept].append(stock)

        # 5. 在每个板块内按首次封板时间排序
        for concept, stocks in sector_groups.items():
            stocks.sort(key=lambda x: x['first_seal_time'])

        # 6. 计算市场高度和总览
        market_height = _calculate_market_height(analyzed_stocks)
        market_overview = _calculate_market_overview(analyzed_stocks)

        # 7. 按板块重要性排序
        sorted_sectors = _sort_sectors_by_importance(sector_groups, strong_sectors)

        result = {
            'current_time': current_time,
            'market_height': market_height,
            'market_overview': market_overview,
            'emotion_status': '强势期',  # 简化处理，实际应该从情绪分析获取
            'mainline_sectors': sorted_sectors['mainline'],
            'potential_sectors': sorted_sectors['potential'],
            'other_sectors': sorted_sectors['other'],
            'all_analyzed_stocks': analyzed_stocks,
            'sector_groups': sector_groups
        }

        print(f"✅ 分析完成，市场高度: {market_height['boards']}板")
        return result

    except Exception as e:
        logging.error(f"今日主线与涨停分析失败: {e}")
        print(f"❌ 今日主线与涨停分析失败: {e}")
        return {
            'market_height': {'boards': 0, 'leader_name': '分析失败', 'leader_code': ''},
            'market_overview': {'limit_up': 0, 'limit_down': 0, 'seal_rate': 0},
            'emotion_status': '分析失败',
            'mainline_sectors': [],
            'potential_sectors': [],
            'other_sectors': [],
            'detailed_report': f'分析失败: {e}'
        }


def analyze_today_limit_ups():
    """
    【升级版】分析今日涨停股，识别题材龙头和板块分布
    整合原有的analyze_limit_up_themes和analyze_first_board_concentration功能
    现在调用analyze_today_mainline_and_limit_ups()的结果，并打印出简洁版的梯队分析摘要

    Returns:
        dict: {
            'total_count': int,           # 总涨停数量
            'echelon_distribution': dict, # 梯队分布 {1: 45, 2: 8, 3: 3, ...}
            'sector_distribution': dict,  # 板块分布 {行业/概念: 股票数量}
            'leader_info': dict,         # 龙头信息
            'hot_sectors': list,         # 热门板块列表
            'detailed_report': str       # 详细报告
        }
    """
    global MARKET_THEMES_DATA, MARKET_LEADER_INFO

    print("\n" + "="*80)
    print("📊 【今日涨停梯队分析】")
    print("="*80)

    # 调用新的核心分析函数
    mainline_result = analyze_today_mainline_and_limit_ups()

    if not mainline_result or not mainline_result.get('all_analyzed_stocks'):
        print("❌ 无涨停数据可分析")
        return {
            'total_count': 0,
            'echelon_distribution': {},
            'sector_distribution': {},
            'leader_info': {'code': None, 'boards': 0},
            'hot_sectors': [],
            'detailed_report': '无数据'
        }

    analyzed_stocks = mainline_result['all_analyzed_stocks']
    market_height = mainline_result['market_height']

    # 统计梯队分布
    echelon_distribution = {}
    for stock in analyzed_stocks:
        boards = stock['consecutive_boards']
        echelon_distribution[boards] = echelon_distribution.get(boards, 0) + 1

    # 统计板块分布
    sector_distribution = {}
    for stock in analyzed_stocks:
        concept = stock['main_concept']
        sector_distribution[concept] = sector_distribution.get(concept, 0) + 1

    # 获取热门板块（按涨停数量排序）
    hot_sectors = sorted(sector_distribution.items(), key=lambda x: x[1], reverse=True)[:5]
    hot_sectors = [sector[0] for sector in hot_sectors]

    # 更新全局变量
    MARKET_LEADER_INFO = {
        'code': market_height['leader_code'],
        'name': market_height['leader_name'],
        'boards': market_height['boards']
    }

    # 打印简洁版摘要
    current_time = mainline_result.get('current_time', '未知')
    total_count = len(analyzed_stocks)

    print(f"### 今日涨停梯队分析 (截至 {current_time})")
    print(f"- 总涨停: {total_count}家")
    print(f"- 市场高度: {market_height['boards']}板 ({market_height['leader_name']})")

    # 连板梯队统计
    echelon_summary = []
    for boards in sorted(echelon_distribution.keys(), reverse=True):
        count = echelon_distribution[boards]
        echelon_summary.append(f"{boards}板: {count}家")
    print(f"- 连板梯队: {', '.join(echelon_summary)}")

    # 热门板块
    hot_sector_summary = []
    for i, (sector, count) in enumerate(sorted(sector_distribution.items(), key=lambda x: x[1], reverse=True)[:3]):
        hot_sector_summary.append(f"{sector}({count}家)")
    print(f"- 热门板块: {', '.join(hot_sector_summary)}")

    return {
        'total_count': total_count,
        'echelon_distribution': echelon_distribution,
        'sector_distribution': sector_distribution,
        'leader_info': MARKET_LEADER_INFO,
        'hot_sectors': hot_sectors,
        'detailed_report': f"分析了{total_count}只涨停股，市场高度{market_height['boards']}板"
    }


def _generate_market_interpretation(stock_name, consecutive_boards, is_mainline, main_concept, seal_strength_grade):
    """
    【辅助函数】生成盘口语言解读
    体现股票的市场地位，例如：
    - 总龙头："市场总龙头，情绪风向标，确认XX为主线。"
    - 板块内首个封板的个股："板块身位龙，率先卡位，引导板块情绪。"
    - 主线内的跟风股："主线内部跟风，承接良好。"
    - 非主线涨停股："独立题材，关注持续性。"
    """
    try:
        if consecutive_boards >= 5:
            # 总龙头级别
            if is_mainline:
                return f"市场总龙头，情绪风向标，确认{main_concept}为主线，封板{seal_strength_grade}。"
            else:
                return f"独立总龙头，{main_concept}题材引领者，封板{seal_strength_grade}，关注持续性。"
        elif consecutive_boards >= 3:
            # 高标级别
            if is_mainline:
                return f"主线高标，{main_concept}板块卡位龙头，封板{seal_strength_grade}，强化题材。"
            else:
                return f"独立高标，{main_concept}题材龙头，封板{seal_strength_grade}，题材分化。"
        elif consecutive_boards == 2:
            # 二板级别
            if is_mainline:
                return f"主线二板，{main_concept}板块跟风，封板{seal_strength_grade}，承接良好。"
            else:
                return f"独立二板，{main_concept}题材跟风，封板{seal_strength_grade}，关注分化。"
        else:
            # 首板级别
            if is_mainline:
                return f"主线首板，{main_concept}新进场资金，封板{seal_strength_grade}，题材扩散。"
            else:
                return f"独立首板，{main_concept}题材，封板{seal_strength_grade}，观察持续性。"
    except Exception as e:
        logging.warning(f"生成盘口语言解读失败: {e}")
        return f"{main_concept}题材，封板{seal_strength_grade}。"


def _calculate_market_height(analyzed_stocks):
    """
    【辅助函数】计算市场高度
    """
    if not analyzed_stocks:
        return {'boards': 0, 'leader_name': '无', 'leader_code': ''}

    # 找到连板数最高的股票
    max_boards = 0
    leader_info = {'boards': 0, 'leader_name': '无', 'leader_code': ''}

    for stock in analyzed_stocks:
        if stock['consecutive_boards'] > max_boards:
            max_boards = stock['consecutive_boards']
            leader_info = {
                'boards': max_boards,
                'leader_name': stock['name'],
                'leader_code': stock['code']
            }

    return leader_info


def _calculate_market_overview(analyzed_stocks):
    """
    【辅助函数】计算市场总览
    """
    if not analyzed_stocks:
        return {'limit_up': 0, 'limit_down': 0, 'seal_rate': 100}

    limit_up_count = len(analyzed_stocks)
    # 简化处理，实际应该从市场数据获取跌停数
    limit_down_count = 0
    seal_rate = 100  # 简化处理，实际应该计算炸板率

    return {
        'limit_up': limit_up_count,
        'limit_down': limit_down_count,
        'seal_rate': seal_rate
    }


def _sort_sectors_by_importance(sector_groups, strong_sectors):
    """
    【辅助函数】按板块重要性排序
    将所有板块按照主线评级、板块内涨停家数、板块内最高连板数的优先级进行排序
    """
    mainline_sectors = []
    potential_sectors = []
    other_sectors = []

    for concept, stocks in sector_groups.items():
        if not stocks:
            continue

        # 计算板块统计信息
        stock_count = len(stocks)
        max_boards = max(stock['consecutive_boards'] for stock in stocks)
        avg_seal_strength = sum(stock['seal_strength_ratio'] for stock in stocks) / stock_count

        sector_info = {
            'name': concept,
            'stocks': stocks,
            'stock_count': stock_count,
            'max_boards': max_boards,
            'avg_seal_strength': avg_seal_strength,
            'is_mainline': concept in strong_sectors
        }

        # 分类
        if concept in strong_sectors:
            mainline_sectors.append(sector_info)
        elif stock_count >= 2 or max_boards >= 2:
            potential_sectors.append(sector_info)
        else:
            other_sectors.append(sector_info)

    # 排序：按涨停家数和最高连板数排序
    def sort_key(sector):
        return (sector['stock_count'], sector['max_boards'], sector['avg_seal_strength'])

    mainline_sectors.sort(key=sort_key, reverse=True)
    potential_sectors.sort(key=sort_key, reverse=True)
    other_sectors.sort(key=sort_key, reverse=True)

    return {
        'mainline': mainline_sectors,
        'potential': potential_sectors,
        'other': other_sectors
    }


def analyze_leader_concept_flow(leader_stock_code):
    """
    【新增】分析最高板股票对应概念的资金流和股票池
    获取最高板股票的概念，然后分析该概念的资金流和成分股
    """
    try:
        logging.info(f"--- [概念分析] 开始分析最高板股票 {leader_stock_code} 的概念资金流 ---")

        # 1. 获取最高板股票的概念
        leader_concepts = get_stock_concepts(leader_stock_code)
        if not leader_concepts:
            logging.warning(f"未找到股票 {leader_stock_code} 的概念信息")
            return

        logging.info(f"最高板股票 {leader_stock_code} 的概念: {leader_concepts}")
        print(f"🎯 最高板股票概念: {', '.join(leader_concepts[:3])}{'...' if len(leader_concepts) > 3 else ''}")

        # 2. 获取概念资金流排名
        concept_flow_df = get_sector_fund_flow_with_backup("概念资金流")

        if concept_flow_df is None or concept_flow_df.empty:
            logging.warning("所有概念资金流接口都失败，无法获取概念资金流数据")
            return

        # 添加缺失的列
        if '资金排名' not in concept_flow_df.columns:
            concept_flow_df['资金排名'] = range(1, len(concept_flow_df) + 1)
        if '序号' not in concept_flow_df.columns:
            concept_flow_df['序号'] = concept_flow_df['资金排名']

        # 3. 分析最高板股票相关概念的资金流情况
        related_concept_flows = []
        for concept in leader_concepts:
            concept_flow_info = concept_flow_df[concept_flow_df['名称'] == concept]
            if not concept_flow_info.empty:
                flow_data = concept_flow_info.iloc[0]
                related_concept_flows.append({
                    'concept': concept,
                    'rank': flow_data['资金排名'],
                    'change_pct': convert_to_float(flow_data.get('涨跌幅', 0)),
                    'main_net_inflow': convert_to_float(flow_data.get('今日主力净流入-净额', 0))
                })

        if related_concept_flows:
            # 按主力净流入排序
            related_concept_flows.sort(key=lambda x: x['main_net_inflow'], reverse=True)

            print(f"\n📊 最高板股票相关概念资金流TOP3:")
            for i, flow in enumerate(related_concept_flows[:3]):
                print(f"   {i+1}. {flow['concept']}: 排名{flow['rank']}, "
                      f"涨跌{flow['change_pct']:.2f}%, "
                      f"主力净流入{flow['main_net_inflow']/100000000:.2f}亿")

            # 4. 分析最强概念的成分股
            strongest_concept = related_concept_flows[0]['concept']
            analyze_concept_stocks(strongest_concept)

            # --- 【新增】保存概念分析数据到本地 ---
            save_concept_analysis_data(leader_stock_code, leader_concepts, related_concept_flows, strongest_concept)

    except Exception as e:
        logging.error(f"分析最高板股票概念资金流失败: {e}")

def analyze_concept_stocks(concept_name):
    """
    【新增】分析指定概念的成分股，寻找符合买入条件的股票
    """
    try:
        logging.info(f"--- [成分股分析] 开始分析概念 {concept_name} 的成分股 ---")

        # 1. 获取概念成分股
        try:
            import akshare as ak
            concept_stocks_df = ak.stock_board_concept_cons_em(symbol=concept_name)
            if concept_stocks_df.empty:
                logging.warning(f"概念 {concept_name} 没有成分股数据")
                return

            concept_stocks_df['代码'] = concept_stocks_df['代码'].astype(str).str.zfill(6)
            logging.info(f"概念 {concept_name} 包含 {len(concept_stocks_df)} 只成分股")

        except Exception as e:
            logging.error(f"获取概念 {concept_name} 成分股失败: {e}")
            return

        # 2. 获取这些股票的资金流数据
        try:
            from market_data_provider import get_individual_fund_flow_with_backup
            stock_df = get_individual_fund_flow_with_backup("个股资金流")
            if stock_df is None or stock_df.empty:
                logging.warning("无法获取个股资金流数据")
                return

            # 确保代码格式一致
            stock_df['代码'] = stock_df['代码'].astype(str).str.zfill(6)

        except Exception as e:
            logging.error(f"获取个股资金流数据失败: {e}")
            return

        # 3. 分析概念成分股中符合买入条件的股票
        concept_buy_candidates = []

        for _, concept_stock in concept_stocks_df.iterrows():
            stock_code = concept_stock['代码']
            stock_name = concept_stock['名称']

            # 在资金流数据中查找该股票
            stock_flow_info = stock_df[stock_df['代码'] == stock_code]
            if stock_flow_info.empty:
                continue

            flow_data = stock_flow_info.iloc[0]
            buy_score = 0
            reasons = []

            # 基础评分逻辑
            main_net_inflow = convert_to_float(flow_data.get('今日主力净流入-净额', 0))
            change_pct = convert_to_float(flow_data.get('今日涨跌幅', 0))

            # 资金流入加分
            if main_net_inflow > 10000000:  # 1000万以上
                buy_score += 3
                reasons.append("资金流入")

            # 涨幅适中加分
            if 2 <= change_pct <= 7:
                buy_score += 2
                reasons.append("涨幅适中")

            # 概念龙头加分
            if stock_name == concept_stocks_df.iloc[0]['名称']:  # 第一只通常是龙头
                buy_score += 2
                reasons.append("概念龙头")

            if buy_score >= 4:  # 降低阈值，专门针对概念股
                concept_buy_candidates.append({
                    'code': stock_code,
                    'name': stock_name,
                    'score': buy_score,
                    'main_net_inflow': main_net_inflow,
                    'change_pct': change_pct,
                    'reasons': reasons
                })

        # 4. 输出结果
        if concept_buy_candidates:
            concept_buy_candidates.sort(key=lambda x: x['score'], reverse=True)

            print(f"\n🎯 概念 {concept_name} 中发现 {len(concept_buy_candidates)} 只符合条件的股票:")
            for i, candidate in enumerate(concept_buy_candidates[:5]):  # 显示前5只
                print(f"   {i+1}. {candidate['code']} {candidate['name']}: "
                      f"得分{candidate['score']}, "
                      f"主力净流入{candidate['main_net_inflow']/10000:.0f}万, "
                      f"涨幅{candidate['change_pct']:.2f}%, "
                      f"理由: {'+'.join(candidate['reasons'])}")

            # 将符合条件的概念股加入全局买入候选列表
            global CONCEPT_BUY_CANDIDATES
            if 'CONCEPT_BUY_CANDIDATES' not in globals():
                CONCEPT_BUY_CANDIDATES = []

            for candidate in concept_buy_candidates:
                CONCEPT_BUY_CANDIDATES.append({
                    'concept': concept_name,
                    'stock_code': candidate['code'],
                    'stock_name': candidate['name'],
                    'score': candidate['score'],
                    'reasons': candidate['reasons']
                })
        else:
            print(f"   概念 {concept_name} 中暂无符合条件的股票")

    except Exception as e:
        logging.error(f"分析概念 {concept_name} 成分股失败: {e}")

def save_concept_analysis_data(leader_stock_code, leader_concepts, related_concept_flows, strongest_concept):
    """
    【新增】保存概念分析数据到本地文件
    """
    try:
        os.makedirs('data', exist_ok=True)

        # 生成文件名
        date_str = datetime.now().strftime('%Y%m%d')
        concept_analysis_file = f'data/concept_analysis_{date_str}.json'
        csv_file = f'data/concept_flow_{date_str}.csv'

        # 准备保存的数据
        analysis_data = {
            'timestamp': datetime.now().isoformat(),
            'leader_stock_code': leader_stock_code,
            'leader_concepts': leader_concepts,
            'related_concept_flows': related_concept_flows,
            'strongest_concept': strongest_concept,
            'market_leader_info': MARKET_LEADER_INFO.copy(),
            'concept_buy_candidates': CONCEPT_BUY_CANDIDATES.copy() if CONCEPT_BUY_CANDIDATES else []
        }

        # 读取现有数据（如果存在）
        existing_data = []
        if os.path.exists(concept_analysis_file):
            try:
                with open(concept_analysis_file, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
                if not isinstance(existing_data, list):
                    existing_data = [existing_data]  # 兼容旧格式
            except Exception as e:
                logging.warning(f"读取现有概念分析文件失败: {e}")
                existing_data = []

        # 追加新数据
        existing_data.append(analysis_data)

        # 保存JSON文件
        with open(concept_analysis_file, 'w', encoding='utf-8') as f:
            json.dump(existing_data, f, ensure_ascii=False, indent=2, default=str)

        logging.info(f"概念分析数据已保存到: {concept_analysis_file}")
        print(f"💾 概念分析数据已保存到: {concept_analysis_file}")

        # 同时保存CSV格式的概念资金流数据（便于Excel查看）
        if related_concept_flows:
            concept_flow_df = pd.DataFrame(related_concept_flows)

            # 如果有现有的概念资金流数据，合并
            try:
                concept_flow_full_df = get_sector_fund_flow_with_backup("概念资金流")
                if concept_flow_full_df is not None and not concept_flow_full_df.empty:
                    combined_df = concept_flow_full_df
                else:
                    combined_df = concept_flow_df
            except:
                combined_df = concept_flow_df

            combined_df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            logging.info(f"概念资金流CSV数据已保存到: {csv_file}")
            print(f"📊 概念资金流CSV数据已保存到: {csv_file}")

        return True

    except Exception as e:
        logging.error(f"保存概念分析数据失败: {e}")
        print(f"❌ 保存概念分析数据失败: {e}")
        return False

def analyze_main_themes_quantitative():
    """
    【V6.0 量化评分版】
    主函数，依次调用通用分析模块，分别对"行业"和"概念"进行主线强度量化评分。
    """
    global STRONG_SECTORS_LIST

    def _run_quantitative_analysis(sector_type: str):
        """
        通用的主线量化分析模块。
        :param sector_type: "行业" 或 "概念"
        """
        global STRONG_SECTORS_LIST
        title = f"{sector_type}主线强度量化评分模型 (V6.0)"
        print("\n" + "=" * 25 + f" {title} " + "=" * 25)

        try:
            # 1. 数据准备
            flow_type = "行业资金流" if sector_type == "行业" else "概念资金流"
            sectors_df = get_sector_fund_flow_with_backup(flow_type)
            if sectors_df is None or sectors_df.empty:
                print(f"❌ 核心数据({flow_type})获取失败，无法执行{sector_type}主线分析。")
                if sector_type == "概念": STRONG_SECTORS_LIST = []
                return

            limit_up_counts = _get_limit_up_counts(sector_type)
            high_gain_counts = _get_high_gain_counts(sector_type)

            sectors_df['涨停家数'] = sectors_df['名称'].map(limit_up_counts).fillna(0).astype(int)
            sectors_df['大涨家数'] = sectors_df['名称'].map(high_gain_counts).fillna(0).astype(int)

            # 确保资金排名列存在
            if '资金排名' not in sectors_df.columns:
                sectors_df.sort_values(by='今日主力净流入-净额', ascending=False, inplace=True)
                sectors_df.reset_index(drop=True, inplace=True)
                sectors_df['资金排名'] = sectors_df.index + 1

            # 2. 定义评分权重 (此为核心心法体现，可根据盘面灵活调整)
            W_RANK = 0.25  # 强度：资金排名权重
            W_LIMIT_UP = 0.30  # 广度：涨停家数权重，这是最强的攻击信号
            W_HIGH_GAIN = 0.15  # 广度：大涨助攻权重
            W_CHANGE_PCT = 0.10  # 速度：板块自身涨速权重
            W_PROMOTION = 0.20  # 【新增】晋级成功率权重，资金和强度的共振

            # 3. 遍历和评分
            analysis_results = []
            scan_depth = 30  # 只分析资金最关注的前30个板块
            for index, row in sectors_df.head(scan_depth).iterrows():
                # 强度分 (0-10)
                rank_score = max(0, (scan_depth - (row['资金排名'] - 1)) / scan_depth * 10)
                # 广度分 (0-10)
                limit_up_score = min(10, row['涨停家数'] * 2)  # 每家涨停得2分
                high_gain_score = min(10, row['大涨家数'] * 1)  # 每家大涨得1分
                # 速度分 (0-10)
                change_pct_score = min(10, row['今日涨跌幅'] * 2 if row['今日涨跌幅'] > 0 else 0)

                # 【新增】晋级成功率分 (0-10)
                promotion_score = _calculate_sector_promotion_score(row['名称'], sector_type)

                # 总分计算（加入晋级成功率）
                total_score = (rank_score * W_RANK + limit_up_score * W_LIMIT_UP +
                               high_gain_score * W_HIGH_GAIN + change_pct_score * W_CHANGE_PCT +
                               promotion_score * W_PROMOTION)

                analysis_results.append({
                    '板块名称': row['名称'],
                    '主线总分': round(total_score, 2),
                    '资金排名': row['资金排名'],
                    '涨停家数': row['涨停家数'],
                    '大涨家数': row['大涨家数'],
                    '板块涨幅': f"{row['今日涨跌幅']:.2f}%",
                    '主力净流入': f"{convert_to_float(row['今日主力净流入-净额'])/100000000:.2f}亿"
                })

            # 4. 输出结果
            if analysis_results:
                # 按总分排序
                analysis_results.sort(key=lambda x: x['主线总分'], reverse=True)

                # 显示前10名
                display_df = pd.DataFrame(analysis_results[:10])
                print(tabulate(display_df, headers='keys', tablefmt='psql', showindex=False))

                # 更新强势板块列表（只有概念板块才更新全局列表）
                if sector_type == "概念":
                    STRONG_SECTORS_LIST = [result['板块名称'] for result in analysis_results if result['主线总分'] >= 6.0]
                    if STRONG_SECTORS_LIST:
                        print(f"\n🎯 【{sector_type}主战场确认】: {', '.join(STRONG_SECTORS_LIST[:3])}")
                    else:
                        print(f"\n🎯 【{sector_type}主战场确认】: {sector_type}板块混沌，暂无明确主线。")

                return analysis_results[:5]  # 返回前5名
            else:
                print(f"❌ 无{sector_type}数据可分析")
                return []

        except Exception as e:
            logging.error(f"{sector_type}主线量化分析失败: {e}")
            print(f"❌ {sector_type}主线量化分析失败: {e}")
            return []

    def _get_limit_up_counts(sector_type):
        """获取各板块的涨停家数统计"""
        try:
            zt_pool_df = safe_akshare_call('stock_zt_pool_em', date=datetime.now().strftime("%Y%m%d"))
            counts = {}
            if not zt_pool_df.empty:
                for _, row in zt_pool_df.iterrows():
                    stock_code = str(row['代码']).zfill(6)
                    if sector_type == "概念":
                        concepts = get_stock_concepts(stock_code)
                        for concept in concepts:
                            counts[concept] = counts.get(concept, 0) + 1
                    else:  # 行业
                        industry = get_stock_industry(stock_code)
                        if industry != '未知':
                            counts[industry] = counts.get(industry, 0) + 1
            return counts
        except Exception as e:
            logging.error(f"获取{sector_type}涨停统计失败: {e}")
            return {}

    def _get_high_gain_counts(sector_type):
        """获取各板块的大涨家数统计（涨幅5%以上）"""
        try:
            from market_data_provider import get_individual_fund_flow_with_backup
            stock_df = get_individual_fund_flow_with_backup("个股资金流")
            counts = {}
            if stock_df is not None and not stock_df.empty:
                # 筛选涨幅5%以上的股票
                high_gain_stocks = stock_df[stock_df['今日涨跌幅'] >= 5.0]
                for _, row in high_gain_stocks.iterrows():
                    stock_code = str(row['代码']).zfill(6)
                    if sector_type == "概念":
                        concepts = get_stock_concepts(stock_code)
                        for concept in concepts:
                            counts[concept] = counts.get(concept, 0) + 1
                    else:  # 行业
                        industry = get_stock_industry(stock_code)
                        if industry != '未知':
                            counts[industry] = counts.get(industry, 0) + 1
            return counts
        except Exception as e:
            logging.error(f"获取{sector_type}大涨统计失败: {e}")
            return {}

    # 执行分析
    try:
        print("\n🎯 启动主线强度量化评分系统...")

        # 先分析行业主线
        industry_results = _run_quantitative_analysis("行业")

        # 再分析概念主线
        concept_results = _run_quantitative_analysis("概念")

        return {
            'industry_results': industry_results,
            'concept_results': concept_results
        }

    except Exception as e:
        logging.error(f"主线量化分析系统失败: {e}")
        print(f"❌ 主线量化分析系统失败: {e}")
        return {'industry_results': [], 'concept_results': []}

def analyze_first_board_concentration():
    """
    【新增功能】分析首板涨停股票的概念和行业板块聚集度。
    """
    try:
        print("\n" + "=" * 20 + " 首板股票板块聚集度分析 " + "=" * 20)

        # 获取涨停池数据
        zt_pool_df = safe_akshare_call('stock_zt_pool_em', date=datetime.now().strftime("%Y%m%d"))

        if zt_pool_df is None or zt_pool_df.empty:
            print("未获取到涨停股池数据，无法分析首板聚集度。")
            logging.warning("未获取到涨停股池数据，无法分析首板聚集度。")
            return

        first_board_df = zt_pool_df[zt_pool_df['连板数'] == 1].copy()

        if first_board_df.empty:
            print("今日暂无首板涨停股票。")
            logging.info("今日暂无首板涨停股票。")
            return

        print(f"发现 {len(first_board_df)} 只首板涨停股票，开始分析板块分布...")
        logging.info(f"发现 {len(first_board_df)} 只首板涨停股票，开始分析板块分布...")

        concept_concentration = {}
        industry_concentration = {}

        print("🔄 开始分析首板股票的概念和行业分布...")

        # 批量获取所有首板股票的行业信息
        from market_data_provider import batch_get_stock_industries
        stock_codes = [str(row['代码']).zfill(6) for _, row in first_board_df.iterrows()]
        print(f"📊 批量获取{len(stock_codes)}只首板股票的行业信息...")
        industries_dict = batch_get_stock_industries(stock_codes)

        for idx, row in first_board_df.iterrows():
            stock_code = str(row['代码']).zfill(6)
            stock_name = row['名称']

            # 统计概念
            concepts = get_stock_concepts(stock_code)
            for concept in concepts:
                concept_concentration[concept] = concept_concentration.get(concept, 0) + 1

            # 统计行业 - 优先使用批量获取的结果
            industry = None

            # 1. 首先尝试从涨停池数据本身获取
            if '所属行业' in row and pd.notna(row['所属行业']) and row['所属行业'] not in ['', '未知']:
                industry = row['所属行业']
                print(f"  📊 {stock_code}({stock_name}) 从数据获取行业: {industry}")

            # 2. 如果数据中没有，使用批量获取的结果
            if not industry or industry == '未知':
                industry = industries_dict.get(stock_code, '未知')
                if industry != '未知':
                    print(f"  📊 {stock_code}({stock_name}) 通过批量查询获取行业: {industry}")
                else:
                    print(f"  ❌ {stock_code}({stock_name}) 无法获取行业信息")

            # 3. 统计有效的行业信息
            if industry and industry != '未知':
                industry_concentration[industry] = industry_concentration.get(industry, 0) + 1

        # --- 展示概念板块聚集度 ---
        if concept_concentration:
            concept_df = pd.DataFrame(list(concept_concentration.items()), columns=['板块名称', '首板家数'])
            concept_df.sort_values(by='首板家数', ascending=False, inplace=True)
            print("\n--- 今日首板聚集度最高的概念板块 TOP 10 ---")
            print(tabulate(concept_df.head(10), headers='keys', tablefmt='psql', showindex=False))
            logging.info(f"首板最集中的概念板块: {concept_df.iloc[0]['板块名称']} ({concept_df.iloc[0]['首板家数']}家)")
        else:
            print("未能分析出首板股票的概念板块分布。")

        # --- 展示行业板块聚集度 ---
        if industry_concentration:
            industry_df = pd.DataFrame(list(industry_concentration.items()), columns=['板块名称', '首板家数'])
            industry_df.sort_values(by='首板家数', ascending=False, inplace=True)
            print("\n--- 今日首板聚集度最高的行业板块 TOP 10 ---")
            print(tabulate(industry_df.head(10), headers='keys', tablefmt='psql', showindex=False))
            logging.info(f"首板最集中的行业板块: {industry_df.iloc[0]['板块名称']} ({industry_df.iloc[0]['首板家数']}家)")
        else:
            print("未能分析出首板股票的行业板块分布。")

        return {
            'concept_concentration': concept_concentration,
            'industry_concentration': industry_concentration,
            'first_board_count': len(first_board_df)
        }

    except Exception as e:
        logging.error(f"分析首板聚集度失败: {e}")
        print(f"❌ 分析首板聚集度失败: {e}")
        return None

def task_analyze_main_themes_V4_backtest(current_snapshot_df):
    """
    【回测专用 V4.1 预期差版】分析并更新全局的强势板块列表。
    新增"变化率"评分，用于捕捉从弱转强，最具"预期差"的新兴主线。
    """
    global STRONG_SECTORS_LIST, THEME_PERSISTENCE_TRACKER

    try:
        timestamp_str = current_snapshot_df['timestamp'].iloc[0].strftime(
            '%H:%M:%S') if not current_snapshot_df.empty else "未知时间"
        print("\n" + "=" * 25 + f" 主线识别系统 (回测@{timestamp_str}) " + "=" * 25)

        if current_snapshot_df.empty:
            STRONG_SECTORS_LIST = []
            return

        current_sectors_df = current_snapshot_df.copy()
        current_sectors_df.rename(columns={'排名': '资金排名'}, inplace=True)

        # 模拟涨停池数据
        zt_pool_df = current_sectors_df[current_sectors_df['今日涨跌幅'] >= 9.8]
        concept_limit_up_counts = {}
        if not zt_pool_df.empty:
            zt_pool_df = zt_pool_df[~zt_pool_df['名称'].str.contains('ST|退|N', na=False)]
            for _, row in zt_pool_df.iterrows():
                concepts = get_stock_concepts(str(row['代码']).zfill(6))
                for concept in concepts:
                    concept_limit_up_counts[concept] = concept_limit_up_counts.get(concept, 0) + 1
        current_sectors_df['涨停家数'] = current_sectors_df['名称'].map(concept_limit_up_counts).fillna(0).astype(int)

        analysis_results = []
        for _, row in current_sectors_df.head(50).iterrows():
            theme_name = row['名称']
            current_rank = row['资金排名']

            strength_score = max(0, (51 - current_rank) / 50 * 10)
            breadth_score = min(10, row['涨停家数'] * 2)

            # --- V4.1 核心升级：引入"预期差"评分 ---
            rank_change_score = 0
            previous_data = THEME_PERSISTENCE_TRACKER.get(theme_name)

            if previous_data:
                # 如果这次仍在榜单前列，则持续性计数+1
                if current_rank <= 30:
                    previous_data['consecutive_counts'] += 1
                else:
                    previous_data['consecutive_counts'] = 0

                # 计算排名变化分（预期差）
                rank_change = previous_data.get('last_rank', 100) - current_rank
                if rank_change > 20:  # 排名大幅跃升
                    rank_change_score = min(10, rank_change / 10)  # 每提升10名得1分
                elif rank_change > 0:  # 排名小幅提升
                    rank_change_score = min(5, rank_change / 5)  # 每提升5名得1分

                # 更新记录
                previous_data['last_rank'] = current_rank
            else:
                # 新上榜的概念
                THEME_PERSISTENCE_TRACKER[theme_name] = {
                    'consecutive_counts': 1 if current_rank <= 30 else 0,
                    'last_rank': current_rank
                }

            consecutive_days = THEME_PERSISTENCE_TRACKER[theme_name]['consecutive_counts']
            persistence_score = min(10, consecutive_days * 2)

            # V4.1 综合主线分计算（加入预期差）
            main_theme_score = (strength_score * 0.3 + persistence_score * 0.3 +
                              breadth_score * 0.3 + rank_change_score * 0.1)

            # 主线诊断
            if main_theme_score >= 6:
                diagnosis = "🌊 主线战场"
            elif main_theme_score >= 3:
                diagnosis = "边缘题材"
            else:
                diagnosis = "⚠️ 小插曲"

            # 格式化净流入金额
            net_inflow = convert_to_float(row.get('今日主力净流入-净额', 0))
            if abs(net_inflow) >= 100000000:
                net_inflow_str = f"{net_inflow / 100000000:.2f}亿"
            else:
                net_inflow_str = f"{net_inflow / 10000:.2f}万"

            analysis_results.append({
                '板块名称': theme_name,
                '资金排名': current_rank,
                '强度分': round(strength_score, 1),
                '持续分': consecutive_days,
                '广度分': row['涨停家数'],
                '预期差分': round(rank_change_score, 1),
                '主线总分': round(main_theme_score, 2),
                '主力净流入': net_inflow_str,
                '诊断': diagnosis
            })

        # 输出分析结果
        if analysis_results:
            concept_df_display = pd.DataFrame(analysis_results)
            # 过滤掉资金流入为负的概念
            concept_df_display = concept_df_display[concept_df_display['主力净流入'].apply(
                lambda x: not (isinstance(x, str) and ('-' in x))
            )]
            print(tabulate(concept_df_display.head(10), headers='keys', tablefmt='psql', showindex=False))

            # 更新强势板块列表
            STRONG_SECTORS_LIST = [result['板块名称'] for result in analysis_results
                                 if pd.to_numeric(result['主线总分'], errors='coerce') >= 6]
            if STRONG_SECTORS_LIST:
                print(f"\n🎯 【主战场确认】: {', '.join(STRONG_SECTORS_LIST[:3])}")
            else:
                print(f"\n🎯 【主战场确认】: 板块混沌，暂无明确主线。")

    except Exception as e:
        logging.error(f"回测主线分析失败: {e}")
        print(f"❌ 回测主线分析失败: {e}")
        STRONG_SECTORS_LIST = []

def analyze_limit_up_themes_backtest(current_snapshot_df):
    """
    【回测版本】分析当前时刻的涨停梯队与主线题材

    与实时版本的区别：
    1. 不调用 ak.stock_zt_pool_em，而是基于当前快照模拟涨停池
    2. 使用快照数据中的涨跌幅来判断涨停股票
    """
    global MARKET_THEMES_DATA, MARKET_LEADER_INFO, PREVIOUS_MARKET_LEADER_INFO, CYCLE_TRANSITION_THEME

    try:
        timestamp_str = current_snapshot_df['timestamp'].iloc[0].strftime('%H:%M:%S') if not current_snapshot_df.empty else "未知时间"
        logging.info(f"--- [回测分析] 开始分析市场涨停梯队与主线题材 (@{timestamp_str}) ---")

        # 1. 基于快照数据模拟涨停池
        zt_pool_df = current_snapshot_df[current_snapshot_df['今日涨跌幅'] >= 9.8].copy()

        if zt_pool_df.empty:
            logging.warning("当前时刻无涨停股票")
            MARKET_THEMES_DATA = {}
            MARKET_LEADER_INFO = {'code': None, 'boards': 0}
            return

        # 清洗数据：过滤ST股票
        zt_pool_df = zt_pool_df[~zt_pool_df['名称'].str.contains('ST|退|N', na=False)]

        # 2. 模拟连板数（简化处理，基于涨幅估算）
        def estimate_boards(change_pct):
            if change_pct >= 19.5:  # 接近20%
                return 2
            elif change_pct >= 9.8:  # 涨停
                return 1
            else:
                return 0

        zt_pool_df['连板数'] = zt_pool_df['今日涨跌幅'].apply(estimate_boards)

        # 3. 寻找当前市场总龙头
        current_leader_code, current_leader_boards, current_leader_name = None, 0, None
        if not zt_pool_df.empty:
            market_leader_row = zt_pool_df.sort_values(by='连板数', ascending=False).iloc[0]
            current_leader_code = str(market_leader_row['代码']).zfill(6)
            current_leader_boards = int(market_leader_row['连板数'])
            current_leader_name = market_leader_row['名称']

        current_leader_info = {'code': current_leader_code, 'boards': current_leader_boards}

        # 4. 周期卡位判断（简化版）
        previous_leader_code = PREVIOUS_MARKET_LEADER_INFO.get('code')
        CYCLE_TRANSITION_THEME = None

        if (previous_leader_code and current_leader_code and
            previous_leader_code != current_leader_code and current_leader_boards > 1):

            new_leader_concepts = get_stock_concepts(current_leader_code)
            new_theme = new_leader_concepts[0] if new_leader_concepts else "新未知"

            CYCLE_TRANSITION_THEME = new_theme
            print(f"🔥【回测-周期卡位信号】新王({current_leader_code} - {current_leader_name})登基！")
            print(f"   新主线可能诞生: 【{CYCLE_TRANSITION_THEME}】")

        # 5. 聚合分析各题材强度
        themes_summary = {}
        if not zt_pool_df.empty:
            for _, row in zt_pool_df.iterrows():
                stock_code = str(row['代码']).zfill(6)
                concepts = get_stock_concepts(stock_code)

                # 使用第一个概念作为主要概念
                theme = concepts[0] if concepts else '未知概念'

                if theme not in themes_summary:
                    themes_summary[theme] = {'count': 0, 'leader_name': '', 'leader_boards': 0}
                themes_summary[theme]['count'] += 1
                if row['连板数'] > themes_summary[theme]['leader_boards']:
                    themes_summary[theme]['leader_boards'] = row['连板数']
                    themes_summary[theme]['leader_name'] = row['名称']

        # 6. 更新全局变量
        MARKET_THEMES_DATA = themes_summary
        MARKET_LEADER_INFO = current_leader_info
        PREVIOUS_MARKET_LEADER_INFO = current_leader_info.copy()

    except Exception as e:
        logging.error(f"回测涨停分析失败: {e}")
        MARKET_THEMES_DATA = {}
        MARKET_LEADER_INFO = {'code': None, 'boards': 0}


def analyze_yesterday_limit_up_performance(yesterday_date, today_date, yesterday_zt_df=None, today_zt_df=None):
    """
    【核心功能】昨日涨停股复盘及晋级分析

    深度剖析前一交易日的涨停股在当日的表现，从而识别出市场最强的风口、
    辨识度最高的龙头，并量化赚钱效应与亏钱效应，为当日的交易决策提供最关键的情报支持。

    Args:
        yesterday_date: str, 昨日日期 YYYYMMDD格式
        today_date: str, 今日日期 YYYYMMDD格式
        yesterday_zt_df: DataFrame, 昨日涨停股池数据（可选，避免重复获取）
        today_zt_df: DataFrame, 今日涨停股池数据（可选，避免重复获取）

    Returns:
        dict: 包含完整分析结果的字典
    """
    print("\n" + "=" * 50)
    print("🎯 昨日涨停股复盘及晋级分析 (Yesterday's Limit-Up Review and Promotion Analysis)")
    print("=" * 50)

    try:
        # 1. 获取昨日涨停池数据（如果没有传入，则获取）
        if yesterday_zt_df is None:
            print(f"📊 获取 {yesterday_date} 涨停池数据...")
            yesterday_zt_df = get_zt_pool_with_backup(yesterday_date)
            if yesterday_zt_df.empty:
                print(f"❌ 无法获取 {yesterday_date} 涨停池数据")
                return {}
            print(f"✅ 成功获取昨日涨停股 {len(yesterday_zt_df)} 只")
        else:
            print(f"✅ 使用已获取的昨日涨停股数据 {len(yesterday_zt_df)} 只")

        # 2. 获取今日涨停池数据（用于判断是否晋级）
        if today_zt_df is None:
            from market_data_provider import is_trading_day_by_date

            # 检查今日是否为交易日
            is_today_trading = is_trading_day_by_date(today_date)
            if is_today_trading:
                print(f"📊 获取 {today_date} 涨停池数据...")
                today_zt_df = get_zt_pool_with_backup(today_date)
                print(f"✅ 今日涨停股 {len(today_zt_df)} 只")
            else:
                print(f"📅 {today_date} 为非交易日，跳过今日涨停池数据获取")
                today_zt_df = pd.DataFrame()  # 非交易日返回空DataFrame
        else:
            print(f"✅ 使用已获取的今日涨停股数据 {len(today_zt_df)} 只")

        # 3. 获取昨日涨停股的今日表现数据
        yesterday_codes = yesterday_zt_df['代码'].tolist()
        print(f"📊 获取 {len(yesterday_codes)} 只昨日涨停股的今日表现...")

        # 3.1 获取今日收盘行情数据
        today_performance_df = get_current_stock_quotes(yesterday_codes)

        # 3.2 获取集合竞价数据
        print(f"🔍 获取 {len(yesterday_codes)} 只股票的集合竞价数据...")
        auction_start_time = time_module.time()
        auction_df = get_stocks_auction_info_with_cache(yesterday_codes, today_date)
        auction_duration = time_module.time() - auction_start_time
        print(f"✅ 竞价数据获取完成，耗时: {auction_duration:.1f}秒")

        # 3.3 获取分时数据
        intraday_df = get_stocks_intraday_pattern(yesterday_codes, today_date)

        # 4. 数据整合与分析
        print("🔍 开始深度分析...")
        analysis_results = []

        for _, yesterday_row in yesterday_zt_df.iterrows():
            stock_code = yesterday_row['代码']
            stock_name = yesterday_row['名称']
            yesterday_boards = yesterday_row.get('连板数', 1)

            # 获取各类数据
            today_perf = today_performance_df[today_performance_df['代码'] == stock_code].iloc[0] if len(today_performance_df[today_performance_df['代码'] == stock_code]) > 0 else None
            auction_info = auction_df[auction_df['代码'] == stock_code].iloc[0] if len(auction_df[auction_df['代码'] == stock_code]) > 0 else None
            intraday_info = intraday_df[intraday_df['代码'] == stock_code].iloc[0] if len(intraday_df[intraday_df['代码'] == stock_code]) > 0 else None

            # 分析晋级结果
            promotion_result = _analyze_promotion_result(stock_code, yesterday_boards, today_zt_df, today_perf)

            # 分析竞价表现
            auction_performance = _analyze_auction_performance(auction_info)

            # 分析分时强度
            intraday_strength = _analyze_intraday_strength(intraday_info, today_perf)

            # 计算收盘溢价
            closing_premium = _calculate_closing_premium(today_perf)

            # 获取核心题材
            core_concepts = get_stock_concepts(stock_code)[:3]  # 取前3个概念

            # 生成盘口语言解读
            market_interpretation = _generate_market_interpretation_old(
                promotion_result, auction_performance, intraday_strength,
                closing_premium, core_concepts, yesterday_boards
            )

            # 获取昨日封单资金
            yesterday_seal_fund = yesterday_row.get('封单资金', 0)

            # 构建分析结果
            analysis_results.append({
                '代码': stock_code,
                '名称': stock_name,
                '昨日连板': yesterday_boards,
                '昨日封单资金': yesterday_seal_fund,
                '晋级结果': promotion_result,
                '竞价表现': auction_performance,
                '分时强度': intraday_strength,
                '收盘溢价': closing_premium,
                '核心题材': '/'.join(core_concepts) if core_concepts else '无',
                '盘口语言解读': market_interpretation
            })

        # 5. 按梯队分组并排序
        grouped_results = _group_by_echelon(analysis_results)

        # 6. 生成并打印复盘报告（传入今日涨停股池数据，避免重复获取）
        _print_review_report(grouped_results, yesterday_date, today_date, today_zt_df)

        # 7. 计算市场情绪指标
        market_sentiment = _calculate_market_sentiment_metrics(analysis_results)

        return {
            'analysis_results': analysis_results,
            'grouped_results': grouped_results,
            'market_sentiment': market_sentiment,
            'yesterday_date': yesterday_date,
            'today_date': today_date
        }

    except Exception as e:
        logging.error(f"昨日涨停股复盘分析失败: {e}")
        print(f"❌ 分析失败: {e}")
        return {}


def _analyze_promotion_result(stock_code, yesterday_boards, today_zt_df, today_perf):
    """
    【增强版】分析股票的晋级结果
    严格按照 今日连板数 == 昨日连板数 + 1 的标准来判断"成功"
    """
    if today_perf is None:
        return "数据缺失"

    try:
        today_change_pct = float(today_perf.get('涨跌幅', 0))

        # 检查是否在今日涨停池中
        if not today_zt_df.empty and '代码' in today_zt_df.columns:
            today_zt_stock = today_zt_df[today_zt_df['代码'] == stock_code]
        else:
            today_zt_stock = pd.DataFrame()  # 空DataFrame

        if not today_zt_stock.empty:
            today_boards = today_zt_stock.iloc[0].get('连板数', 1)

            # 【修正晋级逻辑】优先根据涨跌幅判断，再看连板数
            # 如果涨跌幅>=9.9%，说明涨停成功
            if today_change_pct >= 9.9:
                if today_boards == yesterday_boards + 1:
                    return f"成功 ({today_boards}板)"
                elif today_boards > yesterday_boards + 1:
                    return f"成功 ({today_boards}板)"
                elif today_boards == yesterday_boards:
                    # 连板数相同但涨停，可能是数据源问题，按成功处理
                    return f"成功 ({today_boards}板)"
                else:
                    # 连板数减少但涨停，可能是断板后重新起板
                    return f"重新起板 ({today_boards}板)"
            else:
                # 涨跌幅<9.9%，但在涨停池中，可能是炸板重封
                if today_boards == yesterday_boards:
                    return f"炸板重封 ({today_boards}板)"
                elif today_boards < yesterday_boards:
                    return f"断板 ({today_boards}板)"
                else:
                    return f"成功 ({today_boards}板)"

        # 【修正】根据涨跌幅判断结果，不在涨停池中的情况
        if today_change_pct >= 9.9:  # 涨停但不在涨停池中（可能是数据源问题）
            return f"成功 ({yesterday_boards + 1}板)"
        elif today_change_pct >= 7:  # 大涨但未涨停
            return "冲高回落"
        elif today_change_pct >= 3:  # 中涨
            return "高开回落"
        elif today_change_pct >= 0:  # 小涨或平盘
            return "震荡整理"
        elif today_change_pct >= -3:  # 小跌
            return "小幅回调"
        elif today_change_pct >= -7:  # 中跌
            return "大幅回调"
        else:  # 大跌
            return "核按钮"

    except Exception as e:
        logging.warning(f"分析股票 {stock_code} 晋级结果失败: {e}")
        return "分析失败"


def _analyze_auction_performance(auction_info):
    """
    分析竞价表现
    """
    if auction_info is None:
        return "无数据"

    try:
        auction_change = auction_info.get('竞价涨幅', 0)
        auction_amount = auction_info.get('竞价金额', 0)

        # 格式化金额
        if auction_amount >= 100000000:  # 1亿
            amount_str = f"{auction_amount/100000000:.1f}亿"
        elif auction_amount >= 10000:  # 1万
            amount_str = f"{auction_amount/10000:.0f}万"
        else:
            amount_str = f"{auction_amount:.0f}"

        return f"{amount_str} / {auction_change:+.1f}%"

    except Exception as e:
        logging.warning(f"分析竞价表现失败: {e}")
        return "分析失败"


def _judge_intraday_strength(intraday_info, today_perf):
    """
    【增强修复版】判断分时强度
    根据开、高、低、收和涨跌幅，返回描述性标签
    - 新增逻辑分支，以处理只有单一价格数据（如adata源）导致无法精确判断分时强度的情形。
    """
    if intraday_info is None or today_perf is None:
        return "无数据"

    try:
        open_price = float(intraday_info.get('开盘价', 0))
        high_price = float(intraday_info.get('最高价', 0))
        low_price = float(intraday_info.get('最低价', 0))
        close_price = float(intraday_info.get('收盘价', 0))
        yesterday_close = float(intraday_info.get('昨收', 0))

        # 备用逻辑：如果分时数据中没有昨收，则从行情快照中获取
        if yesterday_close == 0:
            yesterday_close = float(today_perf.get('昨收', open_price))
        if yesterday_close == 0 and open_price > 0:
            yesterday_close = open_price  # 近似处理

        # 【核心修复】处理只有单一价格点（开=高=低=收）的数据源
        if open_price > 0 and open_price == high_price == low_price == close_price:
            today_change_pct = float(today_perf.get('涨跌幅', 0))
            if today_change_pct >= 9.9:
                return "强势涨停"
            elif today_change_pct >= 7:
                return "大涨"
            elif today_change_pct >= 3:
                return "中涨"
            elif today_change_pct > 0:
                return "上涨"
            elif today_change_pct == 0:
                return "平盘"
            elif today_change_pct >= -3:
                return "小跌"
            elif today_change_pct >= -7:
                return "中跌"
            else:
                return "大跌"

        # 备用逻辑：如果分时数据完全缺失，则仅根据收盘涨跌幅判断
        if open_price == 0 and close_price == 0:
            today_change_pct = float(today_perf.get('涨跌幅', 0))
            if today_change_pct >= 9.9: return "涨停封板"
            elif today_change_pct >= 5: return "大涨"
            elif today_change_pct >= 0: return "上涨"
            elif today_change_pct >= -3: return "小跌"
            elif today_change_pct >= -7: return "中跌"
            else: return "大跌"

        if yesterday_close == 0:
            return "数据缺失"

        # --- 原有精细化分时强度判断逻辑 ---
        open_change = (open_price - yesterday_close) / yesterday_close * 100
        high_change = (high_price - yesterday_close) / yesterday_close * 100
        close_change = (close_price - yesterday_close) / yesterday_close * 100
        amplitude = (high_price - low_price) / yesterday_close * 100

        if abs(open_price - high_price) / yesterday_close < 0.5 and abs(high_price - close_price) / yesterday_close < 0.5 and close_change >= 9.5:
            return "一字封死"
        elif open_change >= 9.5 and high_change >= 9.9 and close_change >= 9.5:
            return "高开秒板"
        elif open_change >= 7 and close_change >= 9.5:
            return "高开封板"
        elif high_change >= 9.9 and close_change < high_change - 2:
            return "烂板回封" if close_change >= 9.5 else "冲高回落"
        elif open_change < -2 and close_change >= 9.5:
            return "低开拉板"
        elif amplitude >= 8 and close_change >= 9.5:
            return "换手封板"
        elif close_change >= 9.5:
            return "强势封板"
        elif close_change >= 5:
            return "冲高回落"
        elif close_change >= 0:
            return "震荡整理"
        elif close_change >= -3:
            return "小幅回调"
        elif today_change_pct >= -7:
            return "大幅回调"
        else:
            return "低开低走"

    except Exception as e:
        logging.warning(f"判断分时强度失败: {e}")
        return "分析失败"


def _analyze_intraday_strength(intraday_info, today_perf):
    """
    【兼容性函数】分析分时强度（调用新的判断函数）
    """
    return _judge_intraday_strength(intraday_info, today_perf)


def _calculate_closing_premium(today_perf):
    """
    【修复版】计算收盘溢价
    如果是非交易日要获取上一个交易日的收盘价去计算，如果当前是交易日以当日收盘价去计算
    """
    if today_perf is None:
        return 0.0

    try:
        # 先尝试直接从today_perf获取涨跌幅
        today_change_pct = float(today_perf.get('涨跌幅', 0))

        # 如果涨跌幅为0，可能是数据问题，尝试手动计算
        if today_change_pct == 0:
            current_price = float(today_perf.get('最新价', 0))
            yesterday_close = float(today_perf.get('昨收', 0))

            if current_price > 0 and yesterday_close > 0:
                today_change_pct = (current_price - yesterday_close) / yesterday_close * 100
            else:
                # 如果还是没有数据，尝试从其他字段获取
                current_price = float(today_perf.get('现价', 0))
                if current_price == 0:
                    current_price = float(today_perf.get('收盘价', 0))

                if current_price > 0 and yesterday_close > 0:
                    today_change_pct = (current_price - yesterday_close) / yesterday_close * 100

        return round(today_change_pct, 2)

    except Exception as e:
        logging.warning(f"计算收盘溢价失败: {e}")
        return 0.0


def _generate_market_interpretation_old(promotion_result, auction_performance, intraday_strength,
                                  closing_premium, core_concepts, yesterday_boards):
    """
    【增强版】生成盘口语言解读
    基于所有量化指标（梯队、晋级结果、封单强度、题材等）生成精炼的"盘口语言"解读
    """
    try:
        # 基于量化指标生成解读
        interpretation_parts = []

        # 1. 基于晋级结果的解读
        if "成功" in promotion_result:
            if yesterday_boards >= 7:
                interpretation_parts.append("市场总龙头")
            elif yesterday_boards >= 5:
                interpretation_parts.append("市场最高标")
            elif yesterday_boards >= 3:
                interpretation_parts.append("板块卡位龙头")
            elif yesterday_boards >= 2:
                interpretation_parts.append("板块二线龙头")
            else:
                interpretation_parts.append("首板晋级成功")

            # 基于分时强度的细化解读
            if "一字封死" in intraday_strength:
                interpretation_parts.append("竞价抢筹，强度最高，情绪风向标")
            elif "高开秒板" in intraday_strength:
                interpretation_parts.append("资金认可度极高")
            elif "高开封板" in intraday_strength:
                interpretation_parts.append("承接良好")
            elif "换手封板" in intraday_strength:
                interpretation_parts.append("换手充分，资金活跃")
            elif closing_premium >= 8:
                interpretation_parts.append("封板坚决")

        elif "炸板重封" in promotion_result:
            interpretation_parts.append("分歧后重新统一")
            if closing_premium >= 9:
                interpretation_parts.append("最终封板成功")

        elif "冲高回落" in promotion_result:
            interpretation_parts.append("有资金畏高兑现")
            if closing_premium > 3:
                interpretation_parts.append("但承接尚可")
            elif closing_premium > 0:
                interpretation_parts.append("承接一般")
            else:
                interpretation_parts.append("承接较弱")

        elif "核按钮" in promotion_result:
            interpretation_parts.append("被市场抛弃，亏钱效应源头")
            if yesterday_boards >= 3:
                interpretation_parts.append("高位龙头杀跌，板块见顶信号")

        elif "大幅回调" in promotion_result:
            interpretation_parts.append("资金大幅撤离")

        elif "淘汰" in promotion_result:
            interpretation_parts.append("板块退潮，资金转移")

        # 2. 基于题材的解读
        if core_concepts:
            main_concept = core_concepts[0] if isinstance(core_concepts, list) else str(core_concepts).split('/')[0]
            if "成功" in promotion_result:
                if yesterday_boards >= 5:
                    interpretation_parts.append(f"确认{main_concept}为市场主线")
                else:
                    interpretation_parts.append(f"强化{main_concept}题材")
            elif "核按钮" in promotion_result:
                interpretation_parts.append(f"{main_concept}题材降温")

        # 3. 基于竞价表现的解读
        if auction_performance and auction_performance != "无数据":
            try:
                # 解析竞价涨幅
                if "/" in auction_performance:
                    auction_change_str = auction_performance.split("/")[1].strip().replace("%", "").replace("+", "")
                    auction_change = float(auction_change_str)
                    if auction_change >= 8:
                        interpretation_parts.append("竞价强势")
                    elif auction_change <= -3:
                        interpretation_parts.append("竞价疲弱")
            except:
                pass

        # 4. 基于梯队地位的解读
        if yesterday_boards >= 5 and "成功" in promotion_result:
            interpretation_parts.append("市场情绪标杆")
        elif yesterday_boards >= 3 and "核按钮" in promotion_result:
            interpretation_parts.append("高位龙头崩塌，警惕情绪转向")

        # 组合解读
        if interpretation_parts:
            return "，".join(interpretation_parts) + "。"
        else:
            return "市场表现平淡。"

    except Exception as e:
        logging.warning(f"生成市场解读失败: {e}")
        return "解读生成失败。"


def _group_by_echelon(analysis_results):
    """
    按梯队分组股票
    """
    grouped = {
        '最高板': [],
        '高板': [],
        '中板': [],
        '低板': [],
        '炸板/淘汰区': []
    }

    for result in analysis_results:
        yesterday_boards = result['昨日连板']
        promotion_result = result['晋级结果']

        if yesterday_boards >= 5:
            grouped['最高板'].append(result)
        elif yesterday_boards >= 3:
            grouped['高板'].append(result)
        elif yesterday_boards >= 2:
            grouped['中板'].append(result)
        elif "成功" in promotion_result:
            grouped['低板'].append(result)
        else:
            grouped['炸板/淘汰区'].append(result)

    # 每个梯队内部按收盘溢价排序
    for echelon in grouped:
        grouped[echelon].sort(key=lambda x: x['收盘溢价'], reverse=True)

    return grouped


def _print_review_report(grouped_results, yesterday_date, today_date, today_zt_df=None):
    """
    【增强版】打印符合"顶级游资复盘模板"格式的Markdown表格
    新增 市场地位 和 封单强度 列

    Args:
        grouped_results: dict, 分组结果
        yesterday_date: str, 昨日日期
        today_date: str, 今日日期
        today_zt_df: DataFrame, 今日涨停股池数据（可选，避免重复获取）
    """
    print(f"\n### 昨日（{yesterday_date[:4]}-{yesterday_date[4:6]}-{yesterday_date[6:8]}）涨停股今日表现复盘 (晋级赛)\n")

    # 【修复】在函数开始时进行一次交易日判断，避免重复调用
    from market_data_provider import is_trading_day_by_date
    is_today_trading_day = is_trading_day_by_date(today_date)

    # 表格头部（新增市场地位和封单强度列）
    headers = ["梯队", "代码", "名称", "昨日连板", "**市场地位**", "**晋级结果**",
               "**竞价表现**", "**分时强度**", "**封单强度**", "**收盘溢价**",
               "**核心题材**", "**盘口语言解读**"]

    table_data = []

    for echelon_name, stocks in grouped_results.items():
        if not stocks:
            continue

        for i, stock in enumerate(stocks):
            # 计算市场地位
            market_position = _calculate_market_position(stock['昨日连板'], stock['晋级结果'])

            # 计算封单强度 - 传递交易日判断结果和涨停股池数据
            seal_strength = _calculate_seal_strength(stock, today_date, is_today_trading_day, today_zt_df)

            row = [
                echelon_name if i == 0 else "",  # 只在第一行显示梯队名
                stock['代码'],
                stock['名称'],
                f"{stock['昨日连板']}板",
                market_position,
                stock['晋级结果'],
                stock['竞价表现'],
                stock['分时强度'],
                seal_strength,
                f"{stock['收盘溢价']:+.1f}%",
                stock['核心题材'],
                stock['盘口语言解读']
            ]
            table_data.append(row)

        # 在每个梯队之间添加分隔行
        if stocks and echelon_name != '炸板/淘汰区':
            table_data.append(["---"] * len(headers))

    # 打印表格
    print(tabulate(table_data, headers=headers, tablefmt="pipe", stralign="left"))
    print()


def _calculate_market_position(yesterday_boards, promotion_result):
    """
    计算市场地位
    """
    if yesterday_boards >= 7:
        return "总龙头"
    elif yesterday_boards >= 5:
        return "最高标"
    elif yesterday_boards >= 3:
        return "高标"
    elif yesterday_boards >= 2:
        return "中标"
    elif "成功" in promotion_result:
        return "低标"
    else:
        return "淘汰"


def _calculate_seal_strength(stock, today_date=None, is_today_trading_day=None, today_zt_df=None):
    """
    【逻辑修复版】计算封单强度
    - 核心逻辑修正：对于昨日涨停今日再次涨停的个股，计算其【今日】的封单金额，并与【今日】所有涨停股进行排名比较，得出强度。
    - 对于未能再次涨停的个股，显示“未封板”或“炸板”状态。
    - 增加了对非交易日的处理，避免因无法获取数据而显示“无数据”。
    """
    stock_code = stock.get('代码')
    promotion_result = stock['晋级结果']

    amount_str = "--"
    strength_desc = "未封板"  # 默认为未封板

    try:
        # 只有成功晋级的股票才计算今日的封单强度
        if "成功" in promotion_result and today_date:
            # 【修复】使用传入的交易日判断结果，避免重复调用
            if is_today_trading_day is None:
                from market_data_provider import is_trading_day_by_date
                is_today_trading_day = is_trading_day_by_date(today_date)

            # 获取今日涨停数据（如果没有传入，则获取）
            if today_zt_df is None:
                # 只有在交易日才获取当日涨停数据
                if is_today_trading_day:
                    today_zt_df = get_zt_pool_with_backup(today_date)
                else:
                    today_zt_df = pd.DataFrame()  # 非交易日返回空DataFrame
            # 如果传入了数据但为空，且今日是交易日，说明可能有问题
            elif today_zt_df.empty and is_today_trading_day:
                today_zt_df = get_zt_pool_with_backup(today_date)

            if today_zt_df.empty:
                strength_desc = "待更新" if not is_today_trading_day else "数据缺失"
            elif '封单资金' in today_zt_df.columns:
                today_stock_info = today_zt_df[today_zt_df['代码'] == stock_code]

                if not today_stock_info.empty:
                    today_seal_fund = today_stock_info.iloc[0].get('封单资金', 0)

                    # 格式化【今日】封单金额用于显示
                    if today_seal_fund >= 100000000:
                        amount_str = f"{today_seal_fund / 100000000:.1f}亿"
                    elif today_seal_fund >= 10000:
                        amount_str = f"{today_seal_fund / 10000:.0f}万"
                    else:
                        amount_str = "少量"

                    # 基于【今日】封单数据进行排名
                    if today_seal_fund > 0:
                        valid_seal_funds = today_zt_df[today_zt_df['封单资金'] > 0]['封单资金'].sort_values(
                            ascending=False).reset_index(drop=True)
                        if not valid_seal_funds.empty:
                            rank = (valid_seal_funds >= today_seal_fund).sum()
                            total_count = len(valid_seal_funds)
                            rank_percentage = rank / total_count

                            if rank_percentage <= 0.1:
                                strength_desc = "极强"
                            elif rank_percentage <= 0.3:
                                strength_desc = "很强"
                            elif rank_percentage <= 0.6:
                                strength_desc = "强"
                            else:
                                strength_desc = "中等"
                        else:
                            strength_desc = "一般"
                    else:
                        strength_desc = "无封单"
                else:
                    # 在“成功”结果下却找不到该股，说明数据源可能不一致
                    strength_desc = "数据异常"
            else:
                strength_desc = "数据缺失"  # 涨停池数据缺少封单资金字段

        elif "炸板" in promotion_result:
            strength_desc = "炸板"

        return f"{amount_str} / {strength_desc}"

    except Exception as e:
        logging.warning(f"计算封单强度失败 for {stock_code}: {e}")
        return f"-- / 计算失败"


def _calculate_sector_promotion_score(sector_name, sector_type):
    """
    【优化版】计算板块晋级成功率评分
    通过分析该板块昨日涨停股今日的晋级表现来评分
    【核心优化】使用全局缓存避免重复计算昨日涨停股复盘数据

    Args:
        sector_name: 板块名称
        sector_type: 板块类型（"行业" 或 "概念"）

    Returns:
        float: 晋级成功率评分 (0-10)
    """
    try:
        from datetime import datetime, timedelta
        global YESTERDAY_REVIEW_CACHE

        # 获取昨日和今日日期
        today = datetime.now()
        yesterday = today - timedelta(days=1)

        # 如果是周末，往前推到周五
        while yesterday.weekday() >= 5:  # 5=Saturday, 6=Sunday
            yesterday = yesterday - timedelta(days=1)

        yesterday_str = yesterday.strftime('%Y%m%d')
        today_str = today.strftime('%Y%m%d')

        # 【核心优化】检查缓存，避免重复计算
        current_date_key = f"{yesterday_str}_{today_str}"
        if (YESTERDAY_REVIEW_CACHE['date'] == current_date_key and
            YESTERDAY_REVIEW_CACHE['data'] is not None):
            # 使用缓存数据（减少日志输出，避免重复打印）
            review_result = YESTERDAY_REVIEW_CACHE['data']
            # 只在首次使用缓存时打印一次
            if not YESTERDAY_REVIEW_CACHE.get('cache_logged', False):
                print(f"    📋 使用缓存的昨日涨停股复盘数据 (节省计算时间)")
                YESTERDAY_REVIEW_CACHE['cache_logged'] = True
        else:
            # 首次计算，获取昨日涨停股复盘数据并缓存
            print(f"    🔄 首次计算昨日涨停股复盘数据，将缓存结果...")
            review_result = analyze_yesterday_limit_up_performance(yesterday_str, today_str)

            # 更新缓存
            YESTERDAY_REVIEW_CACHE['data'] = review_result
            YESTERDAY_REVIEW_CACHE['date'] = current_date_key
            YESTERDAY_REVIEW_CACHE['last_update_time'] = datetime.now()
            # 重置日志标记，下次使用缓存时会打印一次
            YESTERDAY_REVIEW_CACHE['cache_logged'] = False

        if not review_result or 'analysis_results' not in review_result:
            return 5.0  # 默认中等分数

        analysis_results = review_result['analysis_results']

        # 筛选出属于该板块的股票
        sector_stocks = []
        for result in analysis_results:
            stock_code = result['代码']
            core_concepts = result['核心题材'].split('/') if result['核心题材'] != '无' else []

            # 获取股票的行业信息
            if sector_type == "行业":
                from market_data_provider import get_stock_industry
                stock_industry = get_stock_industry(stock_code)
                if stock_industry == sector_name:
                    sector_stocks.append(result)
            else:  # 概念
                if sector_name in core_concepts:
                    sector_stocks.append(result)

        if not sector_stocks:
            return 5.0  # 没有相关股票，返回中等分数

        # 计算晋级成功率
        total_count = len(sector_stocks)
        success_count = len([s for s in sector_stocks if "成功" in s['晋级结果']])
        success_rate = success_count / total_count if total_count > 0 else 0

        # 转换为0-10分
        promotion_score = success_rate * 10

        return min(10, max(0, promotion_score))

    except Exception as e:
        logging.warning(f"计算板块 {sector_name} 晋级成功率失败: {e}")
        return 5.0  # 出错时返回中等分数


def _calculate_market_sentiment_metrics(analysis_results):
    """
    计算市场情绪指标
    """
    if not analysis_results:
        return {}

    total_count = len(analysis_results)
    success_count = len([r for r in analysis_results if "成功" in r['晋级结果']])
    nuclear_count = len([r for r in analysis_results if "核按钮" in r['晋级结果']])

    # 计算各种比率
    success_rate = success_count / total_count * 100 if total_count > 0 else 0
    nuclear_rate = nuclear_count / total_count * 100 if total_count > 0 else 0

    # 计算平均溢价
    avg_premium = sum([r['收盘溢价'] for r in analysis_results]) / total_count if total_count > 0 else 0

    # 计算溢价分化度（标准差）
    premiums = [r['收盘溢价'] for r in analysis_results]
    premium_std = np.std(premiums) if len(premiums) > 1 else 0

    return {
        'total_count': total_count,
        'success_count': success_count,
        'success_rate': round(success_rate, 1),
        'nuclear_count': nuclear_count,
        'nuclear_rate': round(nuclear_rate, 1),
        'avg_premium': round(avg_premium, 2),
        'premium_std': round(premium_std, 2)
    }


def analyze_reseal_opportunities():
    """
    【V8.0 核心新增】烂板回封狙击模块 (分歧转一致)
    实时扫描炸板股，量化回封强度，捕捉先手买点
    """
    try:
        print("\n" + "🎯"*15 + " 分歧转一致监控 (烂板回封) " + "🎯"*15)

        # 1. 获取实时炸板股池 (我们的猎物名单) - 使用智能日期选择
        fried_board_df = get_fried_board_pool_with_backup()
        if fried_board_df.empty:
            print("  暂无炸板股票，市场分歧较小。")
            return None

        # 2. 获取这些股票的实时行情
        fried_codes = fried_board_df['代码'].tolist()
        quotes_df = get_current_stock_quotes(fried_codes)
        if quotes_df.empty:
            print("  ❌ 无法获取炸板股的实时行情。")
            return None

        # 3. 获取大盘和指数环境
        index_health = get_core_indices_spot()
        # 【修复】确保指数数据是数值类型
        sh_change = convert_to_float(index_health.get('sh', 0))
        cyb_change = convert_to_float(index_health.get('cyb', 0))
        market_is_weak = sh_change < -0.5 or cyb_change < -0.8

        reseal_candidates = []
        # 4. 遍历猎物名单，寻找回封信号并评分
        for _, row in quotes_df.iterrows():
            stock_code = row['代码']
            # 【修复】确保数值类型转换，避免字符串与浮点数比较错误
            latest_price = convert_to_float(row.get('最新价', 0))
            yesterday_close = convert_to_float(row.get('昨收', 0))

            # 计算涨停价
            if yesterday_close > 0:
                limit_up_price = round(yesterday_close * (1.1 if not str(row.get('名称', '')).startswith('ST') else 1.05), 2)
            else:
                continue  # 跳过无效数据

            # 判断是否回封
            if latest_price >= limit_up_price:
                score = 0
                reasons = []

                # a. 基础分: 成功回封
                score += 5
                reasons.append("成功回封")

                # b. 市场环境分: 弱市回封是真强
                if market_is_weak:
                    score += 5
                    reasons.append("弱市回封")

                # c. 板块地位分: 龙头回封是信仰
                global MARKET_LEADER_INFO
                if stock_code == MARKET_LEADER_INFO.get('code'):
                    score += 8
                    reasons.append("市场总龙头")

                # d. 炸板次数: 首次炸板后的回封更坚决 (数据来自炸板池)
                fried_info = fried_board_df[fried_board_df['代码'] == stock_code]
                # 【修复】确保炸板次数是数值类型
                breakout_times = convert_to_float(fried_info.iloc[0].get('炸板次数', 1)) if not fried_info.empty else 1
                breakout_times = int(breakout_times) if breakout_times > 0 else 1
                if breakout_times == 1:
                    score += 3
                    reasons.append("首次开板")
                else:
                    reasons.append(f"{breakout_times}次开板")

                # e. 封单强度 (需要实时获取)
                # 【修复】确保数值类型转换
                seal_fund = convert_to_float(row.get('封单资金', 0))
                circulating_cap = convert_to_float(row.get('流通市值', 1))
                if circulating_cap > 0 and seal_fund > 0 and seal_fund / circulating_cap > 0.05:
                    score += 4
                    reasons.append("封单强劲")

                reseal_candidates.append({
                    '代码': stock_code,
                    '名称': row['名称'],
                    '回封强度分': score,
                    '状态': '🔥强势回封',
                    '原因': '+'.join(reasons)
                })

        if not reseal_candidates:
            print("  池中有炸板股，但暂未发现回封信号。")
            return None

        # 按强度分排序
        reseal_df = pd.DataFrame(reseal_candidates).sort_values(by='回封强度分', ascending=False)
        return reseal_df

    except Exception as e:
        logging.error(f"分析烂板回封机会失败: {e}")
        print(f"❌ 分析烂板回封机会失败: {e}")
        return None
