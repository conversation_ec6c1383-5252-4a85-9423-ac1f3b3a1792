"""
主控制器模块 - V7.0 四大心法完整版
负责程序调度、任务管理和主程序入口
严格遵循四大心法：周期为王、龙头不死、先手预判、数据为基

四大心法详解：
第一心法：周期为王，情绪为相 (总开关)
- 通过量化昨日涨停股今日表现（晋级率、核按钮率、平均溢价）评估市场情绪
- 输出明确的市场状态和建议仓位，作为所有买入决策的总开关

第二心法：龙头不死，主线不灭 (主战场)
- 通过分析当日涨停梯队和板块聚集度识别市场总龙头和主线战场
- 只有属于主线战场的股票才有资格进入评分环节

第三心法：先手预判，买在分歧 (信号生成)
- 建立统一的龙头战法评分体系，对主线战场内的股票进行量化打分
- 根据市场情绪动态调整买入信号的触发阈值

第四心法：数据为基，应对为上 (系统健壮性)
- 建立三级容灾机制：akshare -> adata -> TPDOG
- 对不同数据源的原始数据进行标准化处理
"""

import schedule
import time as time_module
import logging
import os
import pandas as pd
from datetime import datetime
from collections import defaultdict
from tabulate import tabulate

# 导入分离的模块
from market_data_provider import (
    test_network_connection,
    load_stock_concept_cache,
    update_stock_concept_cache,
    is_trading_time,
    is_trading_day,
    get_last_trading_day,
    AKSHARE_AVAILABLE,
    get_stock_concepts,
    get_zt_pool_with_backup,
    get_current_stock_quotes,
    ak
)
from theme_analyzer import (
    analyze_yesterday_limit_up_performance,
    analyze_intraday_flow_shift,
    get_stock_concepts,
    analyze_reseal_opportunities,
    analyze_concept_main_themes,
    analyze_industry_main_themes
)
from signal_generator import (
    write_signal_file_atomically,
    task_scan_market_for_signals
)

# 导入必要的库
import requests
import json
import pickle

# --- 全局配置 ---
DEBUG_MODE = True
# DEBUG_MODE = False
#开启交易时间限制
ENABLE_TRADING_TIME_CHECK = False
NOTIFICATION_FILE = 'D:/flow_buy.ebk'
SELL_NOTIFICATION_FILE = 'D:/flow_sell.ebk'

# --- 运行模式配置 ---
RUN_MODE = 'REALTIME'  # 'REALTIME' 或 'BACKTEST'
BACKTEST_DATE = '20250718'

# --- 全局变量 ---
TODAY_NOTIFICATION_CACHE = {}
is_market_scan_running = False

# --- 【新增】全局交易时间缓存，避免重复判断 ---
GLOBAL_TRADING_STATUS = {
    'is_trading_day': None,
    'is_trading_time': None,
    'last_check_time': None,
    'cache_duration': 60  # 缓存60秒
}

# === 【新增】全局交易状态检查函数 ===
def get_global_trading_status():
    """
    获取全局交易状态，带缓存机制避免重复判断
    返回: (is_trading_day, is_trading_time)
    """
    global GLOBAL_TRADING_STATUS

    current_time = datetime.now()

    # 检查缓存是否有效
    if (GLOBAL_TRADING_STATUS['last_check_time'] is not None and
        (current_time - GLOBAL_TRADING_STATUS['last_check_time']).total_seconds() < GLOBAL_TRADING_STATUS['cache_duration']):
        # 使用缓存
        return GLOBAL_TRADING_STATUS['is_trading_day'], GLOBAL_TRADING_STATUS['is_trading_time']

    # 缓存过期或首次调用，重新判断
    try:
        trading_day = is_trading_day()
        trading_time = is_trading_time() if trading_day else False

        # 更新缓存
        GLOBAL_TRADING_STATUS.update({
            'is_trading_day': trading_day,
            'is_trading_time': trading_time,
            'last_check_time': current_time
        })

        return trading_day, trading_time

    except Exception as e:
        logging.error(f"获取交易状态失败: {e}")
        # 异常时返回保守值
        return False, False

# === 数据保存相关函数 ===
def get_date_folder():
    """获取按日期命名的文件夹路径（如 data/20250721）"""
    date_str = datetime.now().strftime('%Y%m%d')
    folder = os.path.join('data', date_str)
    os.makedirs(folder, exist_ok=True)
    return folder

def save_raw_data(data, filename_prefix, data_type="csv"):
    """
    保存原始数据到按日期命名的文件夹

    Args:
        data: 要保存的数据（DataFrame或dict）
        filename_prefix: 文件名前缀
        data_type: 数据类型 ("csv", "json", "pkl")
    """
    try:
        date_folder = get_date_folder()
        timestamp = datetime.now().strftime('%H%M%S')

        if data_type == "csv" and isinstance(data, pd.DataFrame):
            filename = f"{filename_prefix}_{timestamp}.csv"
            filepath = os.path.join(date_folder, filename)
            data.to_csv(filepath, index=False, encoding='utf-8-sig')
            print(f"✅ 保存原始数据: {filepath}")
            logging.info(f"保存原始数据: {filepath}")

        elif data_type == "json":
            filename = f"{filename_prefix}_{timestamp}.json"
            filepath = os.path.join(date_folder, filename)
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)
            print(f"✅ 保存原始数据: {filepath}")
            logging.info(f"保存原始数据: {filepath}")

        elif data_type == "pkl":
            filename = f"{filename_prefix}_{timestamp}.pkl"
            filepath = os.path.join(date_folder, filename)
            with open(filepath, 'wb') as f:
                pickle.dump(data, f)
            print(f"✅ 保存原始数据: {filepath}")
            logging.info(f"保存原始数据: {filepath}")

    except Exception as e:
        logging.error(f"保存原始数据失败 [{filename_prefix}]: {e}")
        print(f"❌ 保存原始数据失败 [{filename_prefix}]: {e}")

def save_analysis_report(report_data, report_name):
    """
    保存分析报告到按日期命名的文件夹

    Args:
        report_data: 报告数据（dict或str）
        report_name: 报告名称
    """
    try:
        date_folder = get_date_folder()
        timestamp = datetime.now().strftime('%H%M%S')

        # 保存为JSON格式
        filename = f"{report_name}_{timestamp}.json"
        filepath = os.path.join(date_folder, filename)

        if isinstance(report_data, str):
            # 如果是字符串，包装成dict
            report_data = {
                'report_content': report_data,
                'timestamp': datetime.now().isoformat(),
                'report_name': report_name
            }
        elif isinstance(report_data, dict):
            # 添加时间戳
            report_data['timestamp'] = datetime.now().isoformat()
            report_data['report_name'] = report_name

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2, default=str)

        print(f"✅ 保存分析报告: {filepath}")
        logging.info(f"保存分析报告: {filepath}")

        # 同时保存为文本格式便于查看
        txt_filename = f"{report_name}_{timestamp}.txt"
        txt_filepath = os.path.join(date_folder, txt_filename)

        with open(txt_filepath, 'w', encoding='utf-8') as f:
            if isinstance(report_data.get('report_content'), str):
                f.write(report_data['report_content'])
            else:
                f.write(json.dumps(report_data, ensure_ascii=False, indent=2, default=str))

        print(f"✅ 保存文本报告: {txt_filepath}")
        logging.info(f"保存文本报告: {txt_filepath}")

    except Exception as e:
        logging.error(f"保存分析报告失败 [{report_name}]: {e}")
        print(f"❌ 保存分析报告失败 [{report_name}]: {e}")

# === 四大心法核心数据结构 ===
# 第一心法：市场情绪总开关数据
MARKET_SENTIMENT_DATA = {
    'status': '混沌期',  # 强势期、分化期、混沌期、冰点期
    'position_ratio': 0.5,  # 建议仓位 0.0-1.0
    'score_threshold': 12,  # 动态阈值
    'last_update_time': None
}

# 第二心法：主线战场数据
MAINLINE_BATTLEFIELD_DATA = {
    'market_leader': {'code': None, 'name': None, 'boards': 0},
    'strong_sectors': [],  # 主线战场板块列表
    'limit_up_ladder': {},  # 涨停梯队分布
    'last_update_time': None
}

# 第三心法：龙头战法评分缓存
LEADER_STRATEGY_CACHE = {}

# 第四心法：数据源状态监控
DATA_SOURCE_STATUS = {
    'akshare': True,
    'adata': False,
    'tpdog': False,
    'last_check_time': None
}

def _adjust_sentiment_with_flow_shift(sentiment_data, rising_sectors):
    """
    【V8.0 新增】基于资金流突变动态修正市场情绪
    当资金攻击榜出现高分板块时，认为市场找到新突破口，情绪转暖
    """
    if rising_sectors is None or rising_sectors.empty:
        return sentiment_data

    # 检查是否有突变分超过0.8且排名变化巨大的新板块
    top_rising = rising_sectors.head(1)
    if not top_rising.empty:
        top_score = top_rising.iloc[0]['shift_score']
        rank_change = top_rising.iloc[0]['rank_change']

        if top_score > 0.8 and rank_change > 10:
            # 动态提升仓位建议
            original_ratio = sentiment_data['position_ratio']
            adjusted_ratio = min(1.0, original_ratio + 0.2)  # 大胆提升20%仓位

            print(f"🔥 检测到强势资金突变，动态调整仓位: {original_ratio:.1%} -> {adjusted_ratio:.1%}")

            # 更新全局数据
            MARKET_SENTIMENT_DATA['position_ratio'] = adjusted_ratio
            sentiment_data['position_ratio'] = adjusted_ratio

    return sentiment_data

# --- 配置日志 ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('stock_monitor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# ===== 四大心法核心函数实现 =====

def assess_market_sentiment_v7(yesterday_zt_pool_df=None, today_zt_pool_df=None, yesterday_str=None, today_str=None):
    """
    第一心法：周期为王，情绪为相 (总开关)
    通过量化昨日涨停股今日表现，综合评估市场情绪状态

    Args:
        yesterday_zt_pool_df: DataFrame, 昨日涨停股池数据（可选，避免重复获取）
        today_zt_pool_df: DataFrame, 今日涨停股池数据（可选，避免重复获取）
        yesterday_str: str, 昨日日期字符串（可选）
        today_str: str, 今日日期字符串（可选）

    Returns:
        dict: {
            'status': str,  # 强势期、分化期、混沌期、冰点期
            'position_ratio': float,  # 建议仓位 0.0-1.0
            'score_threshold': int,  # 买入信号动态阈值
            'metrics': dict  # 详细指标
        }
    """
    global MARKET_SENTIMENT_DATA

    try:
        print("\n🎯 第一心法：市场情绪总开关评估...")

        # 获取昨日涨停股今日表现数据
        from datetime import datetime, timedelta
        from market_data_provider import get_last_trading_day

        # 如果没有传入日期参数，则计算
        if yesterday_str is None or today_str is None:
            current_date = datetime.now()
            if is_trading_day():
                today_str = current_date.strftime("%Y%m%d")
                yesterday_str = get_last_trading_day()
            else:
                # 非交易日使用最近的两个交易日
                today_str = get_last_trading_day()
                yesterday_str = get_last_trading_day(today_str)

        # 调用昨日涨停股复盘分析（传入已获取的数据，避免重复调用）
        review_result = analyze_yesterday_limit_up_performance(yesterday_str, today_str, yesterday_zt_pool_df, today_zt_pool_df)

        if not review_result or 'market_sentiment' not in review_result:
            print("❌ 无法获取昨日涨停股表现数据，使用默认情绪评估")
            return _get_default_sentiment()

        metrics = review_result['market_sentiment']
        success_rate = metrics.get('success_rate', 0)
        nuclear_rate = metrics.get('nuclear_rate', 0)
        avg_premium = metrics.get('avg_premium', 0)
        premium_std = metrics.get('premium_std', 0)

        # 情绪评估逻辑
        status, position_ratio, score_threshold = _calculate_market_status(
            success_rate, nuclear_rate, avg_premium, premium_std
        )

        # 更新全局数据
        MARKET_SENTIMENT_DATA.update({
            'status': status,
            'position_ratio': position_ratio,
            'score_threshold': score_threshold,
            'last_update_time': datetime.now()
        })

        print(f"📊 市场情绪评估结果:")
        print(f"   状态: {status}")
        print(f"   建议仓位: {position_ratio:.1%}")
        print(f"   买入阈值: {score_threshold}分")
        print(f"   晋级率: {success_rate:.1f}% | 核按钮率: {nuclear_rate:.1f}%")

        # 【新增】保存市场情绪数据
        sentiment_data = {
            'status': status,
            'position_ratio': position_ratio,
            'score_threshold': score_threshold,
            'metrics': metrics,
            'review_result': review_result
        }
        save_analysis_report(sentiment_data, "market_sentiment_analysis")

        return sentiment_data

    except Exception as e:
        logging.error(f"市场情绪评估失败: {e}")
        print(f"❌ 市场情绪评估失败: {e}")
        return _get_default_sentiment()

def _calculate_market_status(success_rate, nuclear_rate, avg_premium, premium_std):
    """
    【V8.0 游资心法版】根据关键指标计算市场状态
    - 结合晋级率、核按钮率、平均溢价、市场总龙头表现进行综合评判
    """
    # 1. 核心指标量化
    # 晋级率评分 (权重最高)
    promotion_score = (success_rate / 100) * 10

    # 亏钱效应评分 (一票否决项)
    fear_score = (1 - min(1, nuclear_rate / 20)) * 10  # 核按钮率超过20%即为0分

    # 赚钱效应评分
    premium_score = 0
    if avg_premium > 3: premium_score = 10
    elif avg_premium > 1: premium_score = 8
    elif avg_premium > 0: premium_score = 6
    elif avg_premium > -2: premium_score = 4
    else: premium_score = 2

    # 2. 综合情绪分计算
    # 权重: 晋级强度 > 亏钱效应 > 赚钱效应
    sentiment_score = promotion_score * 0.5 + fear_score * 0.3 + premium_score * 0.2

    # 3. 龙头表现修正 (一票否决/一票加成)
    leader_boards = MAINLINE_BATTLEFIELD_DATA.get('market_leader', {}).get('boards', 0)
    if leader_boards >= 7:
        sentiment_score = max(sentiment_score, 8.0) # 市场有绝对龙头，情绪不会差
        print("🔥 龙头修正：市场高度打开，情绪分强制提升！")
    elif nuclear_rate > 15:
        sentiment_score = min(sentiment_score, 3.0) # 核按钮率过高，强制降低情绪分
        print("🧊 龙头修正：亏钱效应放大，情绪分强制降低！")

    # 4. 根据情绪分输出状态和策略
    if sentiment_score >= 8.0:
        return "强势期", 1.0, 8  # 仓位100%，阈值8分
    elif sentiment_score >= 6.0:
        return "分化期", 0.7, 10 # 仓位70%，阈值10分
    elif sentiment_score >= 4.0:
        return "混沌期", 0.4, 12 # 仓位40%，阈值12分
    else:
        return "冰点期", 0.1, 999 # 仓位10%，只看不做

def _get_default_sentiment():
    """
    获取默认情绪评估结果
    """
    return {
        'status': '混沌期',
        'position_ratio': 0.4,
        'score_threshold': 12,
        'metrics': {}
    }

def _adjust_sentiment_with_flow_shift(sentiment_data, rising_sectors):
    """
    【V8.0 新增】基于资金流突变动态修正市场情绪
    当资金攻击榜出现高分板块时，认为市场找到新突破口，情绪转暖
    """
    if rising_sectors is None or rising_sectors.empty:
        return sentiment_data

    # 检查是否有突变分超过0.8且排名变化巨大的新板块
    top_rising = rising_sectors.head(1)
    if not top_rising.empty:
        top_score = top_rising.iloc[0]['shift_score']
        rank_change = top_rising.iloc[0]['rank_change']

        if top_score > 0.8 and rank_change > 10:
            # 动态提升仓位建议
            original_ratio = sentiment_data['position_ratio']
            adjusted_ratio = min(1.0, original_ratio + 0.1)  # 最多提升到100%

            print(f"🔥 检测到强势资金突变，动态调整仓位: {original_ratio:.1%} -> {adjusted_ratio:.1%}")

            # 更新全局数据
            MARKET_SENTIMENT_DATA['position_ratio'] = adjusted_ratio
            sentiment_data['position_ratio'] = adjusted_ratio

    return sentiment_data

def analyze_today_mainline_and_limit_ups_v7(rising_sectors=None, today_zt_pool_df=None):
    """
    第二心法：龙头不死，主线不灭 (主战场)
    通过分析当日涨停梯队和板块聚集度，识别市场总龙头和主线战场

    Args:
        rising_sectors: DataFrame, 资金攻击榜数据 (V8.0新增)
        today_zt_pool_df: DataFrame, 今日涨停股池数据（可选，避免重复获取）

    Returns:
        dict: {
            'market_leader': dict,  # 市场总龙头信息
            'strong_sectors': list,  # 主线战场板块列表
            'limit_up_ladder': dict  # 涨停梯队分布
        }
    """
    global MAINLINE_BATTLEFIELD_DATA

    try:
        print("\n👑 第二心法：主线战场识别...")

        # 1. 获取今日涨停池数据（如果没有传入，则获取）
        if today_zt_pool_df is None:
            from market_data_provider import get_zt_pool_with_backup
            from datetime import datetime as dt

            # 获取当前交易日期
            if is_trading_day():
                today_str = dt.now().strftime("%Y%m%d")
            else:
                today_str = get_last_trading_day()

            zt_pool_df = get_zt_pool_with_backup(today_str)
        else:
            zt_pool_df = today_zt_pool_df

        if zt_pool_df is None or zt_pool_df.empty:
            print("❌ 无法获取涨停池数据")
            return _get_default_mainline_data()

        # 2. 分析涨停梯队分布
        limit_up_ladder = _analyze_limit_up_ladder(zt_pool_df)

        # 3. 识别市场总龙头（最高板）
        market_leader = _identify_market_leader(zt_pool_df)

        # 4. 分析板块聚集度，识别主线战场
        strong_sectors = _identify_strong_sectors(zt_pool_df, rising_sectors)

        # 5. 更新全局数据
        MAINLINE_BATTLEFIELD_DATA.update({
            'market_leader': market_leader,
            'strong_sectors': strong_sectors,
            'limit_up_ladder': limit_up_ladder,
            'last_update_time': datetime.now()
        })

        # 6. 输出结果
        print(f"📊 主线战场识别结果:")
        if market_leader['code']:
            print(f"   市场总龙头: {market_leader['name']}({market_leader['code']}) - {market_leader['boards']}板")
        else:
            print(f"   市场总龙头: 无明确龙头")

        print(f"   主线战场板块({len(strong_sectors)}个):")
        for i, sector in enumerate(strong_sectors[:5]):  # 显示前5个
            print(f"     {i+1}. {sector['name']} - {sector['count']}只涨停")

        print(f"   涨停梯队分布: ", end="")
        for boards, count in sorted(limit_up_ladder.items(), reverse=True):
            if count > 0:
                print(f"{boards}板({count}只) ", end="")
        print()

        # 【新增】保存主线战场数据
        mainline_data = {
            'market_leader': market_leader,
            'strong_sectors': strong_sectors,
            'limit_up_ladder': limit_up_ladder
        }
        save_analysis_report(mainline_data, "mainline_battlefield_analysis")

        # 【新增】保存涨停股池原始数据
        if zt_pool_df is not None and not zt_pool_df.empty:
            save_raw_data(zt_pool_df, "limit_up_pool", "csv")

        return mainline_data

    except Exception as e:
        logging.error(f"主线战场识别失败: {e}")
        print(f"❌ 主线战场识别失败: {e}")
        return _get_default_mainline_data()

def _analyze_limit_up_ladder(zt_pool_df):
    """
    分析涨停梯队分布
    """
    ladder = defaultdict(int)

    for _, row in zt_pool_df.iterrows():
        # 使用标准化后的中文字段名
        boards = row.get('连板数', 1)
        if pd.isna(boards):
            boards = 1
        ladder[int(boards)] += 1

    return dict(ladder)

def _identify_market_leader(zt_pool_df):
    """
    识别市场总龙头（最高板）
    """
    if zt_pool_df.empty:
        return {'code': None, 'name': None, 'boards': 0}

    # 找到连板数最高的股票
    max_boards = 0
    leader_info = {'code': None, 'name': None, 'boards': 0}

    for _, row in zt_pool_df.iterrows():
        # 使用标准化后的中文字段名
        boards = row.get('连板数', 1)
        if pd.isna(boards):
            boards = 1
        boards = int(boards)

        if boards > max_boards:
            max_boards = boards
            leader_info = {
                'code': str(row['代码']).zfill(6),
                'name': row['名称'],
                'boards': boards
            }

    return leader_info

def _identify_strong_sectors(zt_pool_df, rising_sectors=None):
    """
    识别主线战场板块（涨停家数最多的板块）

    Args:
        zt_pool_df: DataFrame, 涨停股池数据
        rising_sectors: DataFrame, 资金攻击榜数据（可选）
    """
    # 统计各概念/行业的涨停家数
    concept_counts = defaultdict(int)
    industry_counts = defaultdict(int)

    for _, row in zt_pool_df.iterrows():
        # 使用标准化后的中文字段名
        stock_code = str(row['代码']).zfill(6)

        # 统计概念
        try:
            concepts = get_stock_concepts(stock_code)
            for concept in concepts:
                concept_counts[concept] += 1
        except:
            pass

        # 统计行业
        try:
            industry = row.get('所属行业', '')
            if industry:
                industry_counts[industry] += 1
        except:
            pass

    # 合并概念和行业，选出涨停家数最多的板块
    strong_sectors = []

    # 添加概念板块
    for concept, count in concept_counts.items():
        if count >= 2:  # 至少2只涨停才算强势板块
            strong_sectors.append({
                'name': concept,
                'type': '概念',
                'count': count
            })

    # 添加行业板块
    for industry, count in industry_counts.items():
        if count >= 2:
            strong_sectors.append({
                'name': industry,
                'type': '行业',
                'count': count
            })

    # 【V8.0 新增】动态加入资金攻击榜前三名板块
    if rising_sectors is not None and not rising_sectors.empty:
        rising_top3 = rising_sectors.head(3)
        for _, row in rising_top3.iterrows():
            sector_name = row['名称']
            # 检查是否已经在强势板块列表中
            if not any(s['name'] == sector_name for s in strong_sectors):
                strong_sectors.append({
                    'name': sector_name,
                    'type': '概念',
                    'count': 1,  # 临时加入，设置为1
                    'source': '资金攻击榜'  # 标记来源
                })
                print(f"🔥 动态加入资金攻击榜板块: {sector_name}")

    # 按涨停家数排序
    strong_sectors.sort(key=lambda x: x['count'], reverse=True)

    return strong_sectors[:10]  # 返回前10个强势板块

def _get_default_mainline_data():
    """
    获取默认主线数据
    """
    return {
        'market_leader': {'code': None, 'name': None, 'boards': 0},
        'strong_sectors': [],
        'limit_up_ladder': {}
    }

def _calculate_stock_score_v7(stock_info, mainline_data, sentiment_data, rising_sectors=None):
    """
    【升级版】第三心法：先手预判，买在分歧 (信号生成)
    统一的龙头战法评分函数，对主线战场内的股票进行量化打分
    新增：先锋卡位分、封单强度分，精准锁定市场辨识度最高的真龙

    Args:
        stock_info: dict, 股票基本信息
        mainline_data: dict, 主线战场数据
        sentiment_data: dict, 市场情绪数据
        rising_sectors: DataFrame, 资金攻击榜数据 (V8.0新增)

    Returns:
        dict: {
            'score': int,  # 总评分
            'reasons': list,  # 得分原因
            'is_qualified': bool  # 是否达到买入阈值
        }
    """
    try:
        score = 0
        reasons = []

        stock_code = stock_info.get('code', '')
        stock_name = stock_info.get('name', '')
        boards = stock_info.get('boards', 1)
        first_seal_time = stock_info.get('first_seal_time', '15:00:00')
        seal_fund = stock_info.get('seal_fund', 0)
        circulating_market_cap = stock_info.get('circulating_market_cap', 0)

        # === 硬性约束：只有主线战场内的股票才能评分 ===
        stock_concepts = get_stock_concepts(stock_code)
        stock_industry = stock_info.get('industry', '')

        strong_sector_names = [s['name'] for s in mainline_data['strong_sectors']]
        is_in_mainline = any(concept in strong_sector_names for concept in stock_concepts) or \
                        stock_industry in strong_sector_names

        if not is_in_mainline:
            return {'score': 0, 'reasons': ['不在主线战场'], 'is_qualified': False}

        # === 龙头地位评分 ===
        market_leader = mainline_data['market_leader']

        # 市场总龙头（最高板）：+10分
        if stock_code == market_leader.get('code'):
            score += 10
            reasons.append("市场总龙头")

        # 板块龙头判断：在所属板块内是否为最高板
        elif _is_sector_leader(stock_code, stock_concepts, boards):
            score += 7
            reasons.append("板块龙头")

        # 2板晋3板（市场核心辨识度）：+6分
        if boards == 3:
            score += 6
            reasons.append("3板核心")
        elif boards == 2:
            score += 4
            reasons.append("2板进阶")

        # === 【新增】先锋卡位评分 (体现速度) ===
        # 10:00前封板的都是强势板，给予加分
        if first_seal_time and first_seal_time < '10:00:00':
            score += 4
            reasons.append(f"早盘卡位({first_seal_time})")
        elif first_seal_time and first_seal_time < '11:00:00':
            score += 2
            reasons.append(f"盘中卡位({first_seal_time})")

        # === 【新增】封单强度评分 (体现共识) ===
        if circulating_market_cap > 0 and seal_fund > 0:
            seal_ratio = seal_fund / circulating_market_cap
            if seal_ratio > 0.1:  # 封单占流通市值超过10%，极强
                score += 5
                reasons.append(f"封单极强({seal_ratio:.1%})")
            elif seal_ratio > 0.05:  # 封单占流通市值超过5%，很强
                score += 3
                reasons.append(f"封单很强({seal_ratio:.1%})")

        # === 主线强度评分 ===
        # 属于当日资金最认可、涨停家数最多的主战场板块：+5分
        if mainline_data['strong_sectors']:
            top_sector = mainline_data['strong_sectors'][0]
            if any(concept == top_sector['name'] for concept in stock_concepts) or \
               stock_industry == top_sector['name']:
                score += 5
                reasons.append(f"最强板块({top_sector['name']})")

        # 所在板块内有市场总龙头：+3分
        if market_leader.get('code') and any(
            concept in [s['name'] for s in mainline_data['strong_sectors'][:3]]
            for concept in stock_concepts
        ):
            score += 3
            reasons.append("龙头板块")

        # === 资金强度与预期差评分 ===
        # 这里简化处理，实际应该结合资金流数据
        if boards >= 2:  # 连板股通常有资金关注
            score += 3
            reasons.append("连板资金")

        # === 价格行为评分 ===
        # 简化处理，实际应该结合分时数据
        if boards == 1:  # 首板更有想象空间
            score += 2
            reasons.append("首板启动")

        # === 【V8.0 新增】资金攻击榜评分 ===
        if rising_sectors is not None and not rising_sectors.empty:
            # 获取资金攻击榜前三名板块
            rising_sectors_list = rising_sectors.head(3)['名称'].tolist()
            is_in_accelerating_theme = any(concept in rising_sectors_list for concept in stock_concepts)

            if is_in_accelerating_theme:
                score += 8  # 给予8分重奖，代表这是在分歧转一致的临界点介入
                reasons.append("新兴主线+资金加速")

        # 判断是否达到买入阈值
        threshold = sentiment_data.get('score_threshold', 12)
        is_qualified = score >= threshold

        return {
            'score': score,
            'reasons': reasons,
            'is_qualified': is_qualified
        }

    except Exception as e:
        logging.error(f"股票评分计算失败: {e}")
        return {'score': 0, 'reasons': ['评分失败'], 'is_qualified': False}

def _is_sector_leader(stock_code, stock_concepts, stock_boards):
    """
    判断是否为板块龙头（在所属板块内是否为最高板）
    """
    try:
        # 简化实现：如果是3板以上，认为是板块龙头
        return stock_boards >= 3
    except:
        return False

def generate_buy_signals_v7(mainline_data, sentiment_data, rising_sectors=None, today_zt_pool_df=None, reseal_df=None):
    """
    第三心法：先手预判，买在分歧 (信号生成)
    基于四大心法生成高质量买入信号

    Args:
        mainline_data: dict, 主线战场数据
        sentiment_data: dict, 市场情绪数据
        rising_sectors: DataFrame, 资金攻击榜数据 (V8.0新增)
        today_zt_pool_df: DataFrame, 今日涨停股池数据（可选，避免重复获取）
        reseal_df: DataFrame, 烂板回封数据（V8.0新增）

    Returns:
        list: 买入信号列表
    """
    try:
        print("\n� 第三心法：龙头战法信号生成...")

        # 检查总开关：冰点期不生成任何买入信号
        if sentiment_data['status'] == '冰点期':
            print("🧊 市场处于冰点期，总开关关闭，不生成买入信号")
            return []

        # 获取涨停池数据作为候选股票（如果没有传入，则获取）
        if today_zt_pool_df is None:
            from datetime import datetime

            # 获取当前交易日期
            if is_trading_day():
                today_str = datetime.now().strftime("%Y%m%d")
            else:
                today_str = get_last_trading_day()

            zt_pool_df = get_zt_pool_with_backup(today_str)
        else:
            zt_pool_df = today_zt_pool_df

        if zt_pool_df is None or zt_pool_df.empty:
            print("❌ 无法获取涨停池数据")
            return []

        buy_signals = []
        qualified_stocks = []

        # 1. 【最高优先级】处理烂板回封信号
        if reseal_df is not None and not reseal_df.empty:
            print("\n  🎯 处理高优先级 [烂板回封] 信号...")
            for _, row in reseal_df.head(3).iterrows(): # 取强度分最高的前3名
                signal = f"{row['代码']},{row['名称']},分歧转一致,{row['回封强度分']}分-{row['原因']}"
                buy_signals.append(signal)
                print(f"    🔥  {signal}")

        # 2. 处理原有的龙头战法评分 (可以增加逻辑避免重复添加)
        existing_codes = {s.split(',')[0] for s in buy_signals}

        # 对每只涨停股进行评分
        for _, row in zt_pool_df.iterrows():
            stock_info = {
                'code': str(row['代码']).zfill(6),
                'name': row['名称'],
                'boards': row.get('连板数', 1),
                'industry': row.get('所属行业', ''),
                'turnover_rate': row.get('换手率', 0),
                'seal_fund': row.get('封单资金', 0)
            }

            # 如果已经是回封信号，则不再重复添加
            if stock_info['code'] in existing_codes:
                continue

            # 计算评分
            score_result = _calculate_stock_score_v7(stock_info, mainline_data, sentiment_data, rising_sectors)

            if score_result['is_qualified']:
                qualified_stocks.append({
                    'stock_info': stock_info,
                    'score_result': score_result
                })

        # 按评分排序
        qualified_stocks.sort(key=lambda x: x['score_result']['score'], reverse=True)

        # 生成买入信号
        for item in qualified_stocks[:10]:  # 最多选择前10只
            stock_info = item['stock_info']
            score_result = item['score_result']

            signal = f"{stock_info['code']},{stock_info['name']},龙头战法,{score_result['score']}分-{'+'.join(score_result['reasons'])}"
            buy_signals.append(signal)

        if buy_signals:
            print(f"✅ 生成 {len(buy_signals)} 个龙头战法买入信号:")
            for i, signal in enumerate(buy_signals):
                print(f"   {i+1}. {signal}")
        else:
            print("� 暂无符合龙头战法条件的买入信号")

        return buy_signals

    except Exception as e:
        logging.error(f"买入信号生成失败: {e}")
        print(f"❌ 买入信号生成失败: {e}")
        return []

def generate_catch_up_signals(mainline_data, sentiment_data):
    """
    【V8.0 乘胜追击版】生成主线内部的补涨/加仓信号
    心法："强者恒强，后排补涨"。当主线得到市场反复确认后，
    如果龙头一字板或封单巨大无法买入，资金就会去挖掘板块内形态好、有辨识度的后排个股
    """
    print("\n" + "🚀" * 15 + " 捕捉主线补涨机会 " + "🚀" * 15)

    # 核心心法：只在情绪稳定、主线明确时发动追击
    if sentiment_data['status'] not in ['强势期', '分化期']:
        print("  市场情绪不稳，不考虑补涨机会。")
        return []

    if not mainline_data['strong_sectors']:
        print("  主线板块未确立，无法寻找补涨目标。")
        return []

    # 心法二：10:30后市场方向更明确，是发动攻击的好时机
    current_time = datetime.now().time()
    target_time = datetime.strptime('10:30', '%H:%M').time()
    if current_time < target_time:
        print("  未到10:30，盘面分歧较大，等待方向明确。")
        return []

    try:
        # 获取最强的1-2个主线板块
        top_sectors = [s['name'] for s in mainline_data['strong_sectors'][:2]]
        print(f"  🎯 锁定最强主线: {', '.join(top_sectors)}")

        # 获取全市场实时行情数据
        all_quotes = get_current_stock_quotes()
        if all_quotes is None or all_quotes.empty:
            print("  ❌ 无法获取实时行情数据")
            return []

        # 筛选候选股
        candidates = []
        for _, row in all_quotes.iterrows():
            stock_code = str(row['代码']).zfill(6)
            change_pct = row.get('涨跌幅', 0)
            volume_ratio = row.get('量比', 0)

            # 条件1: 涨幅在 +5% 到 +9.8% 之间 (即将冲击涨停)
            if not (5 < change_pct < 9.8):
                continue

            # 条件2: 属于最强主线板块
            stock_concepts = get_stock_concepts(stock_code)
            if not any(concept in top_sectors for concept in stock_concepts):
                continue

            # 条件3: 量比放大 (量在价先)
            if volume_ratio < 2.0:
                continue

            candidates.append({
                'code': stock_code,
                'name': row.get('名称', ''),
                'change_pct': change_pct,
                'volume_ratio': volume_ratio,
                'concepts': stock_concepts
            })

        if not candidates:
            print("  暂未发现符合条件的补涨候选股。")
            return []

        print(f"  ✅ 发现 {len(candidates)} 只补涨候选股，进行评分排序...")

        # 此处可以加入更复杂的评分，例如结合分时图的攻击形态等
        # 简化处理：按涨幅排序，最高的作为信号
        candidates.sort(key=lambda x: x['change_pct'], reverse=True)

        best_candidate = candidates[0]
        matching_concept = next((concept for concept in best_candidate['concepts'] if concept in top_sectors), best_candidate['concepts'][0] if best_candidate['concepts'] else '未知')
        reason = f"主线补涨,{matching_concept},涨幅{best_candidate['change_pct']:.1f}%,量比{best_candidate['volume_ratio']:.1f}"
        signal = f"{best_candidate['code']},{best_candidate['name']},{reason}"

        print(f"  🔥 生成补涨信号: {signal}")
        return [signal]

    except Exception as e:
        logging.error(f"生成补涨信号失败: {e}")
        print(f"❌ 生成补涨信号失败: {e}")
        return []

def check_data_source_status():
    """
    第四心法：数据为基，应对为上 (系统健壮性)
    检查各数据源状态，确保数据获取的稳定性
    """
    global DATA_SOURCE_STATUS

    try:
        print("\n🔧 第四心法：数据源状态检查...")

        # 检查akshare状态
        try:
            if ak is not None:
                test_df = ak.tool_trade_date_hist_sina()
                DATA_SOURCE_STATUS['akshare'] = test_df is not None and not test_df.empty
            else:
                DATA_SOURCE_STATUS['akshare'] = False
        except:
            DATA_SOURCE_STATUS['akshare'] = False

        # 检查adata状态（如果已安装）
        try:
            import adata
            test_df = adata.stock.info.all_code()
            DATA_SOURCE_STATUS['adata'] = test_df is not None and not test_df.empty
        except:
            DATA_SOURCE_STATUS['adata'] = False

        # 检查TPDOG状态
        try:
            response = requests.get('https://www.tpdog.com/api/hs/stocks/list?type=sz', timeout=5)
            DATA_SOURCE_STATUS['tpdog'] = response.status_code == 200
        except:
            DATA_SOURCE_STATUS['tpdog'] = False

        DATA_SOURCE_STATUS['last_check_time'] = datetime.now()

        # 输出状态
        print(f"📊 数据源状态:")
        print(f"   akshare: {'✅' if DATA_SOURCE_STATUS['akshare'] else '❌'}")
        print(f"   adata: {'✅' if DATA_SOURCE_STATUS['adata'] else '❌'}")
        print(f"   TPDOG: {'✅' if DATA_SOURCE_STATUS['tpdog'] else '❌'}")

        # 如果所有数据源都不可用，发出警告
        if not any(DATA_SOURCE_STATUS[key] for key in ['akshare', 'adata', 'tpdog']):
            print("⚠️ 警告：所有数据源都不可用，系统将使用缓存数据运行")
            logging.warning("所有数据源都不可用")

        return DATA_SOURCE_STATUS

    except Exception as e:
        logging.error(f"数据源状态检查失败: {e}")
        print(f"❌ 数据源状态检查失败: {e}")
        return DATA_SOURCE_STATUS

def run_continuous_task(task_name, job_func):
    """
    【新增】连续任务执行器，任务完成后立即触发下一个任务
    """
    import threading
    global TASK_RUNNING, LAST_TASK_TIMES

    def wrapped_job():
        global TASK_RUNNING
        try:
            print(f"🔄 开始执行任务: {task_name}")
            start_time = datetime.now()

            job_func()

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            LAST_TASK_TIMES[task_name] = end_time

            print(f"✅ 任务完成: {task_name} (耗时 {duration:.1f}秒)")

        except Exception as e:
            logging.error(f"连续任务执行异常 [{task_name}]: {e}", exc_info=True)
            print(f"❌ 连续任务执行异常 [{task_name}]: {e}")
            # 确保任务状态标志被正确重置
            global is_market_scan_running
            if task_name in ['four_methods', 'traditional_scan']:
                is_market_scan_running = False
        finally:
            TASK_RUNNING = False
            # 【关键】任务完成后立即触发下一个任务
            trigger_next_continuous_task()

    if not TASK_RUNNING:
        TASK_RUNNING = True
        job_thread = threading.Thread(target=wrapped_job)
        job_thread.daemon = True
        job_thread.start()
    else:
        print(f"⏳ 任务 {task_name} 跳过，其他任务正在执行中")

def run_threaded(job_func):
    """
    【保留】传统线程执行器，用于定时任务
    """
    import threading

    def wrapped_job():
        try:
            job_func()
        except Exception as e:
            logging.error(f"后台任务执行异常: {e}", exc_info=True)
            print(f"❌ 后台任务执行异常: {e}")

    job_thread = threading.Thread(target=wrapped_job)
    job_thread.daemon = True
    job_thread.start()

def trigger_next_continuous_task():
    """
    【修复】触发下一个连续任务
    循环轮转执行所有任务，无时间间隔限制
    """
    global CURRENT_TASK_INDEX

    # 【修复】使用全局交易状态，避免重复判断
    # 如果开启了交易时间限制，则检查交易时间
    if ENABLE_TRADING_TIME_CHECK:
        trading_day, trading_time = get_global_trading_status()
        if not (trading_day and trading_time):
            current_time = datetime.now().strftime('%H:%M:%S')
            print(f"⏰ 非交易时间（当前时间: {current_time}），跳过连续任务执行")
            return

    # 【修改】任务配置列表 - 按执行顺序排列
    task_configs = [
        {
            'name': 'position_scan',
            'func': task_scan_positions_for_sell_signals,
        },
        {
            'name': 'four_methods',
            'func': task_scan_market_with_four_methods_v7,
        },
        {
            'name': 'mainline_analysis',
            'func': task_mainline_analysis,
        },
        {
            'name': 'limit_up_review',
            'func': task_limit_up_performance_review,
        },
        {
            'name': 'traditional_scan',
            'func': task_scan_market_for_signals,
        }
    ]

    # 【修复】循环轮转执行任务
    if task_configs:
        current_task = task_configs[CURRENT_TASK_INDEX]
        task_name = current_task['name']
        task_func = current_task['func']

        print(f"⚡ 触发下一个任务: {task_name} (第{CURRENT_TASK_INDEX + 1}/{len(task_configs)}个)")

        # 更新任务索引，实现循环轮转
        CURRENT_TASK_INDEX = (CURRENT_TASK_INDEX + 1) % len(task_configs)

        run_continuous_task(task_name, task_func)

# task_scan_market_for_signals 函数已移至 signal_generator.py 模块

def task_scan_market_with_four_methods_v7():
    """
    【V7.0 四大心法完整版】主循环任务
    严格遵循四大心法的完整交易系统：
    1. 周期为王，情绪为相 (总开关)
    2. 龙头不死，主线不灭 (主战场)
    3. 先手预判，买在分歧 (信号生成)
    4. 数据为基，应对为上 (系统健壮性)
    """
    global is_market_scan_running

    # === 前置检查 ===
    if is_market_scan_running:
        logging.warning("市场扫描任务仍在运行中，跳过本次调度。")
        return

    is_market_scan_running = True
    task_start_time = datetime.now()
    logging.info(f"--- [四大心法V7.0任务开始] @ {task_start_time.strftime('%H:%M:%S')} ---")

    try:
        print("\n" + "="*80)
        print("🎯 【四大心法交易系统 V7.0】启动")
        print("="*80)

        # === 【修复】统一获取涨停股池数据，避免重复调用 ===
        print("\n📊 获取涨停股池数据...")
        if is_trading_day():
            today_str = datetime.now().strftime("%Y%m%d")
            yesterday_str = get_last_trading_day()
        else:
            today_str = get_last_trading_day()
            yesterday_str = get_last_trading_day(today_str)

        # 获取今日和昨日涨停股池数据（一次性获取，避免重复调用）
        today_zt_pool_df = get_zt_pool_with_backup(today_str)
        yesterday_zt_pool_df = get_zt_pool_with_backup(yesterday_str)

        # === 第四心法：数据基石检查 ===
        print("\n� 第四心法：数据基石稳固检查...")
        data_status = check_data_source_status()

        # === 第一心法：市场情绪总开关 ===
        print("\n🎯 第一心法：市场情绪总开关评估...")
        sentiment_data = assess_market_sentiment_v7(yesterday_zt_pool_df, today_zt_pool_df, yesterday_str, today_str)

        # === 【V8.0 新增】盘中资金流突变监控 ===
        print("\n" + "⚡"*15 + " 盘中资金流突变监控 " + "⚡"*15)
        rising_sectors, fading_sectors = analyze_intraday_flow_shift()

        # 【新增】保存资金流突变数据
        if rising_sectors is not None and not rising_sectors.empty:
            save_raw_data(rising_sectors, "rising_sectors", "csv")
        if fading_sectors is not None and not fading_sectors.empty:
            save_raw_data(fading_sectors, "fading_sectors", "csv")

        # === 第二心法：主线战场识别 (观潮法V4驱动) ===
        print("\n👑 第二心法：主线战场识别 (观潮法V4驱动)...")

        # 调用高级主线分析模块
        concept_themes = analyze_concept_main_themes()
        industry_themes = analyze_industry_main_themes()

        # 合并并去重，形成最终的主线战场列表
        strong_sectors_list = list(set(concept_themes + industry_themes))

        # 将这个精准的主线列表更新到全局变量 MAINLINE_BATTLEFIELD_DATA
        MAINLINE_BATTLEFIELD_DATA['strong_sectors'] = [{'name': s, 'count': 0} for s in strong_sectors_list]
        MAINLINE_BATTLEFIELD_DATA['last_update_time'] = datetime.now()

        # 构造兼容的mainline_data结构，保持后续函数调用不变
        mainline_data = {
            'market_leader': MAINLINE_BATTLEFIELD_DATA['market_leader'],
            'strong_sectors': MAINLINE_BATTLEFIELD_DATA['strong_sectors'],
            'limit_up_ladder': MAINLINE_BATTLEFIELD_DATA['limit_up_ladder']
        }

        print(f"📊 观潮法V4识别的主线战场({len(strong_sectors_list)}个):")
        for i, sector in enumerate(strong_sectors_list[:5]):  # 显示前5个
            print(f"     {i+1}. {sector}")

        # 【保留】原有的涨停梯队分析，用于市场总龙头识别
        if today_zt_pool_df is not None and not today_zt_pool_df.empty:
            limit_up_ladder = _analyze_limit_up_ladder(today_zt_pool_df)
            market_leader = _identify_market_leader(today_zt_pool_df)

            # 更新mainline_data中的龙头和梯队信息
            mainline_data['market_leader'] = market_leader
            mainline_data['limit_up_ladder'] = limit_up_ladder

            # 更新全局数据
            MAINLINE_BATTLEFIELD_DATA['market_leader'] = market_leader
            MAINLINE_BATTLEFIELD_DATA['limit_up_ladder'] = limit_up_ladder

            if market_leader['code']:
                print(f"   市场总龙头: {market_leader['name']}({market_leader['code']}) - {market_leader['boards']}板")
            else:
                print(f"   市场总龙头: 无明确龙头")

        if rising_sectors is not None and not rising_sectors.empty:
            display_rising = rising_sectors[['名称', 'current_rank', 'rank_change', 'flow_increment', 'shift_score']].head(5)
            display_rising.columns = ['板块名称', '当前排名', '排名变化', '5分钟净流入(万)', '突变分']
            # 【修复】flow_increment经过单位一致性处理后已经是万元单位，不需要再除以10000
            display_rising['5分钟净流入(万)'] = display_rising['5分钟净流入(万)'].round(2)

            print("\n🔥🔥🔥【资金攻击榜 TOP 5】🔥🔥🔥")
            print(tabulate(display_rising, headers='keys', tablefmt='psql', showindex=False, floatfmt=".2f"))

        if fading_sectors is not None and not fading_sectors.empty:
            display_fading = fading_sectors[['名称', 'current_rank', 'rank_change', 'flow_increment']].head(3)
            display_fading.columns = ['板块名称', '当前排名', '排名变化', '5分钟净流入(万)']
            # 【修复】同样不需要除以10000
            display_fading['5分钟净流入(万)'] = display_fading['5分钟净流入(万)'].round(2)

            print("\n🧊🧊🧊【资金流出榜 TOP 3】🧊🧊🧊")
            print(tabulate(display_fading, headers='keys', tablefmt='psql', showindex=False))

        # === 【V8.0 新增】基于资金流突变动态修正市场情绪 ===
        sentiment_data = _adjust_sentiment_with_flow_shift(sentiment_data, rising_sectors)

        # === 【新增】烂板回封狙击模块 ===
        reseal_df = analyze_reseal_opportunities()
        if reseal_df is not None and not reseal_df.empty:
            print("\n🔥🔥🔥【分歧转一致监控 (烂板回封)】🔥🔥🔥")
            print(tabulate(reseal_df, headers='keys', tablefmt='psql', showindex=False, floatfmt=".2f"))
            # 【新增】保存烂板回封数据
            save_raw_data(reseal_df, "reseal_opportunities", "csv")

        # === 第三心法：龙头战法信号生成 ===
        print("\n💡 第三心法：龙头战法信号生成...")
        # 注意：将 reseal_df 传递给信号生成函数
        buy_signals = generate_buy_signals_v7(mainline_data, sentiment_data, rising_sectors, today_zt_pool_df, reseal_df)

        # === 【V8.0 新增】生成主线补涨信号 ===
        catch_up_signals = generate_catch_up_signals(mainline_data, sentiment_data)
        if catch_up_signals:
            buy_signals.extend(catch_up_signals)

        # === 写入信号文件 ===
        if buy_signals:
            # 去重处理
            unique_signals = sorted(list(set(buy_signals)))
            write_signal_file_atomically(NOTIFICATION_FILE, unique_signals)
            print(f"✅ 成功写入 {len(unique_signals)} 个买入信号到 {NOTIFICATION_FILE}")

            # 【新增】保存买入信号数据
            signals_data = {
                'signals': unique_signals,
                'signal_count': len(unique_signals),
                'generation_method': '四大心法V7.0',
                'market_status': sentiment_data.get('status', '未知'),
                'position_ratio': sentiment_data.get('position_ratio', 0),
                'score_threshold': sentiment_data.get('score_threshold', 12)
            }
            save_analysis_report(signals_data, "buy_signals")
        else:
            print("📝 暂无符合四大心法条件的买入信号")
            # 【新增】保存空信号记录
            empty_signals_data = {
                'signals': [],
                'signal_count': 0,
                'generation_method': '四大心法V7.0',
                'market_status': sentiment_data.get('status', '未知'),
                'reason': '暂无符合四大心法条件的买入信号'
            }
            save_analysis_report(empty_signals_data, "buy_signals")

        # 【新增】保存完整的四大心法分析报告
        complete_report = {
            'sentiment_data': sentiment_data,
            'mainline_data': mainline_data,
            'data_source_status': data_status,
            'buy_signals': buy_signals if buy_signals else [],
            'analysis_timestamp': datetime.now().isoformat(),
            'analysis_method': '四大心法V7.0完整版'
        }
        save_analysis_report(complete_report, "four_methods_complete_analysis")

        print("\n✅ 四大心法V7.0扫描任务完成")

    except Exception as e:
        logging.error(f"四大心法V7.0扫描任务异常: {e}", exc_info=True)
        print(f"❌ 四大心法V7.0扫描任务异常: {e}")
    finally:
        is_market_scan_running = False
        task_end_time = datetime.now()
        duration = (task_end_time - task_start_time).total_seconds()
        logging.info(f"--- [四大心法V7.0任务结束] 耗时 {duration:.1f}秒 ---")

# 删除旧的增强版买入信号函数，已被 generate_buy_signals_v7 替代

def generate_buy_signals(sentiment_result, limit_up_result):
    """
    根据分析结果生成买入信号
    """
    try:
        signals = []
        
        # 基于龙头信息生成信号
        if limit_up_result.get('leader_info'):
            leader = limit_up_result['leader_info']
            if leader.get('boards', 0) >= 2:  # 至少2板
                signal = f"{leader['code']},{leader['name']},龙头信号,{leader['boards']}板"
                signals.append(signal)
        
        # 基于热门板块生成信号
        hot_sectors = limit_up_result.get('hot_sectors', [])
        for sector in hot_sectors[:3]:  # 前3个热门板块
            if sector['count'] >= 3:  # 至少3只涨停
                # 选择板块内的代表股票
                for stock_info in sector['stocks'][:1]:  # 每个板块选1只
                    if '(' in stock_info and ')' in stock_info:
                        stock_name = stock_info.split('(')[0]
                        stock_code = stock_info.split('(')[1].replace(')', '')
                        signal = f"{stock_code},{stock_name},板块信号,{sector['name']}"
                        signals.append(signal)
        
        # 写入信号文件
        if signals:
            write_signal_file_atomically(NOTIFICATION_FILE, signals)
            print(f"📝 生成 {len(signals)} 个买入信号")
            for signal in signals:
                print(f"   {signal}")
        else:
            print("📝 暂无符合条件的买入信号")
            
    except Exception as e:
        logging.error(f"生成买入信号失败: {e}")
        print(f"❌ 生成买入信号失败: {e}")

def task_scan_positions_for_sell_signals():
    """
    【卖出信号任务】扫描持仓生成卖出信号
    """
    try:
        print("\n🔍 扫描持仓生成卖出信号...")
        
        # 这里可以添加持仓扫描逻辑
        # 暂时跳过，因为需要持仓数据
        
        print("✅ 卖出信号扫描完成")
        
    except Exception as e:
        logging.error(f"卖出信号扫描失败: {e}")
        print(f"❌ 卖出信号扫描失败: {e}")

def task_yesterday_limit_up_review():
    """
    【新增】昨日涨停股复盘及晋级分析任务
    每日开盘前执行，为当日交易决策提供关键情报支持
    """
    try:
        print("\n" + "🎯" * 20)
        print("🎯 启动昨日涨停股复盘及晋级分析...")
        print("🎯" * 20)

        # 导入必要的函数
        from datetime import datetime, timedelta
        from market_data_provider import is_trading_day, get_last_trading_day

        # 【修复】非交易日处理逻辑
        current_date = datetime.now()
        current_date_str = current_date.strftime("%Y%m%d")

        # 如果当前是非交易日，获取最近的两个交易日进行复盘
        if not is_trading_day():
            print(f"📅 当前日期 {current_date_str} 为非交易日，获取最近交易日数据进行复盘...")
            today_str = get_last_trading_day()  # 最近的交易日作为"今日"
            yesterday_str = get_last_trading_day(today_str)  # 再往前一个交易日作为"昨日"
            print(f"📅 使用交易日: 昨日={yesterday_str}, 今日={today_str}")
        else:
            # 交易日正常处理
            today = current_date
            yesterday = today - timedelta(days=1)

            # 如果是周一，昨日应该是上周五
            if today.weekday() == 0:  # 周一
                yesterday = today - timedelta(days=3)

            yesterday_str = yesterday.strftime("%Y%m%d")
            today_str = today.strftime("%Y%m%d")
            print(f"📅 交易日正常处理: 昨日={yesterday_str}, 今日={today_str}")

        # 执行复盘分析
        review_result = analyze_yesterday_limit_up_performance(yesterday_str, today_str)

        if review_result:
            print("✅ 昨日涨停股复盘分析完成")

            # 输出关键指标摘要
            if 'market_sentiment' in review_result:
                metrics = review_result['market_sentiment']
                print(f"\n📊 关键指标摘要:")
                print(f"   晋级成功率: {metrics.get('success_rate', 0):.1f}%")
                print(f"   核按钮率: {metrics.get('nuclear_rate', 0):.1f}%")
                print(f"   平均溢价: {metrics.get('avg_premium', 0):+.1f}%")
                print(f"   溢价分化度: {metrics.get('premium_std', 0):.1f}")

                # 根据指标给出操作建议
                success_rate = metrics.get('success_rate', 0)
                nuclear_rate = metrics.get('nuclear_rate', 0)

                if nuclear_rate > 20:
                    print("⚠️ 操作建议: 核按钮率过高，建议谨慎操作")
                elif success_rate > 60:
                    print("🚀 操作建议: 晋级成功率高，市场情绪良好，可积极参与")
                elif success_rate > 40:
                    print("⚖️ 操作建议: 晋级成功率中等，聚焦强势个股")
                else:
                    print("🧊 操作建议: 晋级成功率低，建议观望")

            # 【新增】保存昨日涨停股复盘数据
            save_analysis_report(review_result, "yesterday_limit_up_review")
        else:
            print("❌ 昨日涨停股复盘分析失败")

    except Exception as e:
        logging.error(f"昨日涨停股复盘分析失败: {e}")
        print(f"❌ 昨日涨停股复盘分析失败: {e}")

def task_mainline_analysis():
    """
    【新增】专门的主线分析任务
    包含：概念主线识别系统、行业主线识别系统
    """
    try:
        print("\n" + "="*60)
        print("🎯 【主线识别系统】")
        print("="*60)

        # 1. 概念主线分析
        print("\n📊 概念主线识别分析...")
        from theme_analyzer import analyze_concept_main_themes
        concept_themes = analyze_concept_main_themes()

        # 保存概念主线数据
        if concept_themes:
            concept_data = {
                'concept_themes': concept_themes,
                'analysis_type': '概念主线识别',
                'timestamp': datetime.now().isoformat()
            }
            save_analysis_report(concept_data, "concept_mainline_analysis")

        # 2. 行业主线分析
        print("\n🏭 行业主线识别分析...")
        from theme_analyzer import analyze_industry_main_themes
        industry_themes = analyze_industry_main_themes()

        # 保存行业主线数据
        if industry_themes:
            industry_data = {
                'industry_themes': industry_themes,
                'analysis_type': '行业主线识别',
                'timestamp': datetime.now().isoformat()
            }
            save_analysis_report(industry_data, "industry_mainline_analysis")

        # 3. 量化评分版主线分析
        print("\n📈 量化评分主线分析...")
        from theme_analyzer import analyze_main_themes_quantitative
        quantitative_results = analyze_main_themes_quantitative()

        # 保存量化主线数据
        if quantitative_results:
            quantitative_data = {
                'quantitative_results': quantitative_results,
                'analysis_type': '量化评分主线分析',
                'timestamp': datetime.now().isoformat()
            }
            save_analysis_report(quantitative_data, "quantitative_mainline_analysis")

        print("✅ 主线识别系统分析完成")

    except Exception as e:
        logging.error(f"主线识别系统分析失败: {e}")
        print(f"❌ 主线识别系统分析失败: {e}")

def task_limit_up_performance_review():
    """
    【新增】涨停股表现复盘任务（实时循环版）
    定期执行涨停股今日表现分析
    """
    try:
        print("\n" + "="*60)
        print("📊 【涨停股今日表现复盘】")
        print("="*60)

        # 获取今日日期
        today_str = datetime.now().strftime('%Y%m%d')
        yesterday_str = get_last_trading_day()  # 已经是字符串格式YYYYMMDD

        print(f"📅 复盘日期: {yesterday_str} → {today_str}")

        # 执行复盘分析
        review_result = analyze_yesterday_limit_up_performance(yesterday_str, today_str)

        if review_result:
            print("✅ 涨停股今日表现复盘完成")

            # 输出关键指标摘要
            if 'market_sentiment' in review_result:
                metrics = review_result['market_sentiment']
                print(f"\n📊 关键指标摘要:")
                print(f"   晋级成功率: {metrics.get('success_rate', 0):.1f}%")
                print(f"   核按钮率: {metrics.get('nuclear_rate', 0):.1f}%")
                print(f"   平均溢价: {metrics.get('avg_premium', 0):+.1f}%")

            # 保存复盘数据
            save_analysis_report(review_result, "limit_up_performance_review")
        else:
            print("❌ 涨停股今日表现复盘失败")

    except Exception as e:
        logging.error(f"涨停股今日表现复盘失败: {e}")
        print(f"❌ 涨停股今日表现复盘失败: {e}")

# === 连续任务调度配置 ===
TASK_QUEUE = []
TASK_RUNNING = False
CURRENT_TASK_INDEX = 0  # 【新增】当前任务索引，用于循环轮转
LAST_TASK_TIMES = {
    'four_methods': None,
    'traditional_scan': None,
    'position_scan': None,
    'mainline_analysis': None,
    'limit_up_review': None
}

def schedule_jobs():
    """
    【V7.0 连续任务版】设置连续执行的任务队列
    任务完成后立即进行下一个任务，实现类似实时的连续扫描
    """
    try:
        # 清除所有现有任务（不再使用schedule库的定时功能）
        schedule.clear()

        # 【保留】每日定时任务仍使用schedule
        schedule.every().day.at("09:20").do(run_threaded, task_yesterday_limit_up_review)
        schedule.every().day.at("15:05").do(run_threaded, task_yesterday_limit_up_review)

        print("✅ 连续任务调度设置完成:")
        print("   - 持仓扫描: 循环轮转执行（第1个任务）")
        print("   - 四大心法V7.0扫描: 循环轮转执行（第2个任务，包含资金加速度、炸板监控、烂板回封）")
        print("   - 主线识别系统: 循环轮转执行（第3个任务，概念主线+行业主线+量化评分）")
        print("   - 涨停股表现复盘: 循环轮转执行（第4个任务，实时复盘分析）")
        print("   - 传统市场扫描: 循环轮转执行（第5个任务，备用扫描）")
        print("   - 昨日涨停股复盘: 每日9:20和15:05（定时任务）")
        print("   ⚡ 任务执行模式: 循环轮转，无时间间隔，一个任务完成立即执行下一个任务")
        logging.info("连续任务调度设置完成：所有重要监控功能已纳入循环轮转任务")

    except Exception as e:
        logging.error(f"设置任务调度失败: {e}")
        print(f"❌ 设置任务调度失败: {e}")

def run_backtest():
    """
    回测模式主函数
    """
    try:
        print(f"🔄 开始回测模式，日期: {BACKTEST_DATE}")
        
        # 执行一次完整的市场扫描
        task_scan_market_for_signals()
        
        print("✅ 回测完成")
        
    except Exception as e:
        logging.error(f"回测执行失败: {e}")
        print(f"❌ 回测执行失败: {e}")

def main():
    """
    主程序入口
    """
    # 网络连接测试
    test_network_connection()
    
    logging.info(f"启动股票监控系统... 当前模式: {RUN_MODE}")
    logging.info(f"akshare 接口状态: {AKSHARE_AVAILABLE}")
    
    if RUN_MODE == 'BACKTEST':
        # 回测模式
        print(f"--- 进入回测模式，日期: {BACKTEST_DATE} ---")
        
        # 初始化概念缓存
        if not load_stock_concept_cache():
            print("❌ 回测模式需要概念缓存，请先在实时模式下运行一次")
            return
        
        run_backtest()
        
    else:
        # 实时模式
        print("--- 进入实时监控模式 ---")
        
        # 初始化概念缓存
        print("\n🔄 初始化概念板块缓存...")
        if not load_stock_concept_cache():
            print("缓存不存在或已过期，开始更新缓存...")
            update_stock_concept_cache()
        print("概念板块缓存初始化完成。\n")
        
        # 【修复】加载历史推送缓存 - 处理纯文本格式的信号文件
        try:
            if os.path.exists(NOTIFICATION_FILE) and os.path.getsize(NOTIFICATION_FILE) > 0:
                # 读取纯文本格式的信号文件
                with open(NOTIFICATION_FILE, 'r', encoding='utf-8') as f:
                    lines = [line.strip() for line in f.readlines() if line.strip()]

                if lines:
                    print(f"📚 加载 {len(lines)} 条历史推送信号到缓存")
                    for line in lines:
                        try:
                            # 解析信号格式：股票代码,信号类型,描述,其他信息
                            parts = line.split(',')
                            if len(parts) >= 2:
                                stock_code = parts[0].strip().zfill(6)
                                signal_type = parts[1].strip()
                                cache_key = (stock_code, signal_type)
                                TODAY_NOTIFICATION_CACHE[cache_key] = {'signal': line}
                        except Exception as parse_e:
                            logging.warning(f"解析信号行失败: {line}, 错误: {parse_e}")
        except Exception as e:
            logging.error(f"加载推送历史失败: {e}")
        
        # 设置定时任务
        schedule_jobs()
        
        # 【修复】首次启动立即执行连续任务 - 检查交易时间限制配置
        if ENABLE_TRADING_TIME_CHECK:
            trading_day, trading_time = get_global_trading_status()
            if trading_day and trading_time:
                print("🚀 首次启动，启动连续任务调度...")
                # 立即触发第一个任务
                trigger_next_continuous_task()
            else:
                current_time = datetime.now().strftime('%H:%M:%S')
                print(f"⏰ 非交易时间（当前时间: {current_time}），等待交易时间开始连续任务")
        else:
            print("🚀 首次启动，启动连续任务调度（交易时间限制已关闭）...")
            # 立即触发第一个任务
            trigger_next_continuous_task()

        # 启动调度循环
        print("🔄 启动连续任务调度器...")
        while True:
            # 执行schedule库的定时任务（如复盘分析）
            schedule.run_pending()

            # 【修复】在交易时间内，如果没有任务在运行，触发连续任务 - 检查交易时间限制配置
            if ENABLE_TRADING_TIME_CHECK:
                trading_day, trading_time = get_global_trading_status()
                if (trading_day and trading_time) and not TASK_RUNNING:
                    trigger_next_continuous_task()
            else:
                # 交易时间限制关闭，直接执行任务
                if not TASK_RUNNING:
                    trigger_next_continuous_task()

            # 短暂休眠，避免CPU占用过高
            time_module.sleep(1)

if __name__ == "__main__":
    main()
